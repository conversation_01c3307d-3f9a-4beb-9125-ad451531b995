import{m as e}from"./index-DH0CLqAK.js";const n={getMerchants(t={}){return e.get("/merchants",{params:t})},getMerchant(t){return e.get(`/merchants/${t}`)},createMerchant(t){return e.post("/merchants",t)},updateMerchant(t,r){return e.put(`/merchants/${t}`,r)},deleteMerchant(t){return e.delete(`/merchants/${t}`)},toggleMerchantStatus(t,r){return e.patch(`/merchants/${t}/status`,{status:r})},getMerchantStats(t){return e.get(`/merchants/${t}/stats`)},getMerchantBalance(t){return e.get(`/merchants/${t}/balance`)},rechargeMerchant(t,r){return e.post(`/merchants/${t}/recharge`,{amount:r})}};export{n as m};
