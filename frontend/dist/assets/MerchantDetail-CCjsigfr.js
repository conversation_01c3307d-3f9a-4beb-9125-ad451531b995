import{_ as ee,r as f,a as N,n as te,c as ae,o as w,g as c,A as le,b as t,w as e,d as r,p as oe,q as T,D as ne,E as k,e as se,k as l,h as re,F as ue,s as S,t as s}from"./index-DH0CLqAK.js";import{m as x}from"./merchant-bXI_UmqM.js";import{t as de}from"./transaction-DS2_5LsC.js";const ie={class:"merchant-detail"},ce={class:"page-header"},me={class:"card-header"},pe={class:"card-header"},_e={class:"balance-amount"},fe={class:"card-header"},ve={class:"balance-amount"},be={__name:"MerchantDetail",setup(ge){const z=ne(),C=se(),V=f(!1),M=f(!1),v=f(!1),g=f(),u=f(null),p=N({todayTransactions:0,todayAmount:0,totalTransactions:0,totalAmount:0,totalFee:0}),F=f([]),i=N({type:"increase",amount:0,remark:""}),U={type:[{required:!0,message:"请选择调整类型",trigger:"change"}],amount:[{required:!0,message:"请输入调整金额",trigger:"blur"},{type:"number",min:.01,message:"金额必须大于0",trigger:"blur"}],remark:[{required:!0,message:"请输入调整原因",trigger:"blur"}]},B=async()=>{V.value=!0;try{const n=z.params.id,a=await x.getMerchant(n);u.value=a.data;const h=await x.getMerchantStats(n);Object.assign(p,h.data);const m=await de.getTransactions({merchantId:n,page:1,limit:10,sort:"createdAt",order:"desc"});F.value=m.data.items}catch{k.error("获取商户详情失败"),C.go(-1)}finally{V.value=!1}},$=()=>{k.info("编辑功能待实现")},E=async()=>{if(g.value)try{await g.value.validate(),M.value=!0;const n=i.type==="increase"?i.amount*100:-i.amount*100;await x.adjustMerchantBalance(u.value.id,{amount:n,remark:i.remark}),k.success("余额调整成功"),v.value=!1,L(),B()}catch{k.error("余额调整失败")}finally{M.value=!1}},L=()=>{var n;Object.assign(i,{type:"increase",amount:0,remark:""}),(n=g.value)==null||n.resetFields()},O=()=>{C.push({path:"/transactions",query:{merchantId:u.value.id}})},_=n=>(n/100).toFixed(2),R=n=>new Date(n).toLocaleString("zh-CN"),G=n=>({pending:"warning",success:"success",failed:"danger",cancelled:"info"})[n]||"info",H=n=>({pending:"处理中",success:"成功",failed:"失败",cancelled:"已取消"})[n]||n;return te(()=>{B()}),(n,a)=>{const h=r("el-icon"),m=r("el-button"),d=r("el-descriptions-item"),A=r("el-tag"),j=r("el-descriptions"),D=r("el-card"),q=r("el-col"),J=r("el-row"),b=r("el-table-column"),K=r("el-table"),y=r("el-form-item"),I=r("el-radio"),P=r("el-radio-group"),Q=r("el-input-number"),W=r("el-input"),X=r("el-form"),Y=r("el-dialog"),Z=oe("loading");return w(),ae("div",ie,[c("div",ce,[t(m,{onClick:a[0]||(a[0]=o=>n.$router.go(-1)),style:{"margin-right":"16px"}},{default:e(()=>[t(h,null,{default:e(()=>[t(re(ue))]),_:1}),a[7]||(a[7]=l(" 返回 "))]),_:1,__:[7]}),a[8]||(a[8]=c("h1",null,"商户详情",-1))]),le((w(),T(J,{gutter:20},{default:e(()=>[t(q,{span:12},{default:e(()=>[t(D,{class:"info-card"},{header:e(()=>[c("div",me,[a[10]||(a[10]=c("span",null,"基本信息",-1)),t(m,{type:"primary",size:"small",onClick:$},{default:e(()=>a[9]||(a[9]=[l("编辑")])),_:1,__:[9]})])]),default:e(()=>[u.value?(w(),T(j,{key:0,column:1,border:""},{default:e(()=>[t(d,{label:"商户ID"},{default:e(()=>[l(s(u.value.id),1)]),_:1}),t(d,{label:"商户名称"},{default:e(()=>[l(s(u.value.name),1)]),_:1}),t(d,{label:"用户名"},{default:e(()=>[l(s(u.value.username),1)]),_:1}),t(d,{label:"邮箱"},{default:e(()=>[l(s(u.value.email),1)]),_:1}),t(d,{label:"联系电话"},{default:e(()=>[l(s(u.value.phone),1)]),_:1}),t(d,{label:"费率"},{default:e(()=>[l(s(u.value.feeRate)+"%",1)]),_:1}),t(d,{label:"状态"},{default:e(()=>[t(A,{type:u.value.status==="active"?"success":"danger"},{default:e(()=>[l(s(u.value.status==="active"?"启用":"禁用"),1)]),_:1},8,["type"])]),_:1}),t(d,{label:"注册时间"},{default:e(()=>[l(s(R(u.value.createdAt)),1)]),_:1})]),_:1})):S("",!0)]),_:1})]),_:1}),t(q,{span:12},{default:e(()=>[t(D,{class:"info-card"},{header:e(()=>[c("div",pe,[a[12]||(a[12]=c("span",null,"账户信息",-1)),t(m,{type:"primary",size:"small",onClick:a[1]||(a[1]=o=>v.value=!0)},{default:e(()=>a[11]||(a[11]=[l(" 调整余额 ")])),_:1,__:[11]})])]),default:e(()=>[u.value?(w(),T(j,{key:0,column:1,border:""},{default:e(()=>[t(d,{label:"账户余额"},{default:e(()=>[c("span",_e,"¥"+s(_(u.value.balance)),1)]),_:1}),t(d,{label:"今日交易"},{default:e(()=>[l(s(p.todayTransactions)+"笔",1)]),_:1}),t(d,{label:"今日交易额"},{default:e(()=>[l("¥"+s(_(p.todayAmount)),1)]),_:1}),t(d,{label:"总交易笔数"},{default:e(()=>[l(s(p.totalTransactions)+"笔",1)]),_:1}),t(d,{label:"总交易金额"},{default:e(()=>[l("¥"+s(_(p.totalAmount)),1)]),_:1}),t(d,{label:"总手续费"},{default:e(()=>[l("¥"+s(_(p.totalFee)),1)]),_:1})]),_:1})):S("",!0)]),_:1})]),_:1})]),_:1})),[[Z,V.value]]),t(D,{class:"transactions-card"},{header:e(()=>[c("div",fe,[a[14]||(a[14]=c("span",null,"最近交易",-1)),t(m,{type:"primary",size:"small",onClick:O},{default:e(()=>a[13]||(a[13]=[l(" 查看全部 ")])),_:1,__:[13]})])]),default:e(()=>[t(K,{data:F.value,style:{width:"100%"}},{default:e(()=>[t(b,{prop:"orderId",label:"订单号",width:"200"}),t(b,{prop:"amount",label:"金额",width:"120"},{default:e(({row:o})=>[l(" ¥"+s(_(o.amount)),1)]),_:1}),t(b,{prop:"type",label:"类型",width:"100"},{default:e(({row:o})=>[t(A,{type:o.type==="payment"?"success":"warning"},{default:e(()=>[l(s(o.type==="payment"?"收款":"代付"),1)]),_:2},1032,["type"])]),_:1}),t(b,{prop:"status",label:"状态",width:"100"},{default:e(({row:o})=>[t(A,{type:G(o.status)},{default:e(()=>[l(s(H(o.status)),1)]),_:2},1032,["type"])]),_:1}),t(b,{prop:"createdAt",label:"创建时间"},{default:e(({row:o})=>[l(s(R(o.createdAt)),1)]),_:1})]),_:1},8,["data"])]),_:1}),t(Y,{modelValue:v.value,"onUpdate:modelValue":a[6]||(a[6]=o=>v.value=o),title:"调整余额",width:"400px"},{footer:e(()=>[t(m,{onClick:a[5]||(a[5]=o=>v.value=!1)},{default:e(()=>a[17]||(a[17]=[l("取消")])),_:1,__:[17]}),t(m,{type:"primary",onClick:E,loading:M.value},{default:e(()=>a[18]||(a[18]=[l(" 确定 ")])),_:1,__:[18]},8,["loading"])]),default:e(()=>[t(X,{ref_key:"balanceFormRef",ref:g,model:i,rules:U,"label-width":"100px"},{default:e(()=>[t(y,{label:"当前余额"},{default:e(()=>{var o;return[c("span",ve,"¥"+s(_(((o=u.value)==null?void 0:o.balance)||0)),1)]}),_:1}),t(y,{label:"调整类型",prop:"type"},{default:e(()=>[t(P,{modelValue:i.type,"onUpdate:modelValue":a[2]||(a[2]=o=>i.type=o)},{default:e(()=>[t(I,{value:"increase"},{default:e(()=>a[15]||(a[15]=[l("增加")])),_:1,__:[15]}),t(I,{value:"decrease"},{default:e(()=>a[16]||(a[16]=[l("减少")])),_:1,__:[16]})]),_:1},8,["modelValue"])]),_:1}),t(y,{label:"调整金额",prop:"amount"},{default:e(()=>[t(Q,{modelValue:i.amount,"onUpdate:modelValue":a[3]||(a[3]=o=>i.amount=o),min:.01,precision:2,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(y,{label:"备注",prop:"remark"},{default:e(()=>[t(W,{modelValue:i.remark,"onUpdate:modelValue":a[4]||(a[4]=o=>i.remark=o),type:"textarea",rows:3,placeholder:"请输入调整原因"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Ve=ee(be,[["__scopeId","data-v-9cc06b90"]]);export{Ve as default};
