import{m as r}from"./index-DH0CLqAK.js";const a={getTransactions(t={}){return r.get("/transactions",{params:t})},getTransaction(t){return r.get(`/transactions/${t}`)},createPayment(t){return r.post("/transactions/payment",t)},queryPaymentStatus(t){return r.get(`/transactions/payment/${t}/status`)},getPaymentQrCode(t){return r.get(`/transactions/payment/${t}/qrcode`)},createPayout(t){return r.post("/transactions/payout",t)},queryPayoutStatus(t){return r.get(`/transactions/payout/${t}/status`)},createRecharge(t){return r.post("/transactions/recharge",t)},refreshPaymentQrCode(t){return r.post(`/transactions/payment/${t}/refresh-qrcode`)},getRechargeStats(){return r.get("/transactions/recharge/stats")},getTransactionStats(t={}){return r.get("/transactions/stats",{params:t})},exportTransactions(t={}){return r.get("/transactions/export",{params:t,responseType:"blob"})}};export{a as t};
