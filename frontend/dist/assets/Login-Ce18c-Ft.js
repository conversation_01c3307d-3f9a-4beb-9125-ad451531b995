import{_ as x,u as V,r as h,a as C,c as R,o as z,b as o,w as s,d as a,e as B,f as E,g as l,h as p,i as F,j as L,l as N,k as _,E as f}from"./index-DH0CLqAK.js";const S={class:"login-container"},U={class:"login-footer"},q={__name:"Login",setup(K){const c=B(),m=V(),i=h(),r=C({username:"",password:""}),w={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于 6 个字符",trigger:"blur"}]},u=async()=>{if(i.value)try{await i.value.validate(),await m.login(r),f.success("登录成功"),c.push("/dashboard")}catch(n){n.response&&f.error(n.response.data.message||"登录失败")}};return(n,e)=>{const g=a("el-input"),d=a("el-form-item"),v=a("el-button"),y=a("el-link"),b=a("el-form"),k=a("el-card");return z(),R("div",S,[o(k,{class:"login-card",shadow:"always"},{header:s(()=>e[3]||(e[3]=[l("div",{class:"card-header"},[l("h2",null,"支付宝多商户平台"),l("p",null,"欢迎登录")],-1)])),default:s(()=>[o(b,{ref_key:"loginFormRef",ref:i,model:r,rules:w,class:"login-form",onSubmit:E(u,["prevent"])},{default:s(()=>[o(d,{prop:"username"},{default:s(()=>[o(g,{modelValue:r.username,"onUpdate:modelValue":e[0]||(e[0]=t=>r.username=t),placeholder:"请输入用户名",size:"large","prefix-icon":p(F)},null,8,["modelValue","prefix-icon"])]),_:1}),o(d,{prop:"password"},{default:s(()=>[o(g,{modelValue:r.password,"onUpdate:modelValue":e[1]||(e[1]=t=>r.password=t),type:"password",placeholder:"请输入密码",size:"large","prefix-icon":p(N),"show-password":"",onKeyup:L(u,["enter"])},null,8,["modelValue","prefix-icon"])]),_:1}),o(d,null,{default:s(()=>[o(v,{type:"primary",size:"large",class:"login-button",loading:p(m).loading,onClick:u},{default:s(()=>e[4]||(e[4]=[_(" 登录 ")])),_:1,__:[4]},8,["loading"])]),_:1}),l("div",U,[e[6]||(e[6]=l("span",null,"还没有账号？",-1)),o(y,{type:"primary",onClick:e[2]||(e[2]=t=>n.$router.push("/register"))},{default:s(()=>e[5]||(e[5]=[_(" 立即注册 ")])),_:1,__:[5]})])]),_:1},8,["model"])]),_:1})])}}},$=x(q,[["__scopeId","data-v-3d196cf9"]]);export{$ as default};
