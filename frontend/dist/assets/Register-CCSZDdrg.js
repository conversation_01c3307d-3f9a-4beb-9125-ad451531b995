import{_ as y,u as h,r as N,a as P,c as k,o as x,b as r,w as a,d,e as z,g as u,h as R,k as p,E as g}from"./index-DH0CLqAK.js";const U={class:"register-container"},q={class:"register-footer"},C={__name:"Register",setup(E){const f=z(),m=h(),i=N(),s=P({username:"",email:"",password:"",confirmPassword:"",merchantName:"",phone:""}),_={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于 6 个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:(n,e,o)=>{e!==s.password?o(new Error("两次输入的密码不一致")):o()},trigger:"blur"}],merchantName:[{required:!0,message:"请输入商户名称",trigger:"blur"},{min:2,max:50,message:"商户名称长度在 2 到 50 个字符",trigger:"blur"}],phone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}]},w=async()=>{if(i.value)try{await i.value.validate();const{confirmPassword:n,...e}=s;await m.register(e),g.success("注册成功，请登录"),f.push("/login")}catch(n){n.response&&g.error(n.response.data.message||"注册失败")}};return(n,e)=>{const o=d("el-input"),t=d("el-form-item"),c=d("el-button"),b=d("el-link"),V=d("el-form"),v=d("el-card");return x(),k("div",U,[r(v,{class:"register-card",shadow:"always"},{header:a(()=>e[7]||(e[7]=[u("div",{class:"card-header"},[u("h2",null,"商户注册"),u("p",null,"创建您的商户账号")],-1)])),default:a(()=>[r(V,{ref_key:"registerFormRef",ref:i,model:s,rules:_,class:"register-form","label-width":"80px"},{default:a(()=>[r(t,{label:"用户名",prop:"username"},{default:a(()=>[r(o,{modelValue:s.username,"onUpdate:modelValue":e[0]||(e[0]=l=>s.username=l),placeholder:"请输入用户名",size:"large"},null,8,["modelValue"])]),_:1}),r(t,{label:"邮箱",prop:"email"},{default:a(()=>[r(o,{modelValue:s.email,"onUpdate:modelValue":e[1]||(e[1]=l=>s.email=l),placeholder:"请输入邮箱地址",size:"large"},null,8,["modelValue"])]),_:1}),r(t,{label:"密码",prop:"password"},{default:a(()=>[r(o,{modelValue:s.password,"onUpdate:modelValue":e[2]||(e[2]=l=>s.password=l),type:"password",placeholder:"请输入密码",size:"large","show-password":""},null,8,["modelValue"])]),_:1}),r(t,{label:"确认密码",prop:"confirmPassword"},{default:a(()=>[r(o,{modelValue:s.confirmPassword,"onUpdate:modelValue":e[3]||(e[3]=l=>s.confirmPassword=l),type:"password",placeholder:"请再次输入密码",size:"large","show-password":""},null,8,["modelValue"])]),_:1}),r(t,{label:"商户名称",prop:"merchantName"},{default:a(()=>[r(o,{modelValue:s.merchantName,"onUpdate:modelValue":e[4]||(e[4]=l=>s.merchantName=l),placeholder:"请输入商户名称",size:"large"},null,8,["modelValue"])]),_:1}),r(t,{label:"联系电话",prop:"phone"},{default:a(()=>[r(o,{modelValue:s.phone,"onUpdate:modelValue":e[5]||(e[5]=l=>s.phone=l),placeholder:"请输入联系电话",size:"large"},null,8,["modelValue"])]),_:1}),r(t,null,{default:a(()=>[r(c,{type:"primary",size:"large",class:"register-button",loading:R(m).loading,onClick:w},{default:a(()=>e[8]||(e[8]=[p(" 注册 ")])),_:1,__:[8]},8,["loading"])]),_:1}),u("div",q,[e[10]||(e[10]=u("span",null,"已有账号？",-1)),r(b,{type:"primary",onClick:e[6]||(e[6]=l=>n.$router.push("/login"))},{default:a(()=>e[9]||(e[9]=[p(" 立即登录 ")])),_:1,__:[9]})])]),_:1},8,["model"])]),_:1})])}}},$=y(C,[["__scopeId","data-v-d14827aa"]]);export{$ as default};
