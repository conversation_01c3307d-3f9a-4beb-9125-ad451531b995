import{_ as ee,r as v,a as R,n as te,c as ae,o as z,g as w,b as t,w as l,d as s,E as b,e as le,p as ne,k as u,h as oe,B as se,j as re,A as ue,q as D,t as C,s as ie,C as de}from"./index-DH0CLqAK.js";import{m as h}from"./merchant-bXI_UmqM.js";const me={class:"merchant-management"},pe={class:"page-header"},ce={class:"pagination-container"},ge={__name:"MerchantManagement",setup(fe){const F=le(),k=v(!1),M=v(!1),g=v(!1),m=v(null),V=v(),c=R({name:"",status:""}),U=v([]),r=R({page:1,limit:20,total:0}),o=R({username:"",email:"",password:"",name:"",phone:"",feeRate:0}),j={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}],name:[{required:!0,message:"请输入商户名称",trigger:"blur"}],phone:[{required:!0,message:"请输入联系电话",trigger:"blur"}],feeRate:[{required:!0,message:"请输入费率",trigger:"blur"}]},f=async()=>{k.value=!0;try{const n={page:r.page,limit:r.limit,...c},e=await h.getMerchants(n);U.value=e.data.items,r.total=e.data.total}catch{b.error("获取商户列表失败")}finally{k.value=!1}},x=()=>{r.page=1,f()},N=()=>{Object.assign(c,{name:"",status:""}),x()},A=n=>{F.push(`/merchants/${n}`)},E=n=>{m.value=n,Object.assign(o,{username:n.username,email:n.email,name:n.name,phone:n.phone,feeRate:n.feeRate}),g.value=!0},T=async n=>{const e=n.status==="active"?"禁用":"启用";try{await de.confirm(`确定要${e}该商户吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const y=n.status==="active"?"inactive":"active";await h.toggleMerchantStatus(n.id,y),b.success(`${e}成功`),f()}catch(y){y!=="cancel"&&b.error(`${e}失败`)}},O=async()=>{if(V.value)try{await V.value.validate(),M.value=!0,m.value?(await h.updateMerchant(m.value.id,o),b.success("更新成功")):(await h.createMerchant(o),b.success("创建成功")),g.value=!1,I(),f()}catch{b.error(m.value?"更新失败":"创建失败")}finally{M.value=!1}},I=()=>{var n;m.value=null,Object.assign(o,{username:"",email:"",password:"",name:"",phone:"",feeRate:0}),(n=V.value)==null||n.resetFields()},K=n=>{r.limit=n,r.page=1,f()},L=n=>{r.page=n,f()},P=n=>(n/100).toFixed(2),G=n=>new Date(n).toLocaleString("zh-CN");return te(()=>{f()}),(n,e)=>{const y=s("el-icon"),p=s("el-button"),_=s("el-input"),i=s("el-form-item"),S=s("el-option"),H=s("el-select"),$=s("el-form"),B=s("el-card"),d=s("el-table-column"),J=s("el-tag"),Q=s("el-table"),W=s("el-pagination"),X=s("el-input-number"),Y=s("el-dialog"),Z=ne("loading");return z(),ae("div",me,[w("div",pe,[e[14]||(e[14]=w("h1",null,"商户管理",-1)),t(p,{type:"primary",onClick:e[0]||(e[0]=a=>g.value=!0)},{default:l(()=>[t(y,null,{default:l(()=>[t(oe(se))]),_:1}),e[13]||(e[13]=u(" 新增商户 "))]),_:1,__:[13]})]),t(B,{class:"search-card"},{default:l(()=>[t($,{model:c,inline:""},{default:l(()=>[t(i,{label:"商户名称"},{default:l(()=>[t(_,{modelValue:c.name,"onUpdate:modelValue":e[1]||(e[1]=a=>c.name=a),placeholder:"请输入商户名称",clearable:"",onKeyup:re(x,["enter"])},null,8,["modelValue"])]),_:1}),t(i,{label:"状态"},{default:l(()=>[t(H,{modelValue:c.status,"onUpdate:modelValue":e[2]||(e[2]=a=>c.status=a),placeholder:"请选择状态",clearable:""},{default:l(()=>[t(S,{label:"启用",value:"active"}),t(S,{label:"禁用",value:"inactive"})]),_:1},8,["modelValue"])]),_:1}),t(i,null,{default:l(()=>[t(p,{type:"primary",onClick:x},{default:l(()=>e[15]||(e[15]=[u("搜索")])),_:1,__:[15]}),t(p,{onClick:N},{default:l(()=>e[16]||(e[16]=[u("重置")])),_:1,__:[16]})]),_:1})]),_:1},8,["model"])]),_:1}),t(B,{class:"table-card"},{default:l(()=>[ue((z(),D(Q,{data:U.value,style:{width:"100%"}},{default:l(()=>[t(d,{prop:"id",label:"ID",width:"80"}),t(d,{prop:"name",label:"商户名称",width:"200"}),t(d,{prop:"username",label:"用户名",width:"150"}),t(d,{prop:"email",label:"邮箱",width:"200"}),t(d,{prop:"phone",label:"联系电话",width:"150"}),t(d,{prop:"balance",label:"余额",width:"120"},{default:l(({row:a})=>[u(" ¥"+C(P(a.balance)),1)]),_:1}),t(d,{prop:"status",label:"状态",width:"100"},{default:l(({row:a})=>[t(J,{type:a.status==="active"?"success":"danger"},{default:l(()=>[u(C(a.status==="active"?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),t(d,{prop:"createdAt",label:"创建时间",width:"180"},{default:l(({row:a})=>[u(C(G(a.createdAt)),1)]),_:1}),t(d,{label:"操作",width:"200",fixed:"right"},{default:l(({row:a})=>[t(p,{size:"small",onClick:q=>A(a.id)},{default:l(()=>e[17]||(e[17]=[u("查看")])),_:2,__:[17]},1032,["onClick"]),t(p,{size:"small",type:"primary",onClick:q=>E(a)},{default:l(()=>e[18]||(e[18]=[u("编辑")])),_:2,__:[18]},1032,["onClick"]),t(p,{size:"small",type:a.status==="active"?"warning":"success",onClick:q=>T(a)},{default:l(()=>[u(C(a.status==="active"?"禁用":"启用"),1)]),_:2},1032,["type","onClick"])]),_:1})]),_:1},8,["data"])),[[Z,k.value]]),w("div",ce,[t(W,{"current-page":r.page,"onUpdate:currentPage":e[3]||(e[3]=a=>r.page=a),"page-size":r.limit,"onUpdate:pageSize":e[4]||(e[4]=a=>r.limit=a),"page-sizes":[10,20,50,100],total:r.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:K,onCurrentChange:L},null,8,["current-page","page-size","total"])])]),_:1}),t(Y,{modelValue:g.value,"onUpdate:modelValue":e[12]||(e[12]=a=>g.value=a),title:m.value?"编辑商户":"新增商户",width:"600px"},{footer:l(()=>[t(p,{onClick:e[11]||(e[11]=a=>g.value=!1)},{default:l(()=>e[20]||(e[20]=[u("取消")])),_:1,__:[20]}),t(p,{type:"primary",onClick:O,loading:M.value},{default:l(()=>e[21]||(e[21]=[u(" 确定 ")])),_:1,__:[21]},8,["loading"])]),default:l(()=>[t($,{ref_key:"merchantFormRef",ref:V,model:o,rules:j,"label-width":"100px"},{default:l(()=>[t(i,{label:"用户名",prop:"username"},{default:l(()=>[t(_,{modelValue:o.username,"onUpdate:modelValue":e[5]||(e[5]=a=>o.username=a),disabled:m.value},null,8,["modelValue","disabled"])]),_:1}),t(i,{label:"邮箱",prop:"email"},{default:l(()=>[t(_,{modelValue:o.email,"onUpdate:modelValue":e[6]||(e[6]=a=>o.email=a)},null,8,["modelValue"])]),_:1}),m.value?ie("",!0):(z(),D(i,{key:0,label:"密码",prop:"password"},{default:l(()=>[t(_,{modelValue:o.password,"onUpdate:modelValue":e[7]||(e[7]=a=>o.password=a),type:"password"},null,8,["modelValue"])]),_:1})),t(i,{label:"商户名称",prop:"name"},{default:l(()=>[t(_,{modelValue:o.name,"onUpdate:modelValue":e[8]||(e[8]=a=>o.name=a)},null,8,["modelValue"])]),_:1}),t(i,{label:"联系电话",prop:"phone"},{default:l(()=>[t(_,{modelValue:o.phone,"onUpdate:modelValue":e[9]||(e[9]=a=>o.phone=a)},null,8,["modelValue"])]),_:1}),t(i,{label:"费率",prop:"feeRate"},{default:l(()=>[t(X,{modelValue:o.feeRate,"onUpdate:modelValue":e[10]||(e[10]=a=>o.feeRate=a),min:0,max:100,precision:2,"controls-position":"right"},null,8,["modelValue"]),e[19]||(e[19]=w("span",{style:{"margin-left":"8px"}},"%",-1))]),_:1,__:[19]})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},be=ee(ge,[["__scopeId","data-v-e217be4d"]]);export{be as default};
