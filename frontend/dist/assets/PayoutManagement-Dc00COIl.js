import{_ as de,u as pe,r as g,a as U,n as ie,c as me,o as k,g as h,b as e,w as a,d as r,E as v,p as ce,k as n,h as E,B as fe,A as _e,q as x,s as F,t as u}from"./index-DH0CLqAK.js";import{t as N}from"./transaction-DS2_5LsC.js";const ge={class:"payout-management"},ye={class:"page-header"},be={class:"pagination-container"},ve={class:"form-tip"},Ve={__name:"PayoutManagement",setup(we){const O=pe(),D=g(!1),M=g(!1),V=g(!1),R=g(!1),s=g(null),A=g(),p=U({orderId:"",payeeAccount:"",status:"",dateRange:[]}),z=g([]),i=U({page:1,limit:20,total:0}),d=U({payeeAccount:"",payeeName:"",amount:0,remark:""}),L={payeeAccount:[{required:!0,message:"请输入收款账户",trigger:"blur"}],payeeName:[{required:!0,message:"请输入收款姓名",trigger:"blur"}],amount:[{required:!0,message:"请输入代付金额",trigger:"blur"},{type:"number",min:.01,message:"金额必须大于0",trigger:"blur"}]},P=()=>d.amount?Math.max(d.amount*.006,2).toFixed(2):"0.00",G=()=>{if(!d.amount)return"0.00";const o=parseFloat(P());return(d.amount+o).toFixed(2)},y=async()=>{D.value=!0;try{const o={page:i.page,limit:i.limit,type:"payout",...p};p.dateRange&&p.dateRange.length===2&&(o.startDate=p.dateRange[0],o.endDate=p.dateRange[1]),delete o.dateRange;const t=await N.getTransactions(o);z.value=t.data.items,i.total=t.data.total}catch{v.error("获取代付列表失败")}finally{D.value=!1}},I=()=>{i.page=1,y()},H=()=>{Object.assign(p,{orderId:"",payeeAccount:"",status:"",dateRange:[]}),I()},J=async o=>{try{const t=await N.getTransaction(o.id);s.value=t.data,R.value=!0}catch{v.error("获取代付详情失败")}},K=async o=>{try{await N.queryPayoutStatus(o.orderId),v.success("状态查询成功"),y()}catch{v.error("状态查询失败")}},Q=async()=>{if(A.value)try{await A.value.validate(),M.value=!0;const o={...d,amount:Math.round(d.amount*100)};await N.createPayout(o),v.success("代付发起成功"),V.value=!1,W(),y()}catch{v.error("代付发起失败")}finally{M.value=!1}},W=()=>{var o;Object.assign(d,{payeeAccount:"",payeeName:"",amount:0,remark:""}),(o=A.value)==null||o.resetFields()},X=o=>{i.limit=o,i.page=1,y()},Z=o=>{i.page=o,y()},b=o=>(o/100).toFixed(2),S=o=>new Date(o).toLocaleString("zh-CN"),T=o=>({pending:"warning",success:"success",failed:"danger",cancelled:"info"})[o]||"info",Y=o=>({pending:"处理中",success:"成功",failed:"失败",cancelled:"已取消"})[o]||o;return ie(()=>{y()}),(o,t)=>{const ee=r("el-icon"),_=r("el-button"),w=r("el-input"),f=r("el-form-item"),C=r("el-option"),te=r("el-select"),ae=r("el-date-picker"),q=r("el-form"),B=r("el-card"),c=r("el-table-column"),$=r("el-tag"),le=r("el-table"),oe=r("el-pagination"),ne=r("el-input-number"),j=r("el-dialog"),m=r("el-descriptions-item"),ue=r("el-descriptions"),re=ce("loading");return k(),me("div",ge,[h("div",ye,[t[15]||(t[15]=h("h1",null,"代付管理",-1)),e(_,{type:"primary",onClick:t[0]||(t[0]=l=>V.value=!0)},{default:a(()=>[e(ee,null,{default:a(()=>[e(E(fe))]),_:1}),t[14]||(t[14]=n(" 发起代付 "))]),_:1,__:[14]})]),e(B,{class:"search-card"},{default:a(()=>[e(q,{model:p,inline:""},{default:a(()=>[e(f,{label:"订单号"},{default:a(()=>[e(w,{modelValue:p.orderId,"onUpdate:modelValue":t[1]||(t[1]=l=>p.orderId=l),placeholder:"请输入订单号",clearable:""},null,8,["modelValue"])]),_:1}),e(f,{label:"收款账户"},{default:a(()=>[e(w,{modelValue:p.payeeAccount,"onUpdate:modelValue":t[2]||(t[2]=l=>p.payeeAccount=l),placeholder:"请输入收款账户",clearable:""},null,8,["modelValue"])]),_:1}),e(f,{label:"状态"},{default:a(()=>[e(te,{modelValue:p.status,"onUpdate:modelValue":t[3]||(t[3]=l=>p.status=l),placeholder:"请选择状态",clearable:""},{default:a(()=>[e(C,{label:"处理中",value:"pending"}),e(C,{label:"成功",value:"success"}),e(C,{label:"失败",value:"failed"}),e(C,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"时间范围"},{default:a(()=>[e(ae,{modelValue:p.dateRange,"onUpdate:modelValue":t[4]||(t[4]=l=>p.dateRange=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(f,null,{default:a(()=>[e(_,{type:"primary",onClick:I},{default:a(()=>t[16]||(t[16]=[n("搜索")])),_:1,__:[16]}),e(_,{onClick:H},{default:a(()=>t[17]||(t[17]=[n("重置")])),_:1,__:[17]})]),_:1})]),_:1},8,["model"])]),_:1}),e(B,{class:"table-card"},{default:a(()=>[_e((k(),x(le,{data:z.value,style:{width:"100%"}},{default:a(()=>[e(c,{prop:"orderId",label:"订单号",width:"200"}),E(O).isPlatformAdmin?(k(),x(c,{key:0,prop:"merchantName",label:"商户名称",width:"150"})):F("",!0),e(c,{prop:"amount",label:"代付金额",width:"120"},{default:a(({row:l})=>[n(" ¥"+u(b(l.amount)),1)]),_:1}),e(c,{prop:"fee",label:"手续费",width:"100"},{default:a(({row:l})=>[n(" ¥"+u(b(l.fee)),1)]),_:1}),e(c,{prop:"actualAmount",label:"实际金额",width:"120"},{default:a(({row:l})=>[n(" ¥"+u(b(l.actualAmount)),1)]),_:1}),e(c,{prop:"payeeAccount",label:"收款账户",width:"150"}),e(c,{prop:"payeeName",label:"收款姓名",width:"120"}),e(c,{prop:"status",label:"状态",width:"100"},{default:a(({row:l})=>[e($,{type:T(l.status)},{default:a(()=>[n(u(Y(l.status)),1)]),_:2},1032,["type"])]),_:1}),e(c,{prop:"createdAt",label:"创建时间",width:"180"},{default:a(({row:l})=>[n(u(S(l.createdAt)),1)]),_:1}),e(c,{label:"操作",width:"150",fixed:"right"},{default:a(({row:l})=>[e(_,{size:"small",onClick:se=>J(l)},{default:a(()=>t[18]||(t[18]=[n("查看详情")])),_:2,__:[18]},1032,["onClick"]),l.status==="pending"?(k(),x(_,{key:0,size:"small",type:"primary",onClick:se=>K(l)},{default:a(()=>t[19]||(t[19]=[n(" 查询状态 ")])),_:2,__:[19]},1032,["onClick"])):F("",!0)]),_:1})]),_:1},8,["data"])),[[re,D.value]]),h("div",be,[e(oe,{"current-page":i.page,"onUpdate:currentPage":t[5]||(t[5]=l=>i.page=l),"page-size":i.limit,"onUpdate:pageSize":t[6]||(t[6]=l=>i.limit=l),"page-sizes":[10,20,50,100],total:i.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:X,onCurrentChange:Z},null,8,["current-page","page-size","total"])])]),_:1}),e(j,{modelValue:V.value,"onUpdate:modelValue":t[12]||(t[12]=l=>V.value=l),title:"发起代付",width:"600px"},{footer:a(()=>[e(_,{onClick:t[11]||(t[11]=l=>V.value=!1)},{default:a(()=>t[20]||(t[20]=[n("取消")])),_:1,__:[20]}),e(_,{type:"primary",onClick:Q,loading:M.value},{default:a(()=>t[21]||(t[21]=[n(" 确定 ")])),_:1,__:[21]},8,["loading"])]),default:a(()=>[e(q,{ref_key:"payoutFormRef",ref:A,model:d,rules:L,"label-width":"120px"},{default:a(()=>[e(f,{label:"收款账户",prop:"payeeAccount"},{default:a(()=>[e(w,{modelValue:d.payeeAccount,"onUpdate:modelValue":t[7]||(t[7]=l=>d.payeeAccount=l),placeholder:"请输入支付宝账户"},null,8,["modelValue"])]),_:1}),e(f,{label:"收款姓名",prop:"payeeName"},{default:a(()=>[e(w,{modelValue:d.payeeName,"onUpdate:modelValue":t[8]||(t[8]=l=>d.payeeName=l),placeholder:"请输入收款人姓名"},null,8,["modelValue"])]),_:1}),e(f,{label:"代付金额",prop:"amount"},{default:a(()=>[e(ne,{modelValue:d.amount,"onUpdate:modelValue":t[9]||(t[9]=l=>d.amount=l),min:.01,precision:2,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"]),h("div",ve,"手续费: ¥"+u(P())+"，实际扣款: ¥"+u(G()),1)]),_:1}),e(f,{label:"备注",prop:"remark"},{default:a(()=>[e(w,{modelValue:d.remark,"onUpdate:modelValue":t[10]||(t[10]=l=>d.remark=l),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e(j,{modelValue:R.value,"onUpdate:modelValue":t[13]||(t[13]=l=>R.value=l),title:"代付详情",width:"600px"},{default:a(()=>[s.value?(k(),x(ue,{key:0,column:2,border:""},{default:a(()=>[e(m,{label:"订单号"},{default:a(()=>[n(u(s.value.orderId),1)]),_:1}),e(m,{label:"商户名称"},{default:a(()=>[n(u(s.value.merchantName),1)]),_:1}),e(m,{label:"代付金额"},{default:a(()=>[n("¥"+u(b(s.value.amount)),1)]),_:1}),e(m,{label:"手续费"},{default:a(()=>[n("¥"+u(b(s.value.fee)),1)]),_:1}),e(m,{label:"实际金额"},{default:a(()=>[n("¥"+u(b(s.value.actualAmount)),1)]),_:1}),e(m,{label:"状态"},{default:a(()=>[e($,{type:T(s.value.status)},{default:a(()=>[n(u(Y(s.value.status)),1)]),_:1},8,["type"])]),_:1}),e(m,{label:"收款账户"},{default:a(()=>[n(u(s.value.payeeAccount),1)]),_:1}),e(m,{label:"收款姓名"},{default:a(()=>[n(u(s.value.payeeName),1)]),_:1}),e(m,{label:"支付宝交易号"},{default:a(()=>[n(u(s.value.alipayTradeNo||"-"),1)]),_:1}),e(m,{label:"创建时间"},{default:a(()=>[n(u(S(s.value.createdAt)),1)]),_:1}),e(m,{label:"完成时间"},{default:a(()=>[n(u(s.value.completedAt?S(s.value.completedAt):"-"),1)]),_:1}),e(m,{label:"备注",span:2},{default:a(()=>[n(u(s.value.remark||"-"),1)]),_:1})]),_:1})):F("",!0)]),_:1},8,["modelValue"])])}}},Ce=de(Ve,[["__scopeId","data-v-e9b356be"]]);export{Ce as default};
