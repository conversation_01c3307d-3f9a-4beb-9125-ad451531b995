import{m as g,_ as ee,r as c,a as te,n as ae,c as I,o as k,g as F,b as a,w as n,d as s,E as r,p as le,k as o,h as ne,B as oe,A as se,q as M,t as m,s as re,C as B}from"./index-DH0CLqAK.js";const A={getApiKeys(){return g.get("/api-keys")},createApiKey(i){return g.post("/api-keys",i)},updateApiKey(i,_){return g.put(`/api-keys/${i}`,_)},deleteApiKey(i){return g.delete(`/api-keys/${i}`)},toggleApiKeyStatus(i,_){return g.patch(`/api-keys/${i}/status`,{status:_})},regenerateApiKey(i){return g.post(`/api-keys/${i}/regenerate`)}},ie={class:"api-key-management"},ue={class:"page-header"},pe={key:0},de={key:1},ce={__name:"ApiKeyManagement",setup(i){const _=c(!1),K=c(!1),y=c(!1),S=c(!1),v=c(null),u=c(null),C=c(),D=c([]),d=te({name:"",description:""}),N={name:[{required:!0,message:"请输入密钥名称",trigger:"blur"}]},b=async()=>{_.value=!0;try{const l=await A.getApiKeys();D.value=l.data.map(e=>({...e,showSecret:!1}))}catch{r.error("获取API密钥列表失败")}finally{_.value=!1}},h=l=>{v.value=l,Object.assign(d,{name:l.name,description:l.description}),y.value=!0},E=l=>{l.showSecret=!l.showSecret},q=l=>l?l.substring(0,8)+"****"+l.substring(l.length-8):"",R=async l=>{const e=l.status==="active"?"禁用":"启用";try{await B.confirm(`确定要${e}该API密钥吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const w=l.status==="active"?"inactive":"active";await A.toggleApiKeyStatus(l.id,w),r.success(`${e}成功`),b()}catch(w){w!=="cancel"&&r.error(`${e}失败`)}},j=async l=>{try{await B.confirm("重新生成密钥后，原密钥将失效，确定继续吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await A.regenerateApiKey(l.id);u.value=e.data,S.value=!0,r.success("密钥重新生成成功"),b()}catch(e){e!=="cancel"&&r.error("重新生成密钥失败")}},O=async l=>{try{await B.confirm("删除后无法恢复，确定要删除该API密钥吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await A.deleteApiKey(l.id),r.success("删除成功"),b()}catch(e){e!=="cancel"&&r.error("删除失败")}},L=async()=>{if(C.value)try{if(await C.value.validate(),K.value=!0,v.value)await A.updateApiKey(v.value.id,d),r.success("更新成功");else{const l=await A.createApiKey(d);u.value=l.data,S.value=!0,r.success("创建成功")}y.value=!1,G(),b()}catch{r.error(v.value?"更新失败":"创建失败")}finally{K.value=!1}},G=()=>{var l;v.value=null,Object.assign(d,{name:"",description:""}),(l=C.value)==null||l.resetFields()},T=async l=>{try{await navigator.clipboard.writeText(l),r.success("复制成功")}catch{r.error("复制失败")}},U=l=>new Date(l).toLocaleString("zh-CN");return ae(()=>{b()}),(l,e)=>{const w=s("el-icon"),p=s("el-button"),f=s("el-table-column"),H=s("el-tag"),J=s("el-table"),Q=s("el-card"),V=s("el-input"),z=s("el-form-item"),W=s("el-form"),P=s("el-dialog"),X=s("el-alert"),$=s("el-descriptions-item"),Y=s("el-descriptions"),Z=le("loading");return k(),I("div",ie,[F("div",ue,[e[11]||(e[11]=F("h1",null,"API密钥管理",-1)),a(p,{type:"primary",onClick:e[0]||(e[0]=t=>y.value=!0)},{default:n(()=>[a(w,null,{default:n(()=>[a(ne(oe))]),_:1}),e[10]||(e[10]=o(" 创建密钥 "))]),_:1,__:[10]})]),a(Q,{class:"table-card"},{default:n(()=>[se((k(),M(J,{data:D.value,style:{width:"100%"}},{default:n(()=>[a(f,{prop:"name",label:"密钥名称",width:"200"}),a(f,{prop:"appId",label:"App ID",width:"200"}),a(f,{prop:"appSecret",label:"App Secret",width:"300"},{default:n(({row:t})=>[t.showSecret?(k(),I("span",de,m(t.appSecret),1)):(k(),I("span",pe,m(q(t.appSecret)),1)),a(p,{size:"small",type:"text",onClick:x=>E(t),style:{"margin-left":"8px"}},{default:n(()=>[o(m(t.showSecret?"隐藏":"显示"),1)]),_:2},1032,["onClick"])]),_:1}),a(f,{prop:"status",label:"状态",width:"100"},{default:n(({row:t})=>[a(H,{type:t.status==="active"?"success":"danger"},{default:n(()=>[o(m(t.status==="active"?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),a(f,{prop:"lastUsedAt",label:"最后使用",width:"180"},{default:n(({row:t})=>[o(m(t.lastUsedAt?U(t.lastUsedAt):"从未使用"),1)]),_:1}),a(f,{prop:"createdAt",label:"创建时间",width:"180"},{default:n(({row:t})=>[o(m(U(t.createdAt)),1)]),_:1}),a(f,{label:"操作",width:"200",fixed:"right"},{default:n(({row:t})=>[a(p,{size:"small",onClick:x=>h(t)},{default:n(()=>e[12]||(e[12]=[o("编辑")])),_:2,__:[12]},1032,["onClick"]),a(p,{size:"small",type:"warning",onClick:x=>j(t)},{default:n(()=>e[13]||(e[13]=[o(" 重新生成 ")])),_:2,__:[13]},1032,["onClick"]),a(p,{size:"small",type:t.status==="active"?"warning":"success",onClick:x=>R(t)},{default:n(()=>[o(m(t.status==="active"?"禁用":"启用"),1)]),_:2},1032,["type","onClick"]),a(p,{size:"small",type:"danger",onClick:x=>O(t)},{default:n(()=>e[14]||(e[14]=[o(" 删除 ")])),_:2,__:[14]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Z,_.value]])]),_:1}),a(P,{modelValue:y.value,"onUpdate:modelValue":e[4]||(e[4]=t=>y.value=t),title:v.value?"编辑API密钥":"创建API密钥",width:"500px"},{footer:n(()=>[a(p,{onClick:e[3]||(e[3]=t=>y.value=!1)},{default:n(()=>e[15]||(e[15]=[o("取消")])),_:1,__:[15]}),a(p,{type:"primary",onClick:L,loading:K.value},{default:n(()=>e[16]||(e[16]=[o(" 确定 ")])),_:1,__:[16]},8,["loading"])]),default:n(()=>[a(W,{ref_key:"apiKeyFormRef",ref:C,model:d,rules:N,"label-width":"100px"},{default:n(()=>[a(z,{label:"密钥名称",prop:"name"},{default:n(()=>[a(V,{modelValue:d.name,"onUpdate:modelValue":e[1]||(e[1]=t=>d.name=t),placeholder:"请输入密钥名称"},null,8,["modelValue"])]),_:1}),a(z,{label:"描述",prop:"description"},{default:n(()=>[a(V,{modelValue:d.description,"onUpdate:modelValue":e[2]||(e[2]=t=>d.description=t),type:"textarea",rows:3,placeholder:"请输入密钥描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),a(P,{modelValue:S.value,"onUpdate:modelValue":e[9]||(e[9]=t=>S.value=t),title:"密钥详情",width:"600px"},{default:n(()=>[a(X,{title:"请妥善保管您的密钥信息",type:"warning",closable:!1,style:{"margin-bottom":"20px"}}),u.value?(k(),M(Y,{key:0,column:1,border:""},{default:n(()=>[a($,{label:"密钥名称"},{default:n(()=>[o(m(u.value.name),1)]),_:1}),a($,{label:"App ID"},{default:n(()=>[a(V,{modelValue:u.value.appId,"onUpdate:modelValue":e[6]||(e[6]=t=>u.value.appId=t),readonly:""},{append:n(()=>[a(p,{onClick:e[5]||(e[5]=t=>T(u.value.appId))},{default:n(()=>e[17]||(e[17]=[o("复制")])),_:1,__:[17]})]),_:1},8,["modelValue"])]),_:1}),a($,{label:"App Secret"},{default:n(()=>[a(V,{modelValue:u.value.appSecret,"onUpdate:modelValue":e[8]||(e[8]=t=>u.value.appSecret=t),readonly:""},{append:n(()=>[a(p,{onClick:e[7]||(e[7]=t=>T(u.value.appSecret))},{default:n(()=>e[18]||(e[18]=[o("复制")])),_:1,__:[18]})]),_:1},8,["modelValue"])]),_:1})]),_:1})):re("",!0)]),_:1},8,["modelValue"])])}}},_e=ee(ce,[["__scopeId","data-v-bc4ce613"]]);export{_e as default};
