import{_ as ae,u as le,r as w,a as j,n as ne,c as oe,o as h,g as T,b as e,w as t,d as u,E as g,p as se,k as o,h as U,G as q,A as de,q as D,s as M,t as d}from"./index-DH0CLqAK.js";import{t as k}from"./transaction-DS2_5LsC.js";const re={class:"transaction-list"},ue={class:"page-header"},ie={class:"pagination-container"},pe={__name:"TransactionList",setup(ce){const O=le(),S=w(!1),C=w(!1),A=w(!1),r=w(null),s=j({orderId:"",type:"",status:"",dateRange:[]}),z=w([]),p=j({page:1,limit:20,total:0}),y=async()=>{S.value=!0;try{const n={page:p.page,limit:p.limit,...s};s.dateRange&&s.dateRange.length===2&&(n.startDate=s.dateRange[0],n.endDate=s.dateRange[1]),delete n.dateRange;const a=await k.getTransactions(n);z.value=a.data.items,p.total=a.data.total}catch{g.error("获取交易列表失败")}finally{S.value=!1}},I=()=>{p.page=1,y()},P=()=>{Object.assign(s,{orderId:"",type:"",status:"",dateRange:[]}),I()},$=async n=>{try{const a=await k.getTransaction(n.id);r.value=a.data,A.value=!0}catch{g.error("获取交易详情失败")}},F=async n=>{try{let a;n.type==="payment"?a=await k.queryPaymentStatus(n.orderId):a=await k.queryPayoutStatus(n.orderId),g.success("状态查询成功"),y()}catch{g.error("状态查询失败")}},N=async()=>{try{C.value=!0;const n={...s};s.dateRange&&s.dateRange.length===2&&(n.startDate=s.dateRange[0],n.endDate=s.dateRange[1]),delete n.dateRange;const a=await k.exportTransactions(n),V=new Blob([a.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),m=window.URL.createObjectURL(V),_=document.createElement("a");_.href=m,_.download=`交易记录_${new Date().toISOString().slice(0,10)}.xlsx`,document.body.appendChild(_),_.click(),document.body.removeChild(_),window.URL.revokeObjectURL(m),g.success("导出成功")}catch{g.error("导出失败")}finally{C.value=!1}},G=n=>{p.limit=n,p.page=1,y()},H=n=>{p.page=n,y()},b=n=>(n/100).toFixed(2),R=n=>new Date(n).toLocaleString("zh-CN"),Y=n=>({pending:"warning",success:"success",failed:"danger",cancelled:"info"})[n]||"info",L=n=>({pending:"处理中",success:"成功",failed:"失败",cancelled:"已取消"})[n]||n;return ne(()=>{y()}),(n,a)=>{const V=u("el-icon"),m=u("el-button"),_=u("el-input"),v=u("el-form-item"),f=u("el-option"),B=u("el-select"),J=u("el-date-picker"),K=u("el-form"),E=u("el-card"),c=u("el-table-column"),x=u("el-tag"),Q=u("el-table"),W=u("el-pagination"),i=u("el-descriptions-item"),X=u("el-descriptions"),Z=u("el-dialog"),ee=se("loading");return h(),oe("div",re,[T("div",ue,[a[8]||(a[8]=T("h1",null,"交易记录",-1)),e(m,{type:"primary",onClick:N,loading:C.value},{default:t(()=>[e(V,null,{default:t(()=>[e(U(q))]),_:1}),a[7]||(a[7]=o(" 导出记录 "))]),_:1,__:[7]},8,["loading"])]),e(E,{class:"search-card"},{default:t(()=>[e(K,{model:s,inline:""},{default:t(()=>[e(v,{label:"订单号"},{default:t(()=>[e(_,{modelValue:s.orderId,"onUpdate:modelValue":a[0]||(a[0]=l=>s.orderId=l),placeholder:"请输入订单号",clearable:""},null,8,["modelValue"])]),_:1}),e(v,{label:"交易类型"},{default:t(()=>[e(B,{modelValue:s.type,"onUpdate:modelValue":a[1]||(a[1]=l=>s.type=l),placeholder:"请选择类型",clearable:""},{default:t(()=>[e(f,{label:"收款",value:"payment"}),e(f,{label:"代付",value:"payout"})]),_:1},8,["modelValue"])]),_:1}),e(v,{label:"状态"},{default:t(()=>[e(B,{modelValue:s.status,"onUpdate:modelValue":a[2]||(a[2]=l=>s.status=l),placeholder:"请选择状态",clearable:""},{default:t(()=>[e(f,{label:"处理中",value:"pending"}),e(f,{label:"成功",value:"success"}),e(f,{label:"失败",value:"failed"}),e(f,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue"])]),_:1}),e(v,{label:"时间范围"},{default:t(()=>[e(J,{modelValue:s.dateRange,"onUpdate:modelValue":a[3]||(a[3]=l=>s.dateRange=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(v,null,{default:t(()=>[e(m,{type:"primary",onClick:I},{default:t(()=>a[9]||(a[9]=[o("搜索")])),_:1,__:[9]}),e(m,{onClick:P},{default:t(()=>a[10]||(a[10]=[o("重置")])),_:1,__:[10]}),e(m,{type:"success",onClick:N,loading:C.value},{default:t(()=>[e(V,null,{default:t(()=>[e(U(q))]),_:1}),a[11]||(a[11]=o(" 导出数据 "))]),_:1,__:[11]},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1}),e(E,{class:"table-card"},{default:t(()=>[de((h(),D(Q,{data:z.value,style:{width:"100%"}},{default:t(()=>[e(c,{prop:"orderId",label:"订单号",width:"200"}),U(O).isPlatformAdmin?(h(),D(c,{key:0,prop:"merchantName",label:"商户名称",width:"150"})):M("",!0),e(c,{prop:"amount",label:"金额",width:"120"},{default:t(({row:l})=>[o(" ¥"+d(b(l.amount)),1)]),_:1}),e(c,{prop:"fee",label:"手续费",width:"100"},{default:t(({row:l})=>[o(" ¥"+d(b(l.fee)),1)]),_:1}),e(c,{prop:"type",label:"类型",width:"100"},{default:t(({row:l})=>[e(x,{type:l.type==="payment"?"success":"warning"},{default:t(()=>[o(d(l.type==="payment"?"收款":"代付"),1)]),_:2},1032,["type"])]),_:1}),e(c,{prop:"status",label:"状态",width:"100"},{default:t(({row:l})=>[e(x,{type:Y(l.status)},{default:t(()=>[o(d(L(l.status)),1)]),_:2},1032,["type"])]),_:1}),e(c,{prop:"payeeAccount",label:"收款账户",width:"150"}),e(c,{prop:"createdAt",label:"创建时间",width:"180"},{default:t(({row:l})=>[o(d(R(l.createdAt)),1)]),_:1}),e(c,{prop:"completedAt",label:"完成时间",width:"180"},{default:t(({row:l})=>[o(d(l.completedAt?R(l.completedAt):"-"),1)]),_:1}),e(c,{label:"操作",width:"150",fixed:"right"},{default:t(({row:l})=>[e(m,{size:"small",onClick:te=>$(l)},{default:t(()=>a[12]||(a[12]=[o("查看详情")])),_:2,__:[12]},1032,["onClick"]),l.status==="pending"?(h(),D(m,{key:0,size:"small",type:"primary",onClick:te=>F(l)},{default:t(()=>a[13]||(a[13]=[o(" 查询状态 ")])),_:2,__:[13]},1032,["onClick"])):M("",!0)]),_:1})]),_:1},8,["data"])),[[ee,S.value]]),T("div",ie,[e(W,{"current-page":p.page,"onUpdate:currentPage":a[4]||(a[4]=l=>p.page=l),"page-size":p.limit,"onUpdate:pageSize":a[5]||(a[5]=l=>p.limit=l),"page-sizes":[10,20,50,100],total:p.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:G,onCurrentChange:H},null,8,["current-page","page-size","total"])])]),_:1}),e(Z,{modelValue:A.value,"onUpdate:modelValue":a[6]||(a[6]=l=>A.value=l),title:"交易详情",width:"600px"},{default:t(()=>[r.value?(h(),D(X,{key:0,column:2,border:""},{default:t(()=>[e(i,{label:"订单号"},{default:t(()=>[o(d(r.value.orderId),1)]),_:1}),e(i,{label:"商户名称"},{default:t(()=>[o(d(r.value.merchantName),1)]),_:1}),e(i,{label:"交易金额"},{default:t(()=>[o("¥"+d(b(r.value.amount)),1)]),_:1}),e(i,{label:"手续费"},{default:t(()=>[o("¥"+d(b(r.value.fee)),1)]),_:1}),e(i,{label:"实际金额"},{default:t(()=>[o("¥"+d(b(r.value.actualAmount)),1)]),_:1}),e(i,{label:"交易类型"},{default:t(()=>[e(x,{type:r.value.type==="payment"?"success":"warning"},{default:t(()=>[o(d(r.value.type==="payment"?"收款":"代付"),1)]),_:1},8,["type"])]),_:1}),e(i,{label:"状态"},{default:t(()=>[e(x,{type:Y(r.value.status)},{default:t(()=>[o(d(L(r.value.status)),1)]),_:1},8,["type"])]),_:1}),e(i,{label:"收款账户"},{default:t(()=>[o(d(r.value.payeeAccount),1)]),_:1}),e(i,{label:"收款姓名"},{default:t(()=>[o(d(r.value.payeeName),1)]),_:1}),e(i,{label:"支付宝交易号"},{default:t(()=>[o(d(r.value.alipayTradeNo||"-"),1)]),_:1}),e(i,{label:"创建时间"},{default:t(()=>[o(d(R(r.value.createdAt)),1)]),_:1}),e(i,{label:"完成时间"},{default:t(()=>[o(d(r.value.completedAt?R(r.value.completedAt):"-"),1)]),_:1}),e(i,{label:"备注",span:2},{default:t(()=>[o(d(r.value.remark||"-"),1)]),_:1})]),_:1})):M("",!0)]),_:1},8,["modelValue"])])}}},fe=ae(pe,[["__scopeId","data-v-7a80cbf7"]]);export{fe as default};
