import{m as n,_ as N,u as V,r as w,a as B,n as P,c as j,o as b,g as s,b as e,t as l,h as _,w as a,d as i,E as q,p as I,q as M,s as O,v as U,x as L,y as Y,z as G,A as H,k as f}from"./index-DH0CLqAK.js";import{t as J}from"./transaction-DS2_5LsC.js";const K={getDashboardStats(){return n.get("/statistics/dashboard")},getTransactionStats(t={}){return n.get("/statistics/transactions",{params:t})},getRechargeStats(t={}){return n.get("/statistics/recharge",{params:t})},getPayoutStats(t={}){return n.get("/statistics/payouts",{params:t})},getMerchantStats(t={}){return n.get("/statistics/merchants",{params:t})},getRevenueStats(t={}){return n.get("/statistics/revenue",{params:t})},getTrendData(t={}){return n.get("/statistics/trends",{params:t})},getRealTimeStats(){return n.get("/statistics/realtime")},getMonthlyReport(t,d){return n.get(`/statistics/monthly-report/${t}/${d}`)},getYearlyReport(t){return n.get(`/statistics/yearly-report/${t}`)},getTopMerchants(t={}){return n.get("/statistics/top-merchants",{params:t})},getVolumeAnalysis(t={}){return n.get("/statistics/volume-analysis",{params:t})},getSuccessRateStats(t={}){return n.get("/statistics/success-rate",{params:t})},getFeeStats(t={}){return n.get("/statistics/fees",{params:t})},getUserActivityStats(t={}){return n.get("/statistics/user-activity",{params:t})},getRegionStats(t={}){return n.get("/statistics/regions",{params:t})},getDeviceStats(t={}){return n.get("/statistics/devices",{params:t})},getAnomalyStats(t={}){return n.get("/statistics/anomalies",{params:t})},exportReport(t,d={}){return n.get(`/statistics/export/${t}`,{params:d,responseType:"blob"})}},Q={class:"dashboard"},W={class:"dashboard-header"},X={class:"stats-content"},Z={class:"stats-icon"},tt={class:"stats-info"},st={class:"stats-value"},et={class:"stats-content"},at={class:"stats-icon"},ot={class:"stats-info"},nt={class:"stats-value"},rt={class:"stats-content"},ct={class:"stats-icon"},lt={class:"stats-info"},it={class:"stats-value"},dt={class:"stats-content"},ut={class:"stats-icon"},_t={class:"stats-info"},pt={class:"stats-value"},gt={class:"card-header"},ft={__name:"Dashboard",setup(t){const d=V(),y=w(!1),u=B({totalAmount:0,totalTransactions:0,totalMerchants:0,balance:0,todayTransactions:0,todayAmount:0,successRate:0,pendingPayouts:0}),S=w([]),D=async()=>{var r;try{const o=await K.getDashboardStats();Object.assign(u,o.data)}catch(o){console.error("获取统计数据失败:",o),q.error("获取统计数据失败"),Object.assign(u,{totalAmount:0,totalTransactions:0,totalMerchants:0,balance:((r=d.user)==null?void 0:r.balance)||0,todayTransactions:0,todayAmount:0,successRate:0,pendingPayouts:0})}},R=async()=>{y.value=!0;try{const r=await J.getTransactions({page:1,limit:10,sort:"createdAt",order:"desc"});S.value=r.data.items}catch(r){console.error("获取最近交易失败:",r)}finally{y.value=!1}},h=r=>(r/100).toFixed(2),x=r=>new Date(r).toLocaleString("zh-CN"),C=r=>({pending:"warning",success:"success",failed:"danger",cancelled:"info"})[r]||"info",z=r=>({pending:"处理中",success:"成功",failed:"失败",cancelled:"已取消"})[r]||r;return P(()=>{D(),R()}),(r,o)=>{var T;const m=i("el-icon"),p=i("el-card"),v=i("el-col"),k=i("el-row"),$=i("el-button"),g=i("el-table-column"),A=i("el-tag"),E=i("el-table"),F=I("loading");return b(),j("div",Q,[s("div",W,[o[1]||(o[1]=s("h1",null,"仪表盘",-1)),s("p",null,"欢迎回来，"+l((T=_(d).user)==null?void 0:T.username),1)]),e(k,{gutter:20,class:"stats-row"},{default:a(()=>[e(v,{span:6},{default:a(()=>[e(p,{class:"stats-card"},{default:a(()=>[s("div",X,[s("div",Z,[e(m,{color:"#409EFF",size:"40"},{default:a(()=>[e(_(U))]),_:1})]),s("div",tt,[s("div",st,"¥"+l(h(u.totalAmount)),1),o[2]||(o[2]=s("div",{class:"stats-label"},"总交易金额",-1))])])]),_:1})]),_:1}),e(v,{span:6},{default:a(()=>[e(p,{class:"stats-card"},{default:a(()=>[s("div",et,[s("div",at,[e(m,{color:"#67C23A",size:"40"},{default:a(()=>[e(_(L))]),_:1})]),s("div",ot,[s("div",nt,l(u.totalTransactions),1),o[3]||(o[3]=s("div",{class:"stats-label"},"总交易笔数",-1))])])]),_:1})]),_:1}),_(d).isPlatformAdmin?(b(),M(v,{key:0,span:6},{default:a(()=>[e(p,{class:"stats-card"},{default:a(()=>[s("div",rt,[s("div",ct,[e(m,{color:"#E6A23C",size:"40"},{default:a(()=>[e(_(Y))]),_:1})]),s("div",lt,[s("div",it,l(u.totalMerchants),1),o[4]||(o[4]=s("div",{class:"stats-label"},"商户总数",-1))])])]),_:1})]),_:1})):O("",!0),e(v,{span:6},{default:a(()=>[e(p,{class:"stats-card"},{default:a(()=>[s("div",dt,[s("div",ut,[e(m,{color:"#F56C6C",size:"40"},{default:a(()=>[e(_(G))]),_:1})]),s("div",_t,[s("div",pt,"¥"+l(h(u.balance)),1),o[5]||(o[5]=s("div",{class:"stats-label"},"账户余额",-1))])])]),_:1})]),_:1})]),_:1}),e(p,{class:"recent-transactions"},{header:a(()=>[s("div",gt,[o[7]||(o[7]=s("span",null,"最近交易",-1)),e($,{type:"primary",size:"small",onClick:o[0]||(o[0]=c=>r.$router.push("/transactions"))},{default:a(()=>o[6]||(o[6]=[f(" 查看全部 ")])),_:1,__:[6]})])]),default:a(()=>[H((b(),M(E,{data:S.value,style:{width:"100%"}},{default:a(()=>[e(g,{prop:"orderId",label:"订单号",width:"200"}),e(g,{prop:"amount",label:"金额",width:"120"},{default:a(({row:c})=>[f(" ¥"+l(h(c.amount)),1)]),_:1}),e(g,{prop:"type",label:"类型",width:"100"},{default:a(({row:c})=>[e(A,{type:c.type==="payment"?"success":"warning"},{default:a(()=>[f(l(c.type==="payment"?"收款":"代付"),1)]),_:2},1032,["type"])]),_:1}),e(g,{prop:"status",label:"状态",width:"100"},{default:a(({row:c})=>[e(A,{type:C(c.status)},{default:a(()=>[f(l(z(c.status)),1)]),_:2},1032,["type"])]),_:1}),e(g,{prop:"createdAt",label:"创建时间"},{default:a(({row:c})=>[f(l(x(c.createdAt)),1)]),_:1})]),_:1},8,["data"])),[[F,y.value]])]),_:1})])}}},yt=N(ft,[["__scopeId","data-v-1f80f656"]]);export{yt as default};
