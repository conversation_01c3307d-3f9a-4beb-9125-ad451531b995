import{_ as Ve,u as Re,r as i,a as $,n as Te,H as De,c as j,o as k,g as s,b as t,w as a,d as r,E as m,p as Se,k as n,h as ae,B as Ue,t as d,A as Ie,q as A,s as S}from"./index-DH0CLqAK.js";import{t as y}from"./transaction-DS2_5LsC.js";const Me={class:"recharge-management"},qe={class:"page-header"},Ae={class:"balance-item"},ze={class:"balance-value"},Ye={class:"balance-item"},Fe={class:"balance-value"},Ne={class:"balance-item"},Oe={class:"balance-value"},Pe={class:"pagination-container"},Be={key:0,class:"qr-container"},Qe={class:"qr-info"},$e={class:"qr-code"},je={class:"qr-tips"},Ee={key:0,class:"countdown"},He={class:"qr-actions"},Le={__name:"RechargeManagement",setup(Ge){const E=Re(),z=i(!1),Y=i(!1),F=i(!1),N=i(!1),T=i(!1),D=i(!1),O=i(!1),u=i(null),c=i(null),U=i(),I=i(0);let x=null;const H=i(0),L=i(0),p=$({orderId:"",status:"",dateRange:[]}),G=i([]),_=$({page:1,limit:20,total:0}),v=$({amount:0,remark:""}),le={amount:[{required:!0,message:"请输入充值金额",trigger:"blur"},{type:"number",min:.01,max:5e4,message:"充值金额范围为0.01-50000元",trigger:"blur"}]},w=async()=>{z.value=!0;try{const l={page:_.page,limit:_.limit,type:"recharge",...p};p.dateRange&&p.dateRange.length===2&&(l.startDate=p.dateRange[0],l.endDate=p.dateRange[1]),delete l.dateRange;const e=await y.getTransactions(l);G.value=e.data.items,_.total=e.data.total}catch{m.error("获取充值订单失败")}finally{z.value=!1}},oe=async()=>{try{const l=await y.getRechargeStats();H.value=l.data.todayAmount,L.value=l.data.totalAmount}catch(l){console.error("获取统计数据失败:",l)}},J=()=>{_.page=1,w()},ne=()=>{Object.assign(p,{orderId:"",status:"",dateRange:[]}),J()},se=async()=>{if(U.value)try{await U.value.validate(),Y.value=!0;const l={...v,amount:Math.round(v.amount*100)},e=await y.createRecharge(l);m.success("充值订单创建成功"),T.value=!1,pe(),w(),u.value=e.data,D.value=!0,P()}catch{m.error("创建充值订单失败")}finally{Y.value=!1}},re=async l=>{try{const e=await y.getPaymentQrCode(l.orderId);u.value={...l,qrCodeUrl:e.data.qrCodeUrl},D.value=!0,P()}catch{m.error("获取二维码失败")}},de=async l=>{try{await y.queryPaymentStatus(l.orderId),m.success("状态查询成功"),w()}catch{m.error("状态查询失败")}},ue=async l=>{try{const e=await y.getTransaction(l.id);c.value=e.data,O.value=!0}catch{m.error("获取订单详情失败")}},ie=async()=>{if(u.value){F.value=!0;try{const l=await y.refreshPaymentQrCode(u.value.orderId);u.value.qrCodeUrl=l.data.qrCodeUrl,u.value.expireTime=l.data.expireTime,m.success("二维码刷新成功"),P()}catch{m.error("刷新二维码失败")}finally{F.value=!1}}},ce=async()=>{if(u.value){N.value=!0;try{(await y.queryPaymentStatus(u.value.orderId)).data.status==="success"?(m.success("支付成功！"),D.value=!1,w(),E.fetchUserInfo()):m.info("订单尚未支付")}catch{m.error("查询支付状态失败")}finally{N.value=!1}}},P=()=>{var l;if(x&&clearInterval(x),(l=u.value)!=null&&l.expireTime){const e=new Date(u.value.expireTime).getTime(),M=()=>{const f=Date.now(),g=Math.max(0,Math.floor((e-f)/1e3));I.value=g,g<=0&&(clearInterval(x),m.warning("二维码已过期，请刷新二维码"))};M(),x=setInterval(M,1e3)}},pe=()=>{var l;Object.assign(v,{amount:0,remark:""}),(l=U.value)==null||l.resetFields()},me=l=>{_.limit=l,_.page=1,w()},_e=l=>{_.page=l,w()},V=l=>(l/100).toFixed(2),h=l=>new Date(l).toLocaleString("zh-CN"),K=l=>({pending:"warning",success:"success",failed:"danger",expired:"info"})[l]||"info",W=l=>({pending:"待支付",success:"支付成功",failed:"支付失败",expired:"已过期"})[l]||l;return Te(()=>{w(),oe()}),De(()=>{x&&clearInterval(x)}),(l,e)=>{const M=r("el-icon"),f=r("el-button"),g=r("el-card"),B=r("el-col"),fe=r("el-row"),X=r("el-input"),R=r("el-form-item"),q=r("el-option"),ve=r("el-select"),ge=r("el-date-picker"),Z=r("el-form"),C=r("el-table-column"),ee=r("el-tag"),be=r("el-table"),ye=r("el-pagination"),we=r("el-input-number"),Q=r("el-dialog"),he=r("el-image"),Ce=r("el-alert"),b=r("el-descriptions-item"),ke=r("el-descriptions"),xe=Se("loading");return k(),j("div",Me,[s("div",qe,[e[13]||(e[13]=s("h1",null,"充值管理",-1)),t(f,{type:"primary",onClick:e[0]||(e[0]=o=>T.value=!0)},{default:a(()=>[t(M,null,{default:a(()=>[t(ae(Ue))]),_:1}),e[12]||(e[12]=n(" 发起充值 "))]),_:1,__:[12]})]),t(fe,{gutter:20,class:"balance-info"},{default:a(()=>[t(B,{span:8},{default:a(()=>[t(g,{class:"balance-card"},{default:a(()=>{var o;return[s("div",Ae,[e[14]||(e[14]=s("div",{class:"balance-label"},"当前余额",-1)),s("div",ze,"¥"+d(V(((o=ae(E).user)==null?void 0:o.balance)||0)),1)])]}),_:1})]),_:1}),t(B,{span:8},{default:a(()=>[t(g,{class:"balance-card"},{default:a(()=>[s("div",Ye,[e[15]||(e[15]=s("div",{class:"balance-label"},"今日充值",-1)),s("div",Fe,"¥"+d(V(H.value)),1)])]),_:1})]),_:1}),t(B,{span:8},{default:a(()=>[t(g,{class:"balance-card"},{default:a(()=>[s("div",Ne,[e[16]||(e[16]=s("div",{class:"balance-label"},"总充值金额",-1)),s("div",Oe,"¥"+d(V(L.value)),1)])]),_:1})]),_:1})]),_:1}),t(g,{class:"search-card"},{default:a(()=>[t(Z,{model:p,inline:""},{default:a(()=>[t(R,{label:"订单号"},{default:a(()=>[t(X,{modelValue:p.orderId,"onUpdate:modelValue":e[1]||(e[1]=o=>p.orderId=o),placeholder:"请输入订单号",clearable:""},null,8,["modelValue"])]),_:1}),t(R,{label:"状态"},{default:a(()=>[t(ve,{modelValue:p.status,"onUpdate:modelValue":e[2]||(e[2]=o=>p.status=o),placeholder:"请选择状态",clearable:""},{default:a(()=>[t(q,{label:"待支付",value:"pending"}),t(q,{label:"支付成功",value:"success"}),t(q,{label:"支付失败",value:"failed"}),t(q,{label:"已过期",value:"expired"})]),_:1},8,["modelValue"])]),_:1}),t(R,{label:"时间范围"},{default:a(()=>[t(ge,{modelValue:p.dateRange,"onUpdate:modelValue":e[3]||(e[3]=o=>p.dateRange=o),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),t(R,null,{default:a(()=>[t(f,{type:"primary",onClick:J},{default:a(()=>e[17]||(e[17]=[n("搜索")])),_:1,__:[17]}),t(f,{onClick:ne},{default:a(()=>e[18]||(e[18]=[n("重置")])),_:1,__:[18]})]),_:1})]),_:1},8,["model"])]),_:1}),t(g,{class:"table-card"},{default:a(()=>[Ie((k(),A(be,{data:G.value,style:{width:"100%"}},{default:a(()=>[t(C,{prop:"orderId",label:"订单号",width:"200"}),t(C,{prop:"amount",label:"充值金额",width:"120"},{default:a(({row:o})=>[n(" ¥"+d(V(o.amount)),1)]),_:1}),t(C,{prop:"status",label:"状态",width:"100"},{default:a(({row:o})=>[t(ee,{type:K(o.status)},{default:a(()=>[n(d(W(o.status)),1)]),_:2},1032,["type"])]),_:1}),t(C,{prop:"createdAt",label:"创建时间",width:"180"},{default:a(({row:o})=>[n(d(h(o.createdAt)),1)]),_:1}),t(C,{prop:"expireTime",label:"过期时间",width:"180"},{default:a(({row:o})=>[n(d(o.expireTime?h(o.expireTime):"-"),1)]),_:1}),t(C,{prop:"completedAt",label:"完成时间",width:"180"},{default:a(({row:o})=>[n(d(o.completedAt?h(o.completedAt):"-"),1)]),_:1}),t(C,{label:"操作",width:"200",fixed:"right"},{default:a(({row:o})=>[o.status==="pending"?(k(),A(f,{key:0,size:"small",onClick:te=>re(o)},{default:a(()=>e[19]||(e[19]=[n(" 查看二维码 ")])),_:2,__:[19]},1032,["onClick"])):S("",!0),o.status==="pending"?(k(),A(f,{key:1,size:"small",type:"primary",onClick:te=>de(o)},{default:a(()=>e[20]||(e[20]=[n(" 查询状态 ")])),_:2,__:[20]},1032,["onClick"])):S("",!0),t(f,{size:"small",onClick:te=>ue(o)},{default:a(()=>e[21]||(e[21]=[n(" 查看详情 ")])),_:2,__:[21]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[xe,z.value]]),s("div",Pe,[t(ye,{"current-page":_.page,"onUpdate:currentPage":e[4]||(e[4]=o=>_.page=o),"page-size":_.limit,"onUpdate:pageSize":e[5]||(e[5]=o=>_.limit=o),"page-sizes":[10,20,50,100],total:_.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:me,onCurrentChange:_e},null,8,["current-page","page-size","total"])])]),_:1}),t(Q,{modelValue:T.value,"onUpdate:modelValue":e[9]||(e[9]=o=>T.value=o),title:"发起充值",width:"500px"},{footer:a(()=>[t(f,{onClick:e[8]||(e[8]=o=>T.value=!1)},{default:a(()=>e[23]||(e[23]=[n("取消")])),_:1,__:[23]}),t(f,{type:"primary",onClick:se,loading:Y.value},{default:a(()=>e[24]||(e[24]=[n(" 确定 ")])),_:1,__:[24]},8,["loading"])]),default:a(()=>[t(Z,{ref_key:"rechargeFormRef",ref:U,model:v,rules:le,"label-width":"100px"},{default:a(()=>[t(R,{label:"充值金额",prop:"amount"},{default:a(()=>[t(we,{modelValue:v.amount,"onUpdate:modelValue":e[6]||(e[6]=o=>v.amount=o),min:.01,max:5e4,precision:2,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"]),e[22]||(e[22]=s("div",{class:"form-tip"},"单笔充值金额范围：0.01 - 50,000.00 元",-1))]),_:1,__:[22]}),t(R,{label:"备注",prop:"remark"},{default:a(()=>[t(X,{modelValue:v.remark,"onUpdate:modelValue":e[7]||(e[7]=o=>v.remark=o),type:"textarea",rows:3,placeholder:"请输入备注信息（可选）"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(Q,{modelValue:D.value,"onUpdate:modelValue":e[10]||(e[10]=o=>D.value=o),title:"支付二维码",width:"400px"},{default:a(()=>[u.value?(k(),j("div",Be,[s("div",Qe,[s("p",null,[e[25]||(e[25]=s("strong",null,"订单号：",-1)),n(d(u.value.orderId),1)]),s("p",null,[e[26]||(e[26]=s("strong",null,"充值金额：",-1)),n("¥"+d(V(u.value.amount)),1)]),s("p",null,[e[27]||(e[27]=s("strong",null,"过期时间：",-1)),n(d(h(u.value.expireTime)),1)])]),s("div",$e,[t(he,{src:u.value.qrCodeUrl,alt:"支付二维码",style:{width:"200px",height:"200px"},fit:"contain"},null,8,["src"])]),s("div",je,[t(Ce,{title:"请使用支付宝扫码支付",type:"info",closable:!1,"show-icon":""}),I.value>0?(k(),j("div",Ee," 二维码将在 "+d(Math.floor(I.value/60))+":"+d(String(I.value%60).padStart(2,"0"))+" 后过期 ",1)):S("",!0)]),s("div",He,[t(f,{onClick:ie,loading:F.value},{default:a(()=>e[28]||(e[28]=[n("刷新二维码")])),_:1,__:[28]},8,["loading"]),t(f,{type:"primary",onClick:ce,loading:N.value},{default:a(()=>e[29]||(e[29]=[n(" 检查支付状态 ")])),_:1,__:[29]},8,["loading"])])])):S("",!0)]),_:1},8,["modelValue"]),t(Q,{modelValue:O.value,"onUpdate:modelValue":e[11]||(e[11]=o=>O.value=o),title:"充值详情",width:"600px"},{default:a(()=>[c.value?(k(),A(ke,{key:0,column:2,border:""},{default:a(()=>[t(b,{label:"订单号"},{default:a(()=>[n(d(c.value.orderId),1)]),_:1}),t(b,{label:"充值金额"},{default:a(()=>[n("¥"+d(V(c.value.amount)),1)]),_:1}),t(b,{label:"状态"},{default:a(()=>[t(ee,{type:K(c.value.status)},{default:a(()=>[n(d(W(c.value.status)),1)]),_:1},8,["type"])]),_:1}),t(b,{label:"支付宝交易号"},{default:a(()=>[n(d(c.value.alipayTradeNo||"-"),1)]),_:1}),t(b,{label:"创建时间"},{default:a(()=>[n(d(h(c.value.createdAt)),1)]),_:1}),t(b,{label:"过期时间"},{default:a(()=>[n(d(c.value.expireTime?h(c.value.expireTime):"-"),1)]),_:1}),t(b,{label:"完成时间"},{default:a(()=>[n(d(c.value.completedAt?h(c.value.completedAt):"-"),1)]),_:1}),t(b,{label:"备注",span:2},{default:a(()=>[n(d(c.value.remark||"-"),1)]),_:1})]),_:1})):S("",!0)]),_:1},8,["modelValue"])])}}},We=Ve(Le,[["__scopeId","data-v-786d3e8f"]]);export{We as default};
