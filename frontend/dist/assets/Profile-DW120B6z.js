import{_ as G,u as H,r as P,a as F,n as K,c as Q,o as b,g as _,b as r,w as e,d as m,J as M,q as v,s as V,k as s,t as p,h as n,E as k}from"./index-DH0CLqAK.js";const W={class:"profile"},X={class:"card-header"},Y={class:"balance-amount"},Z={__name:"Profile",setup(ee){const o=H(),h=P(),A=P(),N=P(!1),x=P(!1),y=P(!1),c=F({balance:0,feeRate:0,status:"active",todayTransactions:0,todayAmount:0}),d=F({currentPassword:"",newPassword:"",confirmPassword:""}),f=F({email:"",merchantName:"",phone:""}),D={currentPassword:[{required:!0,message:"请输入当前密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认新密码",trigger:"blur"},{validator:(t,a,u)=>{a!==d.newPassword?u(new Error("两次输入的密码不一致")):u()},trigger:"blur"}]},S={email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],merchantName:[{required:!0,message:"请输入商户名称",trigger:"blur"}],phone:[{required:!0,message:"请输入联系电话",trigger:"blur"}]},T=async()=>{if(o.isMerchantAdmin)try{const t=await M.getProfile();Object.assign(c,t.data.accountInfo)}catch(t){console.error("获取账户信息失败:",t)}},j=()=>{var t,a,u;Object.assign(f,{email:((t=o.user)==null?void 0:t.email)||"",merchantName:((a=o.user)==null?void 0:a.merchantName)||"",phone:((u=o.user)==null?void 0:u.phone)||""}),y.value=!0},L=async()=>{if(A.value)try{await A.value.validate(),x.value=!0,await M.updateProfile(f),await o.fetchUserInfo(),k.success("资料更新成功"),y.value=!1}catch{k.error("资料更新失败")}finally{x.value=!1}},O=async()=>{if(h.value)try{await h.value.validate(),N.value=!0,await M.changePassword({currentPassword:d.currentPassword,newPassword:d.newPassword}),k.success("密码修改成功"),U()}catch{k.error("密码修改失败")}finally{N.value=!1}},U=()=>{var t;Object.assign(d,{currentPassword:"",newPassword:"",confirmPassword:""}),(t=h.value)==null||t.resetFields()},R=t=>(t/100).toFixed(2),z=t=>new Date(t).toLocaleString("zh-CN");return K(()=>{T()}),(t,a)=>{const u=m("el-button"),i=m("el-descriptions-item"),q=m("el-tag"),E=m("el-descriptions"),C=m("el-card"),I=m("el-col"),J=m("el-row"),g=m("el-input"),w=m("el-form-item"),B=m("el-form"),$=m("el-dialog");return b(),Q("div",W,[a[16]||(a[16]=_("div",{class:"page-header"},[_("h1",null,"个人中心")],-1)),r(J,{gutter:20},{default:e(()=>[r(I,{span:12},{default:e(()=>[r(C,{class:"profile-card"},{header:e(()=>[_("div",X,[a[9]||(a[9]=_("span",null,"基本信息",-1)),r(u,{type:"primary",size:"small",onClick:j},{default:e(()=>a[8]||(a[8]=[s("编辑")])),_:1,__:[8]})])]),default:e(()=>[r(E,{column:1,border:""},{default:e(()=>[r(i,{label:"用户名"},{default:e(()=>{var l;return[s(p((l=n(o).user)==null?void 0:l.username),1)]}),_:1}),r(i,{label:"邮箱"},{default:e(()=>{var l;return[s(p((l=n(o).user)==null?void 0:l.email),1)]}),_:1}),r(i,{label:"角色"},{default:e(()=>[r(q,{type:n(o).isPlatformAdmin?"danger":"primary"},{default:e(()=>[s(p(n(o).isPlatformAdmin?"平台管理员":"商户管理员"),1)]),_:1},8,["type"])]),_:1}),n(o).isMerchantAdmin?(b(),v(i,{key:0,label:"商户名称"},{default:e(()=>{var l;return[s(p((l=n(o).user)==null?void 0:l.merchantName),1)]}),_:1})):V("",!0),n(o).isMerchantAdmin?(b(),v(i,{key:1,label:"联系电话"},{default:e(()=>{var l;return[s(p((l=n(o).user)==null?void 0:l.phone),1)]}),_:1})):V("",!0),r(i,{label:"注册时间"},{default:e(()=>{var l;return[s(p(z((l=n(o).user)==null?void 0:l.createdAt)),1)]}),_:1})]),_:1})]),_:1})]),_:1}),n(o).isMerchantAdmin?(b(),v(I,{key:0,span:12},{default:e(()=>[r(C,{class:"profile-card"},{header:e(()=>a[10]||(a[10]=[_("span",null,"账户信息",-1)])),default:e(()=>[r(E,{column:1,border:""},{default:e(()=>[r(i,{label:"账户余额"},{default:e(()=>[_("span",Y,"¥"+p(R(c.balance)),1)]),_:1}),r(i,{label:"费率"},{default:e(()=>[s(p(c.feeRate)+"%",1)]),_:1}),r(i,{label:"账户状态"},{default:e(()=>[r(q,{type:c.status==="active"?"success":"danger"},{default:e(()=>[s(p(c.status==="active"?"正常":"禁用"),1)]),_:1},8,["type"])]),_:1}),r(i,{label:"今日交易"},{default:e(()=>[s(p(c.todayTransactions)+"笔",1)]),_:1}),r(i,{label:"今日交易额"},{default:e(()=>[s("¥"+p(R(c.todayAmount)),1)]),_:1})]),_:1})]),_:1})]),_:1})):V("",!0)]),_:1}),r(C,{class:"profile-card"},{header:e(()=>a[11]||(a[11]=[_("span",null,"修改密码",-1)])),default:e(()=>[r(B,{ref_key:"passwordFormRef",ref:h,model:d,rules:D,"label-width":"120px",style:{"max-width":"500px"}},{default:e(()=>[r(w,{label:"当前密码",prop:"currentPassword"},{default:e(()=>[r(g,{modelValue:d.currentPassword,"onUpdate:modelValue":a[0]||(a[0]=l=>d.currentPassword=l),type:"password",placeholder:"请输入当前密码","show-password":""},null,8,["modelValue"])]),_:1}),r(w,{label:"新密码",prop:"newPassword"},{default:e(()=>[r(g,{modelValue:d.newPassword,"onUpdate:modelValue":a[1]||(a[1]=l=>d.newPassword=l),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),r(w,{label:"确认新密码",prop:"confirmPassword"},{default:e(()=>[r(g,{modelValue:d.confirmPassword,"onUpdate:modelValue":a[2]||(a[2]=l=>d.confirmPassword=l),type:"password",placeholder:"请再次输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),r(w,null,{default:e(()=>[r(u,{type:"primary",onClick:O,loading:N.value},{default:e(()=>a[12]||(a[12]=[s(" 修改密码 ")])),_:1,__:[12]},8,["loading"]),r(u,{onClick:U},{default:e(()=>a[13]||(a[13]=[s("重置")])),_:1,__:[13]})]),_:1})]),_:1},8,["model"])]),_:1}),r($,{modelValue:y.value,"onUpdate:modelValue":a[7]||(a[7]=l=>y.value=l),title:"编辑资料",width:"500px"},{footer:e(()=>[r(u,{onClick:a[6]||(a[6]=l=>y.value=!1)},{default:e(()=>a[14]||(a[14]=[s("取消")])),_:1,__:[14]}),r(u,{type:"primary",onClick:L,loading:x.value},{default:e(()=>a[15]||(a[15]=[s(" 保存 ")])),_:1,__:[15]},8,["loading"])]),default:e(()=>[r(B,{ref_key:"profileFormRef",ref:A,model:f,rules:S,"label-width":"100px"},{default:e(()=>[r(w,{label:"邮箱",prop:"email"},{default:e(()=>[r(g,{modelValue:f.email,"onUpdate:modelValue":a[3]||(a[3]=l=>f.email=l)},null,8,["modelValue"])]),_:1}),n(o).isMerchantAdmin?(b(),v(w,{key:0,label:"商户名称",prop:"merchantName"},{default:e(()=>[r(g,{modelValue:f.merchantName,"onUpdate:modelValue":a[4]||(a[4]=l=>f.merchantName=l)},null,8,["modelValue"])]),_:1})):V("",!0),n(o).isMerchantAdmin?(b(),v(w,{key:1,label:"联系电话",prop:"phone"},{default:e(()=>[r(g,{modelValue:f.phone,"onUpdate:modelValue":a[5]||(a[5]=l=>f.phone=l)},null,8,["modelValue"])]),_:1})):V("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},le=G(Z,[["__scopeId","data-v-f955851c"]]);export{le as default};
