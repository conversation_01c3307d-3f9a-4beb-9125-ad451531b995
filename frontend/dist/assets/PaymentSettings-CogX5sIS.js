import{m as p,_ as G,r as v,a as L,I as O,n as $,c as J,o as T,g as d,b as t,w as a,d as r,E as g,q as X,s as Q,k as u,t as b,C as W}from"./index-DH0CLqAK.js";const w={getPaymentConfig(){return p.get("/settings/payment")},updatePaymentConfig(i){return p.put("/settings/payment",i)},testPaymentConnection(i){return p.post("/settings/payment/test",i)},getConfigHistory(){return p.get("/settings/payment/history")},rollbackConfig(i){return p.post(`/settings/payment/rollback/${i}`)},getSystemSettings(){return p.get("/settings/system")},updateSystemSettings(i){return p.put("/settings/system",i)},getFeeSettings(){return p.get("/settings/fee")},updateFeeSettings(i){return p.put("/settings/fee",i)}},Y={class:"payment-settings"},Z={class:"card-header"},ee={class:"form-tip"},te={__name:"PaymentSettings",setup(i){const _=v(!1),P=v(!1),h=v(!1),y=v(),o=L({environment:"sandbox",appId:"",gatewayUrl:"",privateKey:"",alipayPublicKey:"",appCertPath:"",alipayRootCertPath:"",alipayCertPath:"",signType:"RSA2",charset:"utf-8",format:"json",status:"active"}),I=v([]),q={environment:[{required:!0,message:"请选择环境",trigger:"change"}],appId:[{required:!0,message:"请输入应用ID",trigger:"blur"},{min:16,max:32,message:"应用ID长度应为16-32位",trigger:"blur"}],privateKey:[{required:!0,message:"请输入应用私钥",trigger:"blur"}],alipayPublicKey:[{required:!0,message:"请输入支付宝公钥",trigger:"blur"}],signType:[{required:!0,message:"请选择签名类型",trigger:"change"}],charset:[{required:!0,message:"请选择字符编码",trigger:"change"}],format:[{required:!0,message:"请选择数据格式",trigger:"change"}]};O(()=>o.environment,n=>{n==="sandbox"?o.gatewayUrl="https://openapi.alipaydev.com/gateway.do":o.gatewayUrl="https://openapi.alipay.com/gateway.do"});const x=async()=>{try{const n=await w.getPaymentConfig();Object.assign(o,n.data)}catch{g.error("获取支付配置失败")}},S=async()=>{try{const n=await w.getConfigHistory();I.value=n.data}catch{g.error("获取配置历史失败")}},A=async()=>{if(y.value)try{await y.value.validate(),P.value=!0,await w.updatePaymentConfig(o),g.success("配置保存成功"),_.value=!1,S()}catch{g.error("配置保存失败")}finally{P.value=!1}},D=()=>{var n;x(),(n=y.value)==null||n.resetFields()},N=async()=>{var n,e;if(y.value)try{await y.value.validate(),h.value=!0,await w.testPaymentConnection(o),g.success("连接测试成功")}catch(V){g.error("连接测试失败: "+(((e=(n=V.response)==null?void 0:n.data)==null?void 0:e.message)||V.message))}finally{h.value=!1}},E=async n=>{try{await W.confirm("确定要回滚到此配置版本吗？","确认回滚",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await w.rollbackConfig(n.id),g.success("配置回滚成功"),x(),S()}catch(e){e!=="cancel"&&g.error("配置回滚失败")}},F=n=>new Date(n).toLocaleString("zh-CN");return $(()=>{x(),S()}),(n,e)=>{const V=r("el-switch"),R=r("el-radio"),M=r("el-radio-group"),s=r("el-form-item"),K=r("el-col"),U=r("el-tag"),H=r("el-row"),f=r("el-input"),c=r("el-option"),k=r("el-select"),C=r("el-button"),j=r("el-form"),B=r("el-card"),m=r("el-table-column"),z=r("el-table");return T(),J("div",Y,[e[24]||(e[24]=d("div",{class:"page-header"},[d("h1",null,"支付设置")],-1)),t(B,{class:"config-card"},{header:a(()=>[d("div",Z,[e[12]||(e[12]=d("span",null,"支付宝配置",-1)),t(V,{modelValue:_.value,"onUpdate:modelValue":e[0]||(e[0]=l=>_.value=l),"active-text":"编辑模式","inactive-text":"查看模式"},null,8,["modelValue"])])]),default:a(()=>[t(j,{ref_key:"configFormRef",ref:y,model:o,rules:q,"label-width":"120px",disabled:!_.value},{default:a(()=>[t(H,{gutter:20},{default:a(()=>[t(K,{span:12},{default:a(()=>[t(s,{label:"环境设置",prop:"environment"},{default:a(()=>[t(M,{modelValue:o.environment,"onUpdate:modelValue":e[1]||(e[1]=l=>o.environment=l)},{default:a(()=>[t(R,{value:"sandbox"},{default:a(()=>e[13]||(e[13]=[u("沙箱环境")])),_:1,__:[13]}),t(R,{value:"production"},{default:a(()=>e[14]||(e[14]=[u("正式环境")])),_:1,__:[14]})]),_:1},8,["modelValue"]),e[15]||(e[15]=d("div",{class:"form-tip"}," 沙箱环境用于测试，正式环境用于生产 ",-1))]),_:1,__:[15]})]),_:1}),t(K,{span:12},{default:a(()=>[t(s,{label:"状态",prop:"status"},{default:a(()=>[t(U,{type:o.status==="active"?"success":"danger"},{default:a(()=>[u(b(o.status==="active"?"启用":"禁用"),1)]),_:1},8,["type"])]),_:1})]),_:1})]),_:1}),t(s,{label:"应用ID",prop:"appId"},{default:a(()=>[t(f,{modelValue:o.appId,"onUpdate:modelValue":e[2]||(e[2]=l=>o.appId=l),placeholder:"请输入支付宝应用ID","show-word-limit":"",maxlength:"32"},null,8,["modelValue"])]),_:1}),t(s,{label:"网关地址",prop:"gatewayUrl"},{default:a(()=>[t(f,{modelValue:o.gatewayUrl,"onUpdate:modelValue":e[3]||(e[3]=l=>o.gatewayUrl=l),placeholder:"支付宝网关地址",readonly:""},null,8,["modelValue"]),d("div",ee,b(o.environment==="sandbox"?"沙箱网关地址":"正式网关地址"),1)]),_:1}),t(s,{label:"应用私钥",prop:"privateKey"},{default:a(()=>[t(f,{modelValue:o.privateKey,"onUpdate:modelValue":e[4]||(e[4]=l=>o.privateKey=l),type:"textarea",rows:6,placeholder:"请输入应用私钥（PKCS8格式）","show-word-limit":""},null,8,["modelValue"]),e[16]||(e[16]=d("div",{class:"form-tip"}," 请确保私钥格式正确，包含完整的BEGIN和END标记 ",-1))]),_:1,__:[16]}),t(s,{label:"支付宝公钥",prop:"alipayPublicKey"},{default:a(()=>[t(f,{modelValue:o.alipayPublicKey,"onUpdate:modelValue":e[5]||(e[5]=l=>o.alipayPublicKey=l),type:"textarea",rows:6,placeholder:"请输入支付宝公钥","show-word-limit":""},null,8,["modelValue"])]),_:1}),t(s,{label:"应用公钥证书",prop:"appCertPath"},{default:a(()=>[t(f,{modelValue:o.appCertPath,"onUpdate:modelValue":e[6]||(e[6]=l=>o.appCertPath=l),placeholder:"应用公钥证书路径（可选）"},null,8,["modelValue"]),e[17]||(e[17]=d("div",{class:"form-tip"}," 使用公钥证书模式时需要配置 ",-1))]),_:1,__:[17]}),t(s,{label:"支付宝根证书",prop:"alipayRootCertPath"},{default:a(()=>[t(f,{modelValue:o.alipayRootCertPath,"onUpdate:modelValue":e[7]||(e[7]=l=>o.alipayRootCertPath=l),placeholder:"支付宝根证书路径（可选）"},null,8,["modelValue"])]),_:1}),t(s,{label:"支付宝公钥证书",prop:"alipayCertPath"},{default:a(()=>[t(f,{modelValue:o.alipayCertPath,"onUpdate:modelValue":e[8]||(e[8]=l=>o.alipayCertPath=l),placeholder:"支付宝公钥证书路径（可选）"},null,8,["modelValue"])]),_:1}),t(s,{label:"签名类型",prop:"signType"},{default:a(()=>[t(k,{modelValue:o.signType,"onUpdate:modelValue":e[9]||(e[9]=l=>o.signType=l),placeholder:"请选择签名类型"},{default:a(()=>[t(c,{label:"RSA2",value:"RSA2"}),t(c,{label:"RSA",value:"RSA"})]),_:1},8,["modelValue"]),e[18]||(e[18]=d("div",{class:"form-tip"}," 推荐使用RSA2签名类型，安全性更高 ",-1))]),_:1,__:[18]}),t(s,{label:"字符编码",prop:"charset"},{default:a(()=>[t(k,{modelValue:o.charset,"onUpdate:modelValue":e[10]||(e[10]=l=>o.charset=l),placeholder:"请选择字符编码"},{default:a(()=>[t(c,{label:"UTF-8",value:"utf-8"}),t(c,{label:"GBK",value:"gbk"})]),_:1},8,["modelValue"])]),_:1}),t(s,{label:"数据格式",prop:"format"},{default:a(()=>[t(k,{modelValue:o.format,"onUpdate:modelValue":e[11]||(e[11]=l=>o.format=l),placeholder:"请选择数据格式"},{default:a(()=>[t(c,{label:"JSON",value:"json"}),t(c,{label:"XML",value:"xml"})]),_:1},8,["modelValue"])]),_:1}),_.value?(T(),X(s,{key:0},{default:a(()=>[t(C,{type:"primary",onClick:A,loading:P.value},{default:a(()=>e[19]||(e[19]=[u(" 保存配置 ")])),_:1,__:[19]},8,["loading"]),t(C,{onClick:D},{default:a(()=>e[20]||(e[20]=[u("重置")])),_:1,__:[20]}),t(C,{type:"success",onClick:N,loading:h.value},{default:a(()=>e[21]||(e[21]=[u(" 测试连接 ")])),_:1,__:[21]},8,["loading"])]),_:1})):Q("",!0)]),_:1},8,["model","disabled"])]),_:1}),t(B,{class:"history-card"},{header:a(()=>e[22]||(e[22]=[d("span",null,"配置历史",-1)])),default:a(()=>[t(z,{data:I.value,style:{width:"100%"}},{default:a(()=>[t(m,{prop:"version",label:"版本",width:"80"}),t(m,{prop:"environment",label:"环境",width:"100"},{default:a(({row:l})=>[t(U,{type:l.environment==="production"?"success":"warning"},{default:a(()=>[u(b(l.environment==="production"?"正式":"沙箱"),1)]),_:2},1032,["type"])]),_:1}),t(m,{prop:"appId",label:"应用ID",width:"200"}),t(m,{prop:"status",label:"状态",width:"100"},{default:a(({row:l})=>[t(U,{type:l.status==="active"?"success":"danger"},{default:a(()=>[u(b(l.status==="active"?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),t(m,{prop:"updatedBy",label:"更新人",width:"120"}),t(m,{prop:"updatedAt",label:"更新时间",width:"180"},{default:a(({row:l})=>[u(b(F(l.updatedAt)),1)]),_:1}),t(m,{prop:"remark",label:"备注"}),t(m,{label:"操作",width:"100"},{default:a(({row:l})=>[t(C,{size:"small",onClick:ae=>E(l)},{default:a(()=>e[23]||(e[23]=[u(" 回滚 ")])),_:2,__:[23]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})])}}},oe=G(te,[["__scopeId","data-v-3c3968f0"]]);export{oe as default};
