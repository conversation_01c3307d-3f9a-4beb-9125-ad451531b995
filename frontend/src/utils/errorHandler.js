import { ElMessage, ElNotification } from 'element-plus'

// 错误类型枚举
export const ErrorTypes = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  AUTH_ERROR: 'AUTH_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  BUSINESS_ERROR: 'BUSINESS_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
}

// 错误处理器
export class ErrorHandler {
  static handle(error, options = {}) {
    const {
      showMessage = true,
      showNotification = false,
      customMessage = null,
      onError = null
    } = options

    let errorType = ErrorTypes.UNKNOWN_ERROR
    let message = '操作失败，请稍后重试'
    let title = '错误'

    // 解析错误类型和消息
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          errorType = ErrorTypes.VALIDATION_ERROR
          message = data.message || '请求参数错误'
          title = '参数错误'
          break
        case 401:
          errorType = ErrorTypes.AUTH_ERROR
          message = '登录已过期，请重新登录'
          title = '认证失败'
          // 可以在这里触发登出逻辑
          break
        case 403:
          errorType = ErrorTypes.AUTH_ERROR
          message = '权限不足，无法执行此操作'
          title = '权限不足'
          break
        case 404:
          errorType = ErrorTypes.BUSINESS_ERROR
          message = '请求的资源不存在'
          title = '资源不存在'
          break
        case 422:
          errorType = ErrorTypes.VALIDATION_ERROR
          message = data.message || '数据验证失败'
          title = '验证失败'
          break
        case 429:
          errorType = ErrorTypes.BUSINESS_ERROR
          message = '请求过于频繁，请稍后再试'
          title = '请求限制'
          break
        case 500:
          errorType = ErrorTypes.BUSINESS_ERROR
          message = '服务器内部错误，请联系管理员'
          title = '服务器错误'
          break
        case 502:
        case 503:
        case 504:
          errorType = ErrorTypes.NETWORK_ERROR
          message = '服务暂时不可用，请稍后重试'
          title = '服务不可用'
          break
        default:
          message = data.message || `请求失败 (${status})`
      }
    } else if (error.request) {
      errorType = ErrorTypes.NETWORK_ERROR
      message = '网络连接失败，请检查网络设置'
      title = '网络错误'
    } else {
      message = error.message || '未知错误'
    }

    // 使用自定义消息
    if (customMessage) {
      message = customMessage
    }

    // 显示错误消息
    if (showMessage) {
      ElMessage.error(message)
    }

    if (showNotification) {
      ElNotification.error({
        title,
        message,
        duration: 5000
      })
    }

    // 执行自定义错误处理
    if (onError && typeof onError === 'function') {
      onError(error, errorType, message)
    }

    // 返回错误信息
    return {
      type: errorType,
      message,
      title,
      originalError: error
    }
  }

  // 处理异步操作的错误
  static async handleAsync(asyncFn, options = {}) {
    try {
      const result = await asyncFn()
      return { success: true, data: result }
    } catch (error) {
      const errorInfo = this.handle(error, options)
      return { success: false, error: errorInfo }
    }
  }

  // 处理表单验证错误
  static handleValidationError(error, formRef) {
    if (error.response && error.response.status === 422) {
      const { data } = error.response
      if (data.errors && formRef) {
        // 设置表单字段错误
        Object.keys(data.errors).forEach(field => {
          formRef.setFieldError(field, data.errors[field][0])
        })
        return true
      }
    }
    return false
  }
}

// 全局错误处理函数
export const handleError = (error, options) => {
  return ErrorHandler.handle(error, options)
}

// 异步操作包装器
export const withErrorHandling = (asyncFn, options) => {
  return ErrorHandler.handleAsync(asyncFn, options)
}

// 加载状态管理器
export class LoadingManager {
  constructor() {
    this.loadingStates = new Map()
  }

  setLoading(key, loading) {
    this.loadingStates.set(key, loading)
  }

  isLoading(key) {
    return this.loadingStates.get(key) || false
  }

  clearLoading(key) {
    this.loadingStates.delete(key)
  }

  clearAllLoading() {
    this.loadingStates.clear()
  }
}

// 全局加载管理器实例
export const globalLoadingManager = new LoadingManager()

// 加载状态装饰器
export const withLoading = (key) => {
  return (target, propertyName, descriptor) => {
    const method = descriptor.value
    descriptor.value = async function(...args) {
      globalLoadingManager.setLoading(key, true)
      try {
        return await method.apply(this, args)
      } finally {
        globalLoadingManager.setLoading(key, false)
      }
    }
    return descriptor
  }
}

// 重试机制
export class RetryManager {
  static async retry(fn, options = {}) {
    const {
      maxRetries = 3,
      delay = 1000,
      backoff = 2,
      shouldRetry = (error) => error.response?.status >= 500
    } = options

    let lastError
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error
        
        if (i === maxRetries || !shouldRetry(error)) {
          throw error
        }
        
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(backoff, i)))
      }
    }
    
    throw lastError
  }
}

// 防抖处理
export const debounce = (fn, delay) => {
  let timeoutId
  return (...args) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => fn.apply(this, args), delay)
  }
}

// 节流处理
export const throttle = (fn, delay) => {
  let lastCall = 0
  return (...args) => {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      return fn.apply(this, args)
    }
  }
}
