import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 路由组件
const Login = () => import('@/views/auth/Login.vue')
const Register = () => import('@/views/auth/Register.vue')
const Dashboard = () => import('@/views/Dashboard.vue')
const MerchantManagement = () => import('@/views/merchant/MerchantManagement.vue')
const MerchantDetail = () => import('@/views/merchant/MerchantDetail.vue')
const TransactionList = () => import('@/views/transaction/TransactionList.vue')
const PayoutManagement = () => import('@/views/payout/PayoutManagement.vue')
const RechargeManagement = () => import('@/views/recharge/RechargeManagement.vue')
const PaymentSettings = () => import('@/views/settings/PaymentSettings.vue')
const ApiKeyManagement = () => import('@/views/api/ApiKeyManagement.vue')
const Profile = () => import('@/views/profile/Profile.vue')

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresAuth: false }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: { requiresAuth: false }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true }
  },
  {
    path: '/merchants',
    name: 'MerchantManagement',
    component: MerchantManagement,
    meta: { requiresAuth: true, roles: ['platform_admin'] }
  },
  {
    path: '/merchants/:id',
    name: 'MerchantDetail',
    component: MerchantDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/transactions',
    name: 'TransactionList',
    component: TransactionList,
    meta: { requiresAuth: true }
  },
  {
    path: '/payouts',
    name: 'PayoutManagement',
    component: PayoutManagement,
    meta: { requiresAuth: true }
  },
  {
    path: '/recharge',
    name: 'RechargeManagement',
    component: RechargeManagement,
    meta: { requiresAuth: true, roles: ['merchant_admin'] }
  },
  {
    path: '/settings/payment',
    name: 'PaymentSettings',
    component: PaymentSettings,
    meta: { requiresAuth: true, roles: ['platform_admin'] }
  },
  {
    path: '/api-keys',
    name: 'ApiKeyManagement',
    component: ApiKeyManagement,
    meta: { requiresAuth: true }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: { requiresAuth: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  
  if (to.meta.requiresAuth && !userStore.isAuthenticated) {
    next('/login')
  } else if (to.meta.roles && !to.meta.roles.includes(userStore.user?.role)) {
    next('/dashboard')
  } else {
    next()
  }
})

export default router
