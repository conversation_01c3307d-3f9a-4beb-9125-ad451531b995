<template>
  <div class="app-layout">
    <!-- 未登录状态显示登录/注册页面 -->
    <div v-if="!userStore.isAuthenticated" class="auth-container">
      <router-view />
    </div>
    
    <!-- 已登录状态显示主布局 -->
    <el-container v-else class="main-container">
      <!-- 侧边栏 -->
      <el-aside width="250px" class="sidebar">
        <div class="logo-container">
          <h2>支付宝多商户平台</h2>
        </div>
        
        <el-menu
          :default-active="$route.path"
          class="sidebar-menu"
          router
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
        >
          <!-- 仪表盘 -->
          <el-menu-item index="/dashboard">
            <el-icon><House /></el-icon>
            <span>仪表盘</span>
          </el-menu-item>

          <!-- 平台管理员菜单 -->
          <template v-if="userStore.isPlatformAdmin">
            <!-- 商户管理 -->
            <el-menu-item index="/merchants">
              <el-icon><Shop /></el-icon>
              <span>商户管理</span>
            </el-menu-item>

            <!-- 支付设置 -->
            <el-menu-item index="/settings/payment">
              <el-icon><Setting /></el-icon>
              <span>支付设置</span>
            </el-menu-item>
          </template>

          <!-- 商户管理员菜单 -->
          <template v-if="userStore.isMerchantAdmin">
            <!-- 充值管理 -->
            <el-menu-item index="/recharge">
              <el-icon><Wallet /></el-icon>
              <span>充值管理</span>
            </el-menu-item>
          </template>

          <!-- 通用菜单 -->
          <!-- 交易管理 -->
          <el-menu-item index="/transactions">
            <el-icon><Money /></el-icon>
            <span>交易记录</span>
          </el-menu-item>

          <!-- 代付管理 -->
          <el-menu-item index="/payouts">
            <el-icon><CreditCard /></el-icon>
            <span>代付管理</span>
          </el-menu-item>

          <!-- API密钥管理 -->
          <el-menu-item index="/api-keys">
            <el-icon><Key /></el-icon>
            <span>API密钥</span>
          </el-menu-item>

          <!-- 个人中心 -->
          <el-menu-item index="/profile">
            <el-icon><User /></el-icon>
            <span>个人中心</span>
          </el-menu-item>
        </el-menu>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item v-if="breadcrumbItems.length > 0" 
                v-for="item in breadcrumbItems" 
                :key="item.path"
                :to="item.path ? { path: item.path } : null"
              >
                {{ item.name }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          
          <div class="header-right">
            <el-dropdown @command="handleCommand">
              <span class="user-info">
                <el-avatar :size="32" :src="userStore.user?.avatar">
                  {{ userStore.user?.username?.charAt(0).toUpperCase() }}
                </el-avatar>
                <span class="username">{{ userStore.user?.username }}</span>
                <el-icon><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                  <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <!-- 主内容 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessageBox } from 'element-plus'
import {
  House,
  Shop,
  Money,
  CreditCard,
  Key,
  User,
  ArrowDown,
  Setting,
  Wallet
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 面包屑导航
const breadcrumbItems = computed(() => {
  const items = []
  const routeName = route.name
  
  switch (routeName) {
    case 'MerchantManagement':
      items.push({ name: '商户管理', path: '/merchants' })
      break
    case 'MerchantDetail':
      items.push({ name: '商户管理', path: '/merchants' })
      items.push({ name: '商户详情' })
      break
    case 'TransactionList':
      items.push({ name: '交易记录', path: '/transactions' })
      break
    case 'PayoutManagement':
      items.push({ name: '代付管理', path: '/payouts' })
      break
    case 'ApiKeyManagement':
      items.push({ name: 'API密钥', path: '/api-keys' })
      break
    case 'Profile':
      items.push({ name: '个人中心', path: '/profile' })
      break
  }
  
  return items
})

// 处理下拉菜单命令
const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        userStore.logout()
        router.push('/login')
      } catch {
        // 用户取消
      }
      break
  }
}
</script>

<style scoped>
.app-layout {
  height: 100vh;
  width: 100vw;
}

.auth-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.main-container {
  height: 100vh;
}

.sidebar {
  background-color: #304156;
  overflow: hidden;
}

.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2b3a4b;
  color: white;
  border-bottom: 1px solid #1f2d3d;
}

.logo-container h2 {
  font-size: 16px;
  font-weight: 600;
}

.sidebar-menu {
  border: none;
  height: calc(100vh - 60px);
}

.header {
  background-color: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  margin: 0 8px;
  font-size: 14px;
  color: #606266;
}

.main-content {
  background-color: #f0f2f5;
  padding: 20px;
}
</style>
