<template>
  <div class="qr-payment">
    <div class="payment-header">
      <h3>扫码支付</h3>
      <div class="payment-amount">¥{{ formatMoney(amount) }}</div>
    </div>
    
    <div class="payment-content">
      <!-- 二维码区域 -->
      <div class="qr-section">
        <div class="qr-code-container" v-if="qrCodeUrl && !isExpired">
          <el-image
            :src="qrCodeUrl"
            alt="支付二维码"
            class="qr-code"
            fit="contain"
            :preview-src-list="[qrCodeUrl]"
          >
            <template #error>
              <div class="qr-error">
                <el-icon><Picture /></el-icon>
                <div>二维码加载失败</div>
              </div>
            </template>
          </el-image>
          
          <!-- 倒计时 -->
          <div class="countdown-container" v-if="countdown > 0">
            <el-progress
              :percentage="countdownPercentage"
              :stroke-width="4"
              :show-text="false"
              status="warning"
            />
            <div class="countdown-text">
              {{ Math.floor(countdown / 60) }}:{{ String(countdown % 60).padStart(2, '0') }}
            </div>
          </div>
        </div>
        
        <!-- 过期状态 -->
        <div class="qr-expired" v-else-if="isExpired">
          <el-icon class="expired-icon"><Clock /></el-icon>
          <div class="expired-text">二维码已过期</div>
          <el-button type="primary" @click="refreshQrCode" :loading="refreshing">
            重新生成
          </el-button>
        </div>
        
        <!-- 加载状态 -->
        <div class="qr-loading" v-else>
          <el-skeleton animated>
            <template #template>
              <div class="qr-skeleton">
                <el-skeleton-item variant="image" style="width: 200px; height: 200px;" />
              </div>
            </template>
          </el-skeleton>
        </div>
      </div>
      
      <!-- 支付信息 -->
      <div class="payment-info">
        <el-descriptions :column="1" size="small">
          <el-descriptions-item label="订单号">{{ orderId }}</el-descriptions-item>
          <el-descriptions-item label="支付金额">¥{{ formatMoney(amount) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(createdAt) }}</el-descriptions-item>
          <el-descriptions-item label="过期时间" v-if="expireTime">
            {{ formatDateTime(expireTime) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <!-- 支付状态 -->
      <div class="payment-status">
        <el-tag :type="getStatusType(status)" size="large">
          {{ getStatusText(status) }}
        </el-tag>
      </div>
      
      <!-- 操作按钮 -->
      <div class="payment-actions">
        <el-button @click="refreshQrCode" :loading="refreshing" v-if="status === 'pending' && !isExpired">
          <el-icon><Refresh /></el-icon>
          刷新二维码
        </el-button>
        <el-button type="primary" @click="checkPaymentStatus" :loading="checking">
          <el-icon><Search /></el-icon>
          查询状态
        </el-button>
        <el-button @click="copyOrderId">
          <el-icon><CopyDocument /></el-icon>
          复制订单号
        </el-button>
      </div>
      
      <!-- 支付提示 -->
      <div class="payment-tips">
        <el-alert
          title="支付提示"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <ul>
              <li>请使用支付宝扫描上方二维码完成支付</li>
              <li>二维码有效期为15分钟，过期后请重新生成</li>
              <li>支付完成后请点击"查询状态"确认到账</li>
              <li>如遇问题请联系客服处理</li>
            </ul>
          </template>
        </el-alert>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Picture, Clock, Refresh, Search, CopyDocument } from '@element-plus/icons-vue'

const props = defineProps({
  orderId: {
    type: String,
    required: true
  },
  amount: {
    type: Number,
    required: true
  },
  qrCodeUrl: {
    type: String,
    default: ''
  },
  status: {
    type: String,
    default: 'pending'
  },
  createdAt: {
    type: String,
    required: true
  },
  expireTime: {
    type: String,
    default: ''
  },
  autoRefresh: {
    type: Boolean,
    default: true
  },
  refreshInterval: {
    type: Number,
    default: 5000 // 5秒
  }
})

const emit = defineEmits(['refresh-qrcode', 'check-status', 'status-changed'])

const refreshing = ref(false)
const checking = ref(false)
const countdown = ref(0)
const totalTime = ref(15 * 60) // 15分钟
let countdownTimer = null
let statusTimer = null

// 计算属性
const isExpired = computed(() => {
  return props.status === 'expired' || countdown.value <= 0
})

const countdownPercentage = computed(() => {
  if (totalTime.value === 0) return 0
  return Math.max(0, (countdown.value / totalTime.value) * 100)
})

// 监听过期时间变化
watch(() => props.expireTime, (newExpireTime) => {
  if (newExpireTime) {
    startCountdown()
  }
}, { immediate: true })

// 监听状态变化
watch(() => props.status, (newStatus) => {
  if (newStatus === 'success') {
    stopTimers()
    ElMessage.success('支付成功！')
  } else if (newStatus === 'failed') {
    stopTimers()
    ElMessage.error('支付失败')
  }
})

// 开始倒计时
const startCountdown = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
  
  if (props.expireTime) {
    const expireTime = new Date(props.expireTime).getTime()
    const createdTime = new Date(props.createdAt).getTime()
    totalTime.value = Math.floor((expireTime - createdTime) / 1000)
    
    const updateCountdown = () => {
      const now = Date.now()
      const remaining = Math.max(0, Math.floor((expireTime - now) / 1000))
      countdown.value = remaining
      
      if (remaining <= 0) {
        clearInterval(countdownTimer)
        emit('status-changed', 'expired')
      }
    }
    
    updateCountdown()
    countdownTimer = setInterval(updateCountdown, 1000)
  }
}

// 开始状态轮询
const startStatusPolling = () => {
  if (!props.autoRefresh || props.status !== 'pending') return
  
  if (statusTimer) {
    clearInterval(statusTimer)
  }
  
  statusTimer = setInterval(() => {
    if (props.status === 'pending' && !isExpired.value) {
      checkPaymentStatus(false) // 静默查询
    } else {
      clearInterval(statusTimer)
    }
  }, props.refreshInterval)
}

// 停止所有定时器
const stopTimers = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
  if (statusTimer) {
    clearInterval(statusTimer)
    statusTimer = null
  }
}

// 刷新二维码
const refreshQrCode = async () => {
  refreshing.value = true
  try {
    await emit('refresh-qrcode')
  } finally {
    refreshing.value = false
  }
}

// 查询支付状态
const checkPaymentStatus = async (showMessage = true) => {
  if (showMessage) {
    checking.value = true
  }
  
  try {
    await emit('check-status')
    if (showMessage && props.status === 'pending') {
      ElMessage.info('订单尚未支付，请继续等待')
    }
  } catch (error) {
    if (showMessage) {
      ElMessage.error('查询状态失败')
    }
  } finally {
    if (showMessage) {
      checking.value = false
    }
  }
}

// 复制订单号
const copyOrderId = async () => {
  try {
    await navigator.clipboard.writeText(props.orderId)
    ElMessage.success('订单号已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

// 格式化金额
const formatMoney = (amount) => {
  return (amount / 100).toFixed(2)
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'success': 'success',
    'failed': 'danger',
    'expired': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '待支付',
    'success': '支付成功',
    'failed': '支付失败',
    'expired': '已过期'
  }
  return statusMap[status] || status
}

onMounted(() => {
  startCountdown()
  startStatusPolling()
})

onUnmounted(() => {
  stopTimers()
})

// 暴露方法给父组件
defineExpose({
  refreshQrCode,
  checkPaymentStatus,
  startCountdown,
  stopTimers
})
</script>

<style scoped>
.qr-payment {
  max-width: 400px;
  margin: 0 auto;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.payment-header {
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.payment-header h3 {
  margin: 0 0 10px 0;
  font-size: 18px;
  font-weight: 600;
}

.payment-amount {
  font-size: 32px;
  font-weight: 700;
  margin: 0;
}

.payment-content {
  padding: 20px;
}

.qr-section {
  text-align: center;
  margin-bottom: 20px;
}

.qr-code-container {
  position: relative;
  display: inline-block;
}

.qr-code {
  width: 200px;
  height: 200px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.qr-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 200px;
  height: 200px;
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  color: #909399;
}

.qr-error .el-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

.countdown-container {
  margin-top: 10px;
}

.countdown-text {
  margin-top: 5px;
  font-size: 16px;
  font-weight: 600;
  color: #E6A23C;
}

.qr-expired {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  color: #909399;
}

.expired-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

.expired-text {
  font-size: 16px;
  margin-bottom: 20px;
}

.qr-loading {
  display: flex;
  justify-content: center;
  padding: 20px;
}

.qr-skeleton {
  display: flex;
  justify-content: center;
}

.payment-info {
  margin-bottom: 20px;
}

.payment-status {
  text-align: center;
  margin-bottom: 20px;
}

.payment-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.payment-actions .el-button {
  flex: 1;
  min-width: 100px;
}

.payment-tips {
  margin-top: 20px;
}

.payment-tips ul {
  margin: 0;
  padding-left: 20px;
}

.payment-tips li {
  margin-bottom: 5px;
  font-size: 14px;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .qr-payment {
    margin: 0;
    border-radius: 0;
  }
  
  .payment-actions .el-button {
    min-width: 80px;
    font-size: 12px;
  }
  
  .qr-code {
    width: 180px;
    height: 180px;
  }
}
</style>
