<template>
  <div class="merchant-detail">
    <div class="page-header">
      <el-button @click="$router.go(-1)" style="margin-right: 16px;">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
      <h1>商户详情</h1>
    </div>
    
    <el-row :gutter="20" v-loading="loading">
      <!-- 基本信息 -->
      <el-col :span="12">
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
              <el-button type="primary" size="small" @click="editMerchant">编辑</el-button>
            </div>
          </template>
          
          <el-descriptions :column="1" border v-if="merchant">
            <el-descriptions-item label="商户ID">{{ merchant.id }}</el-descriptions-item>
            <el-descriptions-item label="商户名称">{{ merchant.name }}</el-descriptions-item>
            <el-descriptions-item label="用户名">{{ merchant.username }}</el-descriptions-item>
            <el-descriptions-item label="邮箱">{{ merchant.email }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{ merchant.phone }}</el-descriptions-item>
            <el-descriptions-item label="费率">{{ merchant.feeRate }}%</el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="merchant.status === 'active' ? 'success' : 'danger'">
                {{ merchant.status === 'active' ? '启用' : '禁用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="注册时间">
              {{ formatDateTime(merchant.createdAt) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
      
      <!-- 账户信息 -->
      <el-col :span="12">
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>账户信息</span>
              <el-button type="primary" size="small" @click="showBalanceDialog = true">
                调整余额
              </el-button>
            </div>
          </template>
          
          <el-descriptions :column="1" border v-if="merchant">
            <el-descriptions-item label="账户余额">
              <span class="balance-amount">¥{{ formatMoney(merchant.balance) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="今日交易">{{ stats.todayTransactions }}笔</el-descriptions-item>
            <el-descriptions-item label="今日交易额">¥{{ formatMoney(stats.todayAmount) }}</el-descriptions-item>
            <el-descriptions-item label="总交易笔数">{{ stats.totalTransactions }}笔</el-descriptions-item>
            <el-descriptions-item label="总交易金额">¥{{ formatMoney(stats.totalAmount) }}</el-descriptions-item>
            <el-descriptions-item label="总手续费">¥{{ formatMoney(stats.totalFee) }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 最近交易 -->
    <el-card class="transactions-card">
      <template #header>
        <div class="card-header">
          <span>最近交易</span>
          <el-button type="primary" size="small" @click="viewAllTransactions">
            查看全部
          </el-button>
        </div>
      </template>
      
      <el-table :data="recentTransactions" style="width: 100%">
        <el-table-column prop="orderId" label="订单号" width="200" />
        <el-table-column prop="amount" label="金额" width="120">
          <template #default="{ row }">
            ¥{{ formatMoney(row.amount) }}
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.type === 'payment' ? 'success' : 'warning'">
              {{ row.type === 'payment' ? '收款' : '代付' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 调整余额对话框 -->
    <el-dialog v-model="showBalanceDialog" title="调整余额" width="400px">
      <el-form
        ref="balanceFormRef"
        :model="balanceForm"
        :rules="balanceRules"
        label-width="100px"
      >
        <el-form-item label="当前余额">
          <span class="balance-amount">¥{{ formatMoney(merchant?.balance || 0) }}</span>
        </el-form-item>
        <el-form-item label="调整类型" prop="type">
          <el-radio-group v-model="balanceForm.type">
            <el-radio value="increase">增加</el-radio>
            <el-radio value="decrease">减少</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="调整金额" prop="amount">
          <el-input-number
            v-model="balanceForm.amount"
            :min="0.01"
            :precision="2"
            controls-position="right"
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="balanceForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入调整原因"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showBalanceDialog = false">取消</el-button>
        <el-button type="primary" @click="adjustBalance" :loading="balanceLoading">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { merchantApi } from '@/api/merchant'
import { transactionApi } from '@/api/transaction'
import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const balanceLoading = ref(false)
const showBalanceDialog = ref(false)
const balanceFormRef = ref()

// 商户信息
const merchant = ref(null)

// 统计信息
const stats = reactive({
  todayTransactions: 0,
  todayAmount: 0,
  totalTransactions: 0,
  totalAmount: 0,
  totalFee: 0
})

// 最近交易
const recentTransactions = ref([])

// 余额调整表单
const balanceForm = reactive({
  type: 'increase',
  amount: 0,
  remark: ''
})

// 余额调整验证规则
const balanceRules = {
  type: [
    { required: true, message: '请选择调整类型', trigger: 'change' }
  ],
  amount: [
    { required: true, message: '请输入调整金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '金额必须大于0', trigger: 'blur' }
  ],
  remark: [
    { required: true, message: '请输入调整原因', trigger: 'blur' }
  ]
}

// 获取商户详情
const fetchMerchantDetail = async () => {
  loading.value = true
  try {
    const merchantId = route.params.id
    const response = await merchantApi.getMerchant(merchantId)
    merchant.value = response.data
    
    // 获取统计信息
    const statsResponse = await merchantApi.getMerchantStats(merchantId)
    Object.assign(stats, statsResponse.data)
    
    // 获取最近交易
    const transactionsResponse = await transactionApi.getTransactions({
      merchantId,
      page: 1,
      limit: 10,
      sort: 'createdAt',
      order: 'desc'
    })
    recentTransactions.value = transactionsResponse.data.items
  } catch (error) {
    ElMessage.error('获取商户详情失败')
    router.go(-1)
  } finally {
    loading.value = false
  }
}

// 编辑商户
const editMerchant = () => {
  // 这里可以打开编辑对话框或跳转到编辑页面
  ElMessage.info('编辑功能待实现')
}

// 调整余额
const adjustBalance = async () => {
  if (!balanceFormRef.value) return
  
  try {
    await balanceFormRef.value.validate()
    balanceLoading.value = true
    
    const adjustAmount = balanceForm.type === 'increase' 
      ? balanceForm.amount * 100 
      : -balanceForm.amount * 100
    
    await merchantApi.adjustMerchantBalance(merchant.value.id, {
      amount: adjustAmount,
      remark: balanceForm.remark
    })
    
    ElMessage.success('余额调整成功')
    showBalanceDialog.value = false
    resetBalanceForm()
    fetchMerchantDetail() // 刷新数据
  } catch (error) {
    ElMessage.error('余额调整失败')
  } finally {
    balanceLoading.value = false
  }
}

// 重置余额表单
const resetBalanceForm = () => {
  Object.assign(balanceForm, {
    type: 'increase',
    amount: 0,
    remark: ''
  })
  balanceFormRef.value?.resetFields()
}

// 查看所有交易
const viewAllTransactions = () => {
  router.push({
    path: '/transactions',
    query: { merchantId: merchant.value.id }
  })
}

// 格式化金额
const formatMoney = (amount) => {
  return (amount / 100).toFixed(2)
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'success': 'success',
    'failed': 'danger',
    'cancelled': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '处理中',
    'success': '成功',
    'failed': '失败',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

onMounted(() => {
  fetchMerchantDetail()
})
</script>

<style scoped>
.merchant-detail {
  padding: 0;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  color: #303133;
  margin: 0;
}

.info-card {
  margin-bottom: 20px;
}

.transactions-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.balance-amount {
  font-size: 18px;
  font-weight: 600;
  color: #67C23A;
}
</style>
