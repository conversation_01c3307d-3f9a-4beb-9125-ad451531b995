<template>
  <div class="merchant-management">
    <div class="page-header">
      <h1>商户管理</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        新增商户
      </el-button>
    </div>
    
    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="商户名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入商户名称"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="inactive" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 商户列表 -->
    <el-card class="table-card">
      <el-table :data="merchants" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="商户名称" width="200" />
        <el-table-column prop="username" label="用户名" width="150" />
        <el-table-column prop="email" label="邮箱" width="200" />
        <el-table-column prop="phone" label="联系电话" width="150" />
        <el-table-column prop="balance" label="余额" width="120">
          <template #default="{ row }">
            ¥{{ formatMoney(row.balance) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewMerchant(row.id)">查看</el-button>
            <el-button size="small" type="primary" @click="editMerchant(row)">编辑</el-button>
            <el-button
              size="small"
              :type="row.status === 'active' ? 'warning' : 'success'"
              @click="toggleStatus(row)"
            >
              {{ row.status === 'active' ? '禁用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 创建/编辑商户对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingMerchant ? '编辑商户' : '新增商户'"
      width="600px"
    >
      <el-form
        ref="merchantFormRef"
        :model="merchantForm"
        :rules="merchantRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="merchantForm.username" :disabled="editingMerchant" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="merchantForm.email" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!editingMerchant">
          <el-input v-model="merchantForm.password" type="password" />
        </el-form-item>
        <el-form-item label="商户名称" prop="name">
          <el-input v-model="merchantForm.name" />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="merchantForm.phone" />
        </el-form-item>
        <el-form-item label="费率" prop="feeRate">
          <el-input-number
            v-model="merchantForm.feeRate"
            :min="0"
            :max="100"
            :precision="2"
            controls-position="right"
          />
          <span style="margin-left: 8px;">%</span>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { merchantApi } from '@/api/merchant'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const router = useRouter()
const loading = ref(false)
const submitting = ref(false)
const showCreateDialog = ref(false)
const editingMerchant = ref(null)
const merchantFormRef = ref()

// 搜索表单
const searchForm = reactive({
  name: '',
  status: ''
})

// 商户列表
const merchants = ref([])

// 分页
const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// 商户表单
const merchantForm = reactive({
  username: '',
  email: '',
  password: '',
  name: '',
  phone: '',
  feeRate: 0
})

// 表单验证规则
const merchantRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入商户名称', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  feeRate: [
    { required: true, message: '请输入费率', trigger: 'blur' }
  ]
}

// 获取商户列表
const fetchMerchants = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.limit,
      ...searchForm
    }
    const response = await merchantApi.getMerchants(params)
    merchants.value = response.data.items
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取商户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchMerchants()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    status: ''
  })
  handleSearch()
}

// 查看商户详情
const viewMerchant = (id) => {
  router.push(`/merchants/${id}`)
}

// 编辑商户
const editMerchant = (merchant) => {
  editingMerchant.value = merchant
  Object.assign(merchantForm, {
    username: merchant.username,
    email: merchant.email,
    name: merchant.name,
    phone: merchant.phone,
    feeRate: merchant.feeRate
  })
  showCreateDialog.value = true
}

// 切换状态
const toggleStatus = async (merchant) => {
  const action = merchant.status === 'active' ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(`确定要${action}该商户吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const newStatus = merchant.status === 'active' ? 'inactive' : 'active'
    await merchantApi.toggleMerchantStatus(merchant.id, newStatus)
    ElMessage.success(`${action}成功`)
    fetchMerchants()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${action}失败`)
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!merchantFormRef.value) return
  
  try {
    await merchantFormRef.value.validate()
    submitting.value = true
    
    if (editingMerchant.value) {
      await merchantApi.updateMerchant(editingMerchant.value.id, merchantForm)
      ElMessage.success('更新成功')
    } else {
      await merchantApi.createMerchant(merchantForm)
      ElMessage.success('创建成功')
    }
    
    showCreateDialog.value = false
    resetForm()
    fetchMerchants()
  } catch (error) {
    ElMessage.error(editingMerchant.value ? '更新失败' : '创建失败')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  editingMerchant.value = null
  Object.assign(merchantForm, {
    username: '',
    email: '',
    password: '',
    name: '',
    phone: '',
    feeRate: 0
  })
  merchantFormRef.value?.resetFields()
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.limit = size
  pagination.page = 1
  fetchMerchants()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchMerchants()
}

// 格式化金额
const formatMoney = (amount) => {
  return (amount / 100).toFixed(2)
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchMerchants()
})
</script>

<style scoped>
.merchant-management {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  color: #303133;
  margin: 0;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
