<template>
  <div class="api-key-management">
    <div class="page-header">
      <h1>API密钥管理</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        创建密钥
      </el-button>
    </div>
    
    <!-- API密钥列表 -->
    <el-card class="table-card">
      <el-table :data="apiKeys" style="width: 100%" v-loading="loading">
        <el-table-column prop="name" label="密钥名称" width="200" />
        <el-table-column prop="appId" label="App ID" width="200" />
        <el-table-column prop="appSecret" label="App Secret" width="300">
          <template #default="{ row }">
            <span v-if="!row.showSecret">{{ maskSecret(row.appSecret) }}</span>
            <span v-else>{{ row.appSecret }}</span>
            <el-button 
              size="small" 
              type="text" 
              @click="toggleSecret(row)"
              style="margin-left: 8px;"
            >
              {{ row.showSecret ? '隐藏' : '显示' }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastUsedAt" label="最后使用" width="180">
          <template #default="{ row }">
            {{ row.lastUsedAt ? formatDateTime(row.lastUsedAt) : '从未使用' }}
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="editApiKey(row)">编辑</el-button>
            <el-button size="small" type="warning" @click="regenerateSecret(row)">
              重新生成
            </el-button>
            <el-button
              size="small"
              :type="row.status === 'active' ? 'warning' : 'success'"
              @click="toggleStatus(row)"
            >
              {{ row.status === 'active' ? '禁用' : '启用' }}
            </el-button>
            <el-button size="small" type="danger" @click="deleteApiKey(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 创建/编辑API密钥对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingApiKey ? '编辑API密钥' : '创建API密钥'"
      width="500px"
    >
      <el-form
        ref="apiKeyFormRef"
        :model="apiKeyForm"
        :rules="apiKeyRules"
        label-width="100px"
      >
        <el-form-item label="密钥名称" prop="name">
          <el-input v-model="apiKeyForm.name" placeholder="请输入密钥名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="apiKeyForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入密钥描述"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </template>
    </el-dialog>
    
    <!-- 密钥详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="密钥详情" width="600px">
      <el-alert
        title="请妥善保管您的密钥信息"
        type="warning"
        :closable="false"
        style="margin-bottom: 20px;"
      />
      
      <el-descriptions :column="1" border v-if="newApiKey">
        <el-descriptions-item label="密钥名称">{{ newApiKey.name }}</el-descriptions-item>
        <el-descriptions-item label="App ID">
          <el-input v-model="newApiKey.appId" readonly>
            <template #append>
              <el-button @click="copyToClipboard(newApiKey.appId)">复制</el-button>
            </template>
          </el-input>
        </el-descriptions-item>
        <el-descriptions-item label="App Secret">
          <el-input v-model="newApiKey.appSecret" readonly>
            <template #append>
              <el-button @click="copyToClipboard(newApiKey.appSecret)">复制</el-button>
            </template>
          </el-input>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { apiKeyApi } from '@/api/apiKey'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const loading = ref(false)
const submitting = ref(false)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const editingApiKey = ref(null)
const newApiKey = ref(null)
const apiKeyFormRef = ref()

// API密钥列表
const apiKeys = ref([])

// API密钥表单
const apiKeyForm = reactive({
  name: '',
  description: ''
})

// 表单验证规则
const apiKeyRules = {
  name: [
    { required: true, message: '请输入密钥名称', trigger: 'blur' }
  ]
}

// 获取API密钥列表
const fetchApiKeys = async () => {
  loading.value = true
  try {
    const response = await apiKeyApi.getApiKeys()
    apiKeys.value = response.data.map(key => ({
      ...key,
      showSecret: false
    }))
  } catch (error) {
    ElMessage.error('获取API密钥列表失败')
  } finally {
    loading.value = false
  }
}

// 编辑API密钥
const editApiKey = (apiKey) => {
  editingApiKey.value = apiKey
  Object.assign(apiKeyForm, {
    name: apiKey.name,
    description: apiKey.description
  })
  showCreateDialog.value = true
}

// 切换密钥显示/隐藏
const toggleSecret = (apiKey) => {
  apiKey.showSecret = !apiKey.showSecret
}

// 掩码显示密钥
const maskSecret = (secret) => {
  if (!secret) return ''
  return secret.substring(0, 8) + '****' + secret.substring(secret.length - 8)
}

// 切换状态
const toggleStatus = async (apiKey) => {
  const action = apiKey.status === 'active' ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(`确定要${action}该API密钥吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const newStatus = apiKey.status === 'active' ? 'inactive' : 'active'
    await apiKeyApi.toggleApiKeyStatus(apiKey.id, newStatus)
    ElMessage.success(`${action}成功`)
    fetchApiKeys()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${action}失败`)
    }
  }
}

// 重新生成密钥
const regenerateSecret = async (apiKey) => {
  try {
    await ElMessageBox.confirm('重新生成密钥后，原密钥将失效，确定继续吗？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await apiKeyApi.regenerateApiKey(apiKey.id)
    newApiKey.value = response.data
    showDetailDialog.value = true
    ElMessage.success('密钥重新生成成功')
    fetchApiKeys()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重新生成密钥失败')
    }
  }
}

// 删除API密钥
const deleteApiKey = async (apiKey) => {
  try {
    await ElMessageBox.confirm('删除后无法恢复，确定要删除该API密钥吗？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await apiKeyApi.deleteApiKey(apiKey.id)
    ElMessage.success('删除成功')
    fetchApiKeys()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!apiKeyFormRef.value) return
  
  try {
    await apiKeyFormRef.value.validate()
    submitting.value = true
    
    if (editingApiKey.value) {
      await apiKeyApi.updateApiKey(editingApiKey.value.id, apiKeyForm)
      ElMessage.success('更新成功')
    } else {
      const response = await apiKeyApi.createApiKey(apiKeyForm)
      newApiKey.value = response.data
      showDetailDialog.value = true
      ElMessage.success('创建成功')
    }
    
    showCreateDialog.value = false
    resetForm()
    fetchApiKeys()
  } catch (error) {
    ElMessage.error(editingApiKey.value ? '更新失败' : '创建失败')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  editingApiKey.value = null
  Object.assign(apiKeyForm, {
    name: '',
    description: ''
  })
  apiKeyFormRef.value?.resetFields()
}

// 复制到剪贴板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchApiKeys()
})
</script>

<style scoped>
.api-key-management {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  color: #303133;
  margin: 0;
}

.table-card {
  margin-bottom: 20px;
}
</style>
