<template>
  <div class="payout-management">
    <div class="page-header">
      <h1>代付管理</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        发起代付
      </el-button>
    </div>
    
    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="订单号">
          <el-input
            v-model="searchForm.orderId"
            placeholder="请输入订单号"
            clearable
          />
        </el-form-item>
        <el-form-item label="收款账户">
          <el-input
            v-model="searchForm.payeeAccount"
            placeholder="请输入收款账户"
            clearable
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="处理中" value="pending" />
            <el-option label="成功" value="success" />
            <el-option label="失败" value="failed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 代付列表 -->
    <el-card class="table-card">
      <el-table :data="payouts" style="width: 100%" v-loading="loading">
        <el-table-column prop="orderId" label="订单号" width="200" />
        <el-table-column prop="merchantName" label="商户名称" width="150" v-if="userStore.isPlatformAdmin" />
        <el-table-column prop="amount" label="代付金额" width="120">
          <template #default="{ row }">
            ¥{{ formatMoney(row.amount) }}
          </template>
        </el-table-column>
        <el-table-column prop="fee" label="手续费" width="100">
          <template #default="{ row }">
            ¥{{ formatMoney(row.fee) }}
          </template>
        </el-table-column>
        <el-table-column prop="actualAmount" label="实际金额" width="120">
          <template #default="{ row }">
            ¥{{ formatMoney(row.actualAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="payeeAccount" label="收款账户" width="150" />
        <el-table-column prop="payeeName" label="收款姓名" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewPayout(row)">查看详情</el-button>
            <el-button 
              size="small" 
              type="primary" 
              @click="queryStatus(row)"
              v-if="row.status === 'pending'"
            >
              查询状态
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 发起代付对话框 -->
    <el-dialog v-model="showCreateDialog" title="发起代付" width="600px">
      <el-form
        ref="payoutFormRef"
        :model="payoutForm"
        :rules="payoutRules"
        label-width="120px"
      >
        <el-form-item label="收款账户" prop="payeeAccount">
          <el-input v-model="payoutForm.payeeAccount" placeholder="请输入支付宝账户" />
        </el-form-item>
        <el-form-item label="收款姓名" prop="payeeName">
          <el-input v-model="payoutForm.payeeName" placeholder="请输入收款人姓名" />
        </el-form-item>
        <el-form-item label="代付金额" prop="amount">
          <el-input-number
            v-model="payoutForm.amount"
            :min="0.01"
            :precision="2"
            controls-position="right"
            style="width: 100%;"
          />
          <div class="form-tip">手续费: ¥{{ calculateFee() }}，实际扣款: ¥{{ calculateTotal() }}</div>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="payoutForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </template>
    </el-dialog>
    
    <!-- 代付详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="代付详情" width="600px">
      <el-descriptions :column="2" border v-if="selectedPayout">
        <el-descriptions-item label="订单号">{{ selectedPayout.orderId }}</el-descriptions-item>
        <el-descriptions-item label="商户名称">{{ selectedPayout.merchantName }}</el-descriptions-item>
        <el-descriptions-item label="代付金额">¥{{ formatMoney(selectedPayout.amount) }}</el-descriptions-item>
        <el-descriptions-item label="手续费">¥{{ formatMoney(selectedPayout.fee) }}</el-descriptions-item>
        <el-descriptions-item label="实际金额">¥{{ formatMoney(selectedPayout.actualAmount) }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(selectedPayout.status)">
            {{ getStatusText(selectedPayout.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="收款账户">{{ selectedPayout.payeeAccount }}</el-descriptions-item>
        <el-descriptions-item label="收款姓名">{{ selectedPayout.payeeName }}</el-descriptions-item>
        <el-descriptions-item label="支付宝交易号">{{ selectedPayout.alipayTradeNo || '-' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDateTime(selectedPayout.createdAt) }}</el-descriptions-item>
        <el-descriptions-item label="完成时间">{{ selectedPayout.completedAt ? formatDateTime(selectedPayout.completedAt) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ selectedPayout.remark || '-' }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { transactionApi } from '@/api/transaction'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const userStore = useUserStore()
const loading = ref(false)
const submitting = ref(false)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const selectedPayout = ref(null)
const payoutFormRef = ref()

// 搜索表单
const searchForm = reactive({
  orderId: '',
  payeeAccount: '',
  status: '',
  dateRange: []
})

// 代付列表
const payouts = ref([])

// 分页
const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// 代付表单
const payoutForm = reactive({
  payeeAccount: '',
  payeeName: '',
  amount: 0,
  remark: ''
})

// 表单验证规则
const payoutRules = {
  payeeAccount: [
    { required: true, message: '请输入收款账户', trigger: 'blur' }
  ],
  payeeName: [
    { required: true, message: '请输入收款姓名', trigger: 'blur' }
  ],
  amount: [
    { required: true, message: '请输入代付金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '金额必须大于0', trigger: 'blur' }
  ]
}

// 计算手续费
const calculateFee = () => {
  if (!payoutForm.amount) return '0.00'
  // 假设手续费率为0.6%，最低2元
  const feeRate = 0.006
  const fee = Math.max(payoutForm.amount * feeRate, 2)
  return fee.toFixed(2)
}

// 计算总金额
const calculateTotal = () => {
  if (!payoutForm.amount) return '0.00'
  const fee = parseFloat(calculateFee())
  return (payoutForm.amount + fee).toFixed(2)
}

// 获取代付列表
const fetchPayouts = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.limit,
      type: 'payout',
      ...searchForm
    }
    
    // 处理日期范围
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = searchForm.dateRange[0]
      params.endDate = searchForm.dateRange[1]
    }
    delete params.dateRange
    
    const response = await transactionApi.getTransactions(params)
    payouts.value = response.data.items
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取代付列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchPayouts()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    orderId: '',
    payeeAccount: '',
    status: '',
    dateRange: []
  })
  handleSearch()
}

// 查看代付详情
const viewPayout = async (payout) => {
  try {
    const response = await transactionApi.getTransaction(payout.id)
    selectedPayout.value = response.data
    showDetailDialog.value = true
  } catch (error) {
    ElMessage.error('获取代付详情失败')
  }
}

// 查询代付状态
const queryStatus = async (payout) => {
  try {
    await transactionApi.queryPayoutStatus(payout.orderId)
    ElMessage.success('状态查询成功')
    fetchPayouts() // 刷新列表
  } catch (error) {
    ElMessage.error('状态查询失败')
  }
}

// 提交代付
const handleSubmit = async () => {
  if (!payoutFormRef.value) return
  
  try {
    await payoutFormRef.value.validate()
    submitting.value = true
    
    const payoutData = {
      ...payoutForm,
      amount: Math.round(payoutForm.amount * 100) // 转换为分
    }
    
    await transactionApi.createPayout(payoutData)
    ElMessage.success('代付发起成功')
    showCreateDialog.value = false
    resetForm()
    fetchPayouts()
  } catch (error) {
    ElMessage.error('代付发起失败')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(payoutForm, {
    payeeAccount: '',
    payeeName: '',
    amount: 0,
    remark: ''
  })
  payoutFormRef.value?.resetFields()
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.limit = size
  pagination.page = 1
  fetchPayouts()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchPayouts()
}

// 格式化金额
const formatMoney = (amount) => {
  return (amount / 100).toFixed(2)
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'success': 'success',
    'failed': 'danger',
    'cancelled': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '处理中',
    'success': '成功',
    'failed': '失败',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

onMounted(() => {
  fetchPayouts()
})
</script>

<style scoped>
.payout-management {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  color: #303133;
  margin: 0;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style>
