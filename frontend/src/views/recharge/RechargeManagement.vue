<template>
  <div class="recharge-management">
    <div class="page-header">
      <h1>充值管理</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        发起充值
      </el-button>
    </div>
    
    <!-- 余额信息 -->
    <el-row :gutter="20" class="balance-info">
      <el-col :span="8">
        <el-card class="balance-card">
          <div class="balance-item">
            <div class="balance-label">当前余额</div>
            <div class="balance-value">¥{{ formatMoney(userStore.user?.balance || 0) }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="balance-card">
          <div class="balance-item">
            <div class="balance-label">今日充值</div>
            <div class="balance-value">¥{{ formatMoney(todayRecharge) }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="balance-card">
          <div class="balance-item">
            <div class="balance-label">总充值金额</div>
            <div class="balance-value">¥{{ formatMoney(totalRecharge) }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="订单号">
          <el-input
            v-model="searchForm.orderId"
            placeholder="请输入订单号"
            clearable
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="待支付" value="pending" />
            <el-option label="支付成功" value="success" />
            <el-option label="支付失败" value="failed" />
            <el-option label="已过期" value="expired" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 充值订单列表 -->
    <el-card class="table-card">
      <el-table :data="rechargeOrders" style="width: 100%" v-loading="loading">
        <el-table-column prop="orderId" label="订单号" width="200" />
        <el-table-column prop="amount" label="充值金额" width="120">
          <template #default="{ row }">
            ¥{{ formatMoney(row.amount) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="expireTime" label="过期时间" width="180">
          <template #default="{ row }">
            {{ row.expireTime ? formatDateTime(row.expireTime) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="completedAt" label="完成时间" width="180">
          <template #default="{ row }">
            {{ row.completedAt ? formatDateTime(row.completedAt) : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewQrCode(row)" v-if="row.status === 'pending'">
              查看二维码
            </el-button>
            <el-button size="small" type="primary" @click="queryStatus(row)" v-if="row.status === 'pending'">
              查询状态
            </el-button>
            <el-button size="small" @click="viewDetail(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 发起充值对话框 -->
    <el-dialog v-model="showCreateDialog" title="发起充值" width="500px">
      <el-form
        ref="rechargeFormRef"
        :model="rechargeForm"
        :rules="rechargeRules"
        label-width="100px"
      >
        <el-form-item label="充值金额" prop="amount">
          <el-input-number
            v-model="rechargeForm.amount"
            :min="0.01"
            :max="50000"
            :precision="2"
            controls-position="right"
            style="width: 100%;"
          />
          <div class="form-tip">单笔充值金额范围：0.01 - 50,000.00 元</div>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="rechargeForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </template>
    </el-dialog>
    
    <!-- 二维码显示对话框 -->
    <el-dialog v-model="showQrDialog" title="支付二维码" width="400px">
      <div class="qr-container" v-if="currentOrder">
        <div class="qr-info">
          <p><strong>订单号：</strong>{{ currentOrder.orderId }}</p>
          <p><strong>充值金额：</strong>¥{{ formatMoney(currentOrder.amount) }}</p>
          <p><strong>过期时间：</strong>{{ formatDateTime(currentOrder.expireTime) }}</p>
        </div>
        
        <div class="qr-code">
          <el-image
            :src="currentOrder.qrCodeUrl"
            alt="支付二维码"
            style="width: 200px; height: 200px;"
            fit="contain"
          />
        </div>
        
        <div class="qr-tips">
          <el-alert
            title="请使用支付宝扫码支付"
            type="info"
            :closable="false"
            show-icon
          />
          <div class="countdown" v-if="countdown > 0">
            二维码将在 {{ Math.floor(countdown / 60) }}:{{ String(countdown % 60).padStart(2, '0') }} 后过期
          </div>
        </div>
        
        <div class="qr-actions">
          <el-button @click="refreshQrCode" :loading="refreshing">刷新二维码</el-button>
          <el-button type="primary" @click="checkPaymentStatus" :loading="checking">
            检查支付状态
          </el-button>
        </div>
      </div>
    </el-dialog>
    
    <!-- 订单详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="充值详情" width="600px">
      <el-descriptions :column="2" border v-if="selectedOrder">
        <el-descriptions-item label="订单号">{{ selectedOrder.orderId }}</el-descriptions-item>
        <el-descriptions-item label="充值金额">¥{{ formatMoney(selectedOrder.amount) }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(selectedOrder.status)">
            {{ getStatusText(selectedOrder.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="支付宝交易号">{{ selectedOrder.alipayTradeNo || '-' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDateTime(selectedOrder.createdAt) }}</el-descriptions-item>
        <el-descriptions-item label="过期时间">{{ selectedOrder.expireTime ? formatDateTime(selectedOrder.expireTime) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="完成时间">{{ selectedOrder.completedAt ? formatDateTime(selectedOrder.completedAt) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ selectedOrder.remark || '-' }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { transactionApi } from '@/api/transaction'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const userStore = useUserStore()
const loading = ref(false)
const submitting = ref(false)
const refreshing = ref(false)
const checking = ref(false)
const showCreateDialog = ref(false)
const showQrDialog = ref(false)
const showDetailDialog = ref(false)
const currentOrder = ref(null)
const selectedOrder = ref(null)
const rechargeFormRef = ref()
const countdown = ref(0)
let countdownTimer = null

// 统计数据
const todayRecharge = ref(0)
const totalRecharge = ref(0)

// 搜索表单
const searchForm = reactive({
  orderId: '',
  status: '',
  dateRange: []
})

// 充值订单列表
const rechargeOrders = ref([])

// 分页
const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// 充值表单
const rechargeForm = reactive({
  amount: 0,
  remark: ''
})

// 表单验证规则
const rechargeRules = {
  amount: [
    { required: true, message: '请输入充值金额', trigger: 'blur' },
    { type: 'number', min: 0.01, max: 50000, message: '充值金额范围为0.01-50000元', trigger: 'blur' }
  ]
}

// 获取充值订单列表
const fetchRechargeOrders = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.limit,
      type: 'recharge',
      ...searchForm
    }
    
    // 处理日期范围
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = searchForm.dateRange[0]
      params.endDate = searchForm.dateRange[1]
    }
    delete params.dateRange
    
    const response = await transactionApi.getTransactions(params)
    rechargeOrders.value = response.data.items
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取充值订单失败')
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const fetchStats = async () => {
  try {
    const response = await transactionApi.getRechargeStats()
    todayRecharge.value = response.data.todayAmount
    totalRecharge.value = response.data.totalAmount
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchRechargeOrders()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    orderId: '',
    status: '',
    dateRange: []
  })
  handleSearch()
}

// 提交充值
const handleSubmit = async () => {
  if (!rechargeFormRef.value) return
  
  try {
    await rechargeFormRef.value.validate()
    submitting.value = true
    
    const rechargeData = {
      ...rechargeForm,
      amount: Math.round(rechargeForm.amount * 100) // 转换为分
    }
    
    const response = await transactionApi.createRecharge(rechargeData)
    ElMessage.success('充值订单创建成功')
    showCreateDialog.value = false
    resetForm()
    fetchRechargeOrders()
    
    // 直接显示二维码
    currentOrder.value = response.data
    showQrDialog.value = true
    startCountdown()
  } catch (error) {
    ElMessage.error('创建充值订单失败')
  } finally {
    submitting.value = false
  }
}

// 查看二维码
const viewQrCode = async (order) => {
  try {
    const response = await transactionApi.getPaymentQrCode(order.orderId)
    currentOrder.value = {
      ...order,
      qrCodeUrl: response.data.qrCodeUrl
    }
    showQrDialog.value = true
    startCountdown()
  } catch (error) {
    ElMessage.error('获取二维码失败')
  }
}

// 查询支付状态
const queryStatus = async (order) => {
  try {
    await transactionApi.queryPaymentStatus(order.orderId)
    ElMessage.success('状态查询成功')
    fetchRechargeOrders() // 刷新列表
  } catch (error) {
    ElMessage.error('状态查询失败')
  }
}

// 查看详情
const viewDetail = async (order) => {
  try {
    const response = await transactionApi.getTransaction(order.id)
    selectedOrder.value = response.data
    showDetailDialog.value = true
  } catch (error) {
    ElMessage.error('获取订单详情失败')
  }
}

// 刷新二维码
const refreshQrCode = async () => {
  if (!currentOrder.value) return
  
  refreshing.value = true
  try {
    const response = await transactionApi.refreshPaymentQrCode(currentOrder.value.orderId)
    currentOrder.value.qrCodeUrl = response.data.qrCodeUrl
    currentOrder.value.expireTime = response.data.expireTime
    ElMessage.success('二维码刷新成功')
    startCountdown()
  } catch (error) {
    ElMessage.error('刷新二维码失败')
  } finally {
    refreshing.value = false
  }
}

// 检查支付状态
const checkPaymentStatus = async () => {
  if (!currentOrder.value) return
  
  checking.value = true
  try {
    const response = await transactionApi.queryPaymentStatus(currentOrder.value.orderId)
    if (response.data.status === 'success') {
      ElMessage.success('支付成功！')
      showQrDialog.value = false
      fetchRechargeOrders()
      userStore.fetchUserInfo() // 刷新用户余额
    } else {
      ElMessage.info('订单尚未支付')
    }
  } catch (error) {
    ElMessage.error('查询支付状态失败')
  } finally {
    checking.value = false
  }
}

// 开始倒计时
const startCountdown = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
  
  if (currentOrder.value?.expireTime) {
    const expireTime = new Date(currentOrder.value.expireTime).getTime()
    const updateCountdown = () => {
      const now = Date.now()
      const remaining = Math.max(0, Math.floor((expireTime - now) / 1000))
      countdown.value = remaining
      
      if (remaining <= 0) {
        clearInterval(countdownTimer)
        ElMessage.warning('二维码已过期，请刷新二维码')
      }
    }
    
    updateCountdown()
    countdownTimer = setInterval(updateCountdown, 1000)
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(rechargeForm, {
    amount: 0,
    remark: ''
  })
  rechargeFormRef.value?.resetFields()
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.limit = size
  pagination.page = 1
  fetchRechargeOrders()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchRechargeOrders()
}

// 格式化金额
const formatMoney = (amount) => {
  return (amount / 100).toFixed(2)
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'success': 'success',
    'failed': 'danger',
    'expired': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '待支付',
    'success': '支付成功',
    'failed': '支付失败',
    'expired': '已过期'
  }
  return statusMap[status] || status
}

onMounted(() => {
  fetchRechargeOrders()
  fetchStats()
})

onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
})
</script>

<style scoped>
.recharge-management {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  color: #303133;
  margin: 0;
}

.balance-info {
  margin-bottom: 20px;
}

.balance-card {
  text-align: center;
}

.balance-item {
  padding: 10px 0;
}

.balance-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.balance-value {
  font-size: 24px;
  font-weight: 600;
  color: #67C23A;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.qr-container {
  text-align: center;
}

.qr-info {
  margin-bottom: 20px;
  text-align: left;
}

.qr-code {
  margin: 20px 0;
}

.qr-tips {
  margin: 20px 0;
}

.countdown {
  margin-top: 10px;
  font-size: 14px;
  color: #E6A23C;
  font-weight: 600;
}

.qr-actions {
  margin-top: 20px;
}

.qr-actions .el-button {
  margin: 0 5px;
}
</style>
