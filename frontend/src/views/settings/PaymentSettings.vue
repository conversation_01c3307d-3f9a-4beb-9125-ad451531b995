<template>
  <div class="payment-settings">
    <div class="page-header">
      <h1>支付设置</h1>
    </div>
    
    <!-- 支付宝配置 -->
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>支付宝配置</span>
          <el-switch
            v-model="isEditMode"
            active-text="编辑模式"
            inactive-text="查看模式"
          />
        </div>
      </template>
      
      <el-form
        ref="configFormRef"
        :model="configForm"
        :rules="configRules"
        label-width="120px"
        :disabled="!isEditMode"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="环境设置" prop="environment">
              <el-radio-group v-model="configForm.environment">
                <el-radio value="sandbox">沙箱环境</el-radio>
                <el-radio value="production">正式环境</el-radio>
              </el-radio-group>
              <div class="form-tip">
                沙箱环境用于测试，正式环境用于生产
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-tag :type="configForm.status === 'active' ? 'success' : 'danger'">
                {{ configForm.status === 'active' ? '启用' : '禁用' }}
              </el-tag>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="应用ID" prop="appId">
          <el-input
            v-model="configForm.appId"
            placeholder="请输入支付宝应用ID"
            show-word-limit
            maxlength="32"
          />
        </el-form-item>
        
        <el-form-item label="网关地址" prop="gatewayUrl">
          <el-input
            v-model="configForm.gatewayUrl"
            placeholder="支付宝网关地址"
            readonly
          />
          <div class="form-tip">
            {{ configForm.environment === 'sandbox' ? '沙箱网关地址' : '正式网关地址' }}
          </div>
        </el-form-item>
        
        <el-form-item label="应用私钥" prop="privateKey">
          <el-input
            v-model="configForm.privateKey"
            type="textarea"
            :rows="6"
            placeholder="请输入应用私钥（PKCS8格式）"
            show-word-limit
          />
          <div class="form-tip">
            请确保私钥格式正确，包含完整的BEGIN和END标记
          </div>
        </el-form-item>
        
        <el-form-item label="支付宝公钥" prop="alipayPublicKey">
          <el-input
            v-model="configForm.alipayPublicKey"
            type="textarea"
            :rows="6"
            placeholder="请输入支付宝公钥"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="应用公钥证书" prop="appCertPath">
          <el-input
            v-model="configForm.appCertPath"
            placeholder="应用公钥证书路径（可选）"
          />
          <div class="form-tip">
            使用公钥证书模式时需要配置
          </div>
        </el-form-item>
        
        <el-form-item label="支付宝根证书" prop="alipayRootCertPath">
          <el-input
            v-model="configForm.alipayRootCertPath"
            placeholder="支付宝根证书路径（可选）"
          />
        </el-form-item>
        
        <el-form-item label="支付宝公钥证书" prop="alipayCertPath">
          <el-input
            v-model="configForm.alipayCertPath"
            placeholder="支付宝公钥证书路径（可选）"
          />
        </el-form-item>
        
        <el-form-item label="签名类型" prop="signType">
          <el-select v-model="configForm.signType" placeholder="请选择签名类型">
            <el-option label="RSA2" value="RSA2" />
            <el-option label="RSA" value="RSA" />
          </el-select>
          <div class="form-tip">
            推荐使用RSA2签名类型，安全性更高
          </div>
        </el-form-item>
        
        <el-form-item label="字符编码" prop="charset">
          <el-select v-model="configForm.charset" placeholder="请选择字符编码">
            <el-option label="UTF-8" value="utf-8" />
            <el-option label="GBK" value="gbk" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="数据格式" prop="format">
          <el-select v-model="configForm.format" placeholder="请选择数据格式">
            <el-option label="JSON" value="json" />
            <el-option label="XML" value="xml" />
          </el-select>
        </el-form-item>
        
        <el-form-item v-if="isEditMode">
          <el-button type="primary" @click="handleSave" :loading="saving">
            保存配置
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="testConnection" :loading="testing">
            测试连接
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 配置历史 -->
    <el-card class="history-card">
      <template #header>
        <span>配置历史</span>
      </template>
      
      <el-table :data="configHistory" style="width: 100%">
        <el-table-column prop="version" label="版本" width="80" />
        <el-table-column prop="environment" label="环境" width="100">
          <template #default="{ row }">
            <el-tag :type="row.environment === 'production' ? 'success' : 'warning'">
              {{ row.environment === 'production' ? '正式' : '沙箱' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="appId" label="应用ID" width="200" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="updatedBy" label="更新人" width="120" />
        <el-table-column prop="updatedAt" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.updatedAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" />
        <el-table-column label="操作" width="100">
          <template #default="{ row }">
            <el-button size="small" @click="rollbackConfig(row)">
              回滚
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { settingsApi } from '@/api/settings'
import { ElMessage, ElMessageBox } from 'element-plus'

const isEditMode = ref(false)
const saving = ref(false)
const testing = ref(false)
const configFormRef = ref()

// 配置表单
const configForm = reactive({
  environment: 'sandbox',
  appId: '',
  gatewayUrl: '',
  privateKey: '',
  alipayPublicKey: '',
  appCertPath: '',
  alipayRootCertPath: '',
  alipayCertPath: '',
  signType: 'RSA2',
  charset: 'utf-8',
  format: 'json',
  status: 'active'
})

// 配置历史
const configHistory = ref([])

// 表单验证规则
const configRules = {
  environment: [
    { required: true, message: '请选择环境', trigger: 'change' }
  ],
  appId: [
    { required: true, message: '请输入应用ID', trigger: 'blur' },
    { min: 16, max: 32, message: '应用ID长度应为16-32位', trigger: 'blur' }
  ],
  privateKey: [
    { required: true, message: '请输入应用私钥', trigger: 'blur' }
  ],
  alipayPublicKey: [
    { required: true, message: '请输入支付宝公钥', trigger: 'blur' }
  ],
  signType: [
    { required: true, message: '请选择签名类型', trigger: 'change' }
  ],
  charset: [
    { required: true, message: '请选择字符编码', trigger: 'change' }
  ],
  format: [
    { required: true, message: '请选择数据格式', trigger: 'change' }
  ]
}

// 监听环境变化，自动更新网关地址
watch(() => configForm.environment, (newEnv) => {
  if (newEnv === 'sandbox') {
    configForm.gatewayUrl = 'https://openapi.alipaydev.com/gateway.do'
  } else {
    configForm.gatewayUrl = 'https://openapi.alipay.com/gateway.do'
  }
})

// 获取支付配置
const fetchPaymentConfig = async () => {
  try {
    const response = await settingsApi.getPaymentConfig()
    Object.assign(configForm, response.data)
  } catch (error) {
    ElMessage.error('获取支付配置失败')
  }
}

// 获取配置历史
const fetchConfigHistory = async () => {
  try {
    const response = await settingsApi.getConfigHistory()
    configHistory.value = response.data
  } catch (error) {
    ElMessage.error('获取配置历史失败')
  }
}

// 保存配置
const handleSave = async () => {
  if (!configFormRef.value) return
  
  try {
    await configFormRef.value.validate()
    saving.value = true
    
    await settingsApi.updatePaymentConfig(configForm)
    ElMessage.success('配置保存成功')
    isEditMode.value = false
    fetchConfigHistory() // 刷新历史记录
  } catch (error) {
    ElMessage.error('配置保存失败')
  } finally {
    saving.value = false
  }
}

// 重置配置
const handleReset = () => {
  fetchPaymentConfig()
  configFormRef.value?.resetFields()
}

// 测试连接
const testConnection = async () => {
  if (!configFormRef.value) return
  
  try {
    await configFormRef.value.validate()
    testing.value = true
    
    await settingsApi.testPaymentConnection(configForm)
    ElMessage.success('连接测试成功')
  } catch (error) {
    ElMessage.error('连接测试失败: ' + (error.response?.data?.message || error.message))
  } finally {
    testing.value = false
  }
}

// 回滚配置
const rollbackConfig = async (historyConfig) => {
  try {
    await ElMessageBox.confirm('确定要回滚到此配置版本吗？', '确认回滚', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await settingsApi.rollbackConfig(historyConfig.id)
    ElMessage.success('配置回滚成功')
    fetchPaymentConfig()
    fetchConfigHistory()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('配置回滚失败')
    }
  }
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchPaymentConfig()
  fetchConfigHistory()
})
</script>

<style scoped>
.payment-settings {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  color: #303133;
  margin: 0;
}

.config-card {
  margin-bottom: 20px;
}

.history-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style>
