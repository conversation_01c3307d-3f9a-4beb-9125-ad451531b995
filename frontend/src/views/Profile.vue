<template>
  <div class="profile">
    <div class="page-header">
      <h1>个人中心</h1>
    </div>
    
    <el-row :gutter="20">
      <!-- 基本信息 -->
      <el-col :span="12">
        <el-card class="profile-card">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
              <el-button type="primary" size="small" @click="editProfile">编辑</el-button>
            </div>
          </template>
          
          <el-descriptions :column="1" border>
            <el-descriptions-item label="用户名">{{ userStore.user?.username }}</el-descriptions-item>
            <el-descriptions-item label="邮箱">{{ userStore.user?.email }}</el-descriptions-item>
            <el-descriptions-item label="角色">
              <el-tag :type="userStore.isPlatformAdmin ? 'danger' : 'primary'">
                {{ userStore.isPlatformAdmin ? '平台管理员' : '商户管理员' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="商户名称" v-if="userStore.isMerchantAdmin">
              {{ userStore.user?.merchantName }}
            </el-descriptions-item>
            <el-descriptions-item label="联系电话" v-if="userStore.isMerchantAdmin">
              {{ userStore.user?.phone }}
            </el-descriptions-item>
            <el-descriptions-item label="注册时间">
              {{ formatDateTime(userStore.user?.createdAt) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
      
      <!-- 账户信息 -->
      <el-col :span="12" v-if="userStore.isMerchantAdmin">
        <el-card class="profile-card">
          <template #header>
            <span>账户信息</span>
          </template>
          
          <el-descriptions :column="1" border>
            <el-descriptions-item label="账户余额">
              <span class="balance-amount">¥{{ formatMoney(accountInfo.balance) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="费率">{{ accountInfo.feeRate }}%</el-descriptions-item>
            <el-descriptions-item label="账户状态">
              <el-tag :type="accountInfo.status === 'active' ? 'success' : 'danger'">
                {{ accountInfo.status === 'active' ? '正常' : '禁用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="今日交易">{{ accountInfo.todayTransactions }}笔</el-descriptions-item>
            <el-descriptions-item label="今日交易额">¥{{ formatMoney(accountInfo.todayAmount) }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 修改密码 -->
    <el-card class="profile-card">
      <template #header>
        <span>修改密码</span>
      </template>
      
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="120px"
        style="max-width: 500px;"
      >
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input
            v-model="passwordForm.currentPassword"
            type="password"
            placeholder="请输入当前密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="changePassword" :loading="passwordLoading">
            修改密码
          </el-button>
          <el-button @click="resetPasswordForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 编辑资料对话框 -->
    <el-dialog v-model="showEditDialog" title="编辑资料" width="500px">
      <el-form
        ref="profileFormRef"
        :model="profileForm"
        :rules="profileRules"
        label-width="100px"
      >
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="profileForm.email" />
        </el-form-item>
        <el-form-item label="商户名称" prop="merchantName" v-if="userStore.isMerchantAdmin">
          <el-input v-model="profileForm.merchantName" />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone" v-if="userStore.isMerchantAdmin">
          <el-input v-model="profileForm.phone" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="updateProfile" :loading="profileLoading">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { authApi } from '@/api/auth'
import { ElMessage } from 'element-plus'

const userStore = useUserStore()
const passwordFormRef = ref()
const profileFormRef = ref()
const passwordLoading = ref(false)
const profileLoading = ref(false)
const showEditDialog = ref(false)

// 账户信息
const accountInfo = reactive({
  balance: 0,
  feeRate: 0,
  status: 'active',
  todayTransactions: 0,
  todayAmount: 0
})

// 修改密码表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 编辑资料表单
const profileForm = reactive({
  email: '',
  merchantName: '',
  phone: ''
})

// 确认密码验证
const validateConfirmPassword = (rule, value, callback) => {
  if (value !== passwordForm.newPassword) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 密码表单验证规则
const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 资料表单验证规则
const profileRules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  merchantName: [
    { required: true, message: '请输入商户名称', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ]
}

// 获取账户信息
const fetchAccountInfo = async () => {
  if (!userStore.isMerchantAdmin) return
  
  try {
    const response = await authApi.getProfile()
    Object.assign(accountInfo, response.data.accountInfo)
  } catch (error) {
    console.error('获取账户信息失败:', error)
  }
}

// 编辑资料
const editProfile = () => {
  Object.assign(profileForm, {
    email: userStore.user?.email || '',
    merchantName: userStore.user?.merchantName || '',
    phone: userStore.user?.phone || ''
  })
  showEditDialog.value = true
}

// 更新资料
const updateProfile = async () => {
  if (!profileFormRef.value) return
  
  try {
    await profileFormRef.value.validate()
    profileLoading.value = true
    
    await authApi.updateProfile(profileForm)
    await userStore.fetchUserInfo() // 刷新用户信息
    
    ElMessage.success('资料更新成功')
    showEditDialog.value = false
  } catch (error) {
    ElMessage.error('资料更新失败')
  } finally {
    profileLoading.value = false
  }
}

// 修改密码
const changePassword = async () => {
  if (!passwordFormRef.value) return
  
  try {
    await passwordFormRef.value.validate()
    passwordLoading.value = true
    
    await authApi.changePassword({
      currentPassword: passwordForm.currentPassword,
      newPassword: passwordForm.newPassword
    })
    
    ElMessage.success('密码修改成功')
    resetPasswordForm()
  } catch (error) {
    ElMessage.error('密码修改失败')
  } finally {
    passwordLoading.value = false
  }
}

// 重置密码表单
const resetPasswordForm = () => {
  Object.assign(passwordForm, {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  passwordFormRef.value?.resetFields()
}

// 格式化金额
const formatMoney = (amount) => {
  return (amount / 100).toFixed(2)
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchAccountInfo()
})
</script>

<style scoped>
.profile {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  color: #303133;
  margin: 0;
}

.profile-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.balance-amount {
  font-size: 18px;
  font-weight: 600;
  color: #67C23A;
}
</style>
