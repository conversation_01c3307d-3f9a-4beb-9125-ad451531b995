<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>仪表盘</h1>
      <p>欢迎回来，{{ userStore.user?.username }}</p>
    </div>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon color="#409EFF" size="40"><Money /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">¥{{ formatMoney(stats.totalAmount) }}</div>
              <div class="stats-label">总交易金额</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon color="#67C23A" size="40"><Document /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ stats.totalTransactions }}</div>
              <div class="stats-label">总交易笔数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6" v-if="userStore.isPlatformAdmin">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon color="#E6A23C" size="40"><Shop /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ stats.totalMerchants }}</div>
              <div class="stats-label">商户总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon color="#F56C6C" size="40"><Wallet /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">¥{{ formatMoney(stats.balance) }}</div>
              <div class="stats-label">账户余额</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 最近交易 -->
    <el-card class="recent-transactions">
      <template #header>
        <div class="card-header">
          <span>最近交易</span>
          <el-button type="primary" size="small" @click="$router.push('/transactions')">
            查看全部
          </el-button>
        </div>
      </template>
      
      <el-table :data="recentTransactions" style="width: 100%" v-loading="loading">
        <el-table-column prop="orderId" label="订单号" width="200" />
        <el-table-column prop="amount" label="金额" width="120">
          <template #default="{ row }">
            ¥{{ formatMoney(row.amount) }}
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.type === 'payment' ? 'success' : 'warning'">
              {{ row.type === 'payment' ? '收款' : '代付' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { statisticsApi } from '@/api/statistics'
import { ElMessage } from 'element-plus'
import { transactionApi } from '@/api/transaction'
import { merchantApi } from '@/api/merchant'
import { Money, Document, Shop, Wallet } from '@element-plus/icons-vue'

const userStore = useUserStore()
const loading = ref(false)

// 统计数据
const stats = reactive({
  totalAmount: 0,
  totalTransactions: 0,
  totalMerchants: 0,
  balance: 0,
  todayTransactions: 0,
  todayAmount: 0,
  successRate: 0,
  pendingPayouts: 0
})

// 最近交易
const recentTransactions = ref([])

// 获取统计数据
const fetchStats = async () => {
  try {
    const response = await statisticsApi.getDashboardStats()
    Object.assign(stats, response.data)
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')

    // 使用默认数据
    Object.assign(stats, {
      totalAmount: 0,
      totalTransactions: 0,
      totalMerchants: 0,
      balance: userStore.user?.balance || 0,
      todayTransactions: 0,
      todayAmount: 0,
      successRate: 0,
      pendingPayouts: 0
    })
  }
}

// 获取最近交易
const fetchRecentTransactions = async () => {
  loading.value = true
  try {
    const response = await transactionApi.getTransactions({ 
      page: 1, 
      limit: 10,
      sort: 'createdAt',
      order: 'desc'
    })
    recentTransactions.value = response.data.items
  } catch (error) {
    console.error('获取最近交易失败:', error)
  } finally {
    loading.value = false
  }
}

// 格式化金额
const formatMoney = (amount) => {
  return (amount / 100).toFixed(2)
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'success': 'success',
    'failed': 'danger',
    'cancelled': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '处理中',
    'success': '成功',
    'failed': '失败',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

onMounted(() => {
  fetchStats()
  fetchRecentTransactions()
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.dashboard-header {
  margin-bottom: 20px;
}

.dashboard-header h1 {
  color: #303133;
  margin-bottom: 8px;
}

.dashboard-header p {
  color: #909399;
  font-size: 14px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  margin-right: 20px;
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.recent-transactions {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
