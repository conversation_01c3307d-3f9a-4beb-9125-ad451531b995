<template>
  <div class="transaction-list">
    <div class="page-header">
      <h1>交易记录</h1>
      <el-button type="primary" @click="exportTransactions">
        <el-icon><Download /></el-icon>
        导出记录
      </el-button>
    </div>
    
    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="订单号">
          <el-input
            v-model="searchForm.orderId"
            placeholder="请输入订单号"
            clearable
          />
        </el-form-item>
        <el-form-item label="交易类型">
          <el-select v-model="searchForm.type" placeholder="请选择类型" clearable>
            <el-option label="收款" value="payment" />
            <el-option label="代付" value="payout" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="处理中" value="pending" />
            <el-option label="成功" value="success" />
            <el-option label="失败" value="failed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 交易列表 -->
    <el-card class="table-card">
      <el-table :data="transactions" style="width: 100%" v-loading="loading">
        <el-table-column prop="orderId" label="订单号" width="200" />
        <el-table-column prop="merchantName" label="商户名称" width="150" v-if="userStore.isPlatformAdmin" />
        <el-table-column prop="amount" label="金额" width="120">
          <template #default="{ row }">
            ¥{{ formatMoney(row.amount) }}
          </template>
        </el-table-column>
        <el-table-column prop="fee" label="手续费" width="100">
          <template #default="{ row }">
            ¥{{ formatMoney(row.fee) }}
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.type === 'payment' ? 'success' : 'warning'">
              {{ row.type === 'payment' ? '收款' : '代付' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="payeeAccount" label="收款账户" width="150" />
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="completedAt" label="完成时间" width="180">
          <template #default="{ row }">
            {{ row.completedAt ? formatDateTime(row.completedAt) : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewTransaction(row)">查看详情</el-button>
            <el-button 
              size="small" 
              type="primary" 
              @click="queryStatus(row)"
              v-if="row.status === 'pending'"
            >
              查询状态
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 交易详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="交易详情" width="600px">
      <el-descriptions :column="2" border v-if="selectedTransaction">
        <el-descriptions-item label="订单号">{{ selectedTransaction.orderId }}</el-descriptions-item>
        <el-descriptions-item label="商户名称">{{ selectedTransaction.merchantName }}</el-descriptions-item>
        <el-descriptions-item label="交易金额">¥{{ formatMoney(selectedTransaction.amount) }}</el-descriptions-item>
        <el-descriptions-item label="手续费">¥{{ formatMoney(selectedTransaction.fee) }}</el-descriptions-item>
        <el-descriptions-item label="实际金额">¥{{ formatMoney(selectedTransaction.actualAmount) }}</el-descriptions-item>
        <el-descriptions-item label="交易类型">
          <el-tag :type="selectedTransaction.type === 'payment' ? 'success' : 'warning'">
            {{ selectedTransaction.type === 'payment' ? '收款' : '代付' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(selectedTransaction.status)">
            {{ getStatusText(selectedTransaction.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="收款账户">{{ selectedTransaction.payeeAccount }}</el-descriptions-item>
        <el-descriptions-item label="收款姓名">{{ selectedTransaction.payeeName }}</el-descriptions-item>
        <el-descriptions-item label="支付宝交易号">{{ selectedTransaction.alipayTradeNo || '-' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDateTime(selectedTransaction.createdAt) }}</el-descriptions-item>
        <el-descriptions-item label="完成时间">{{ selectedTransaction.completedAt ? formatDateTime(selectedTransaction.completedAt) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ selectedTransaction.remark || '-' }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { transactionApi } from '@/api/transaction'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'

const userStore = useUserStore()
const loading = ref(false)
const showDetailDialog = ref(false)
const selectedTransaction = ref(null)

// 搜索表单
const searchForm = reactive({
  orderId: '',
  type: '',
  status: '',
  dateRange: []
})

// 交易列表
const transactions = ref([])

// 分页
const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// 获取交易列表
const fetchTransactions = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.limit,
      ...searchForm
    }
    
    // 处理日期范围
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = searchForm.dateRange[0]
      params.endDate = searchForm.dateRange[1]
    }
    delete params.dateRange
    
    const response = await transactionApi.getTransactions(params)
    transactions.value = response.data.items
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取交易列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchTransactions()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    orderId: '',
    type: '',
    status: '',
    dateRange: []
  })
  handleSearch()
}

// 查看交易详情
const viewTransaction = async (transaction) => {
  try {
    const response = await transactionApi.getTransaction(transaction.id)
    selectedTransaction.value = response.data
    showDetailDialog.value = true
  } catch (error) {
    ElMessage.error('获取交易详情失败')
  }
}

// 查询交易状态
const queryStatus = async (transaction) => {
  try {
    let response
    if (transaction.type === 'payment') {
      response = await transactionApi.queryPaymentStatus(transaction.orderId)
    } else {
      response = await transactionApi.queryPayoutStatus(transaction.orderId)
    }
    
    ElMessage.success('状态查询成功')
    fetchTransactions() // 刷新列表
  } catch (error) {
    ElMessage.error('状态查询失败')
  }
}

// 导出交易记录
const exportTransactions = async () => {
  try {
    const params = { ...searchForm }
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = searchForm.dateRange[0]
      params.endDate = searchForm.dateRange[1]
    }
    delete params.dateRange
    
    const response = await transactionApi.exportTransactions(params)
    
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `交易记录_${new Date().toISOString().slice(0, 10)}.xlsx`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.limit = size
  pagination.page = 1
  fetchTransactions()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchTransactions()
}

// 格式化金额
const formatMoney = (amount) => {
  return (amount / 100).toFixed(2)
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'success': 'success',
    'failed': 'danger',
    'cancelled': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '处理中',
    'success': '成功',
    'failed': '失败',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

onMounted(() => {
  fetchTransactions()
})
</script>

<style scoped>
.transaction-list {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  color: #303133;
  margin: 0;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
