import request from './request'

export const transactionApi = {
  // 获取交易列表
  getTransactions(params = {}) {
    return request.get('/transactions', { params })
  },

  // 获取交易详情
  getTransaction(id) {
    return request.get(`/transactions/${id}`)
  },

  // 创建支付订单
  createPayment(paymentData) {
    return request.post('/transactions/payment', paymentData)
  },

  // 查询支付状态
  queryPaymentStatus(orderId) {
    return request.get(`/transactions/payment/${orderId}/status`)
  },

  // 获取支付二维码
  getPaymentQrCode(orderId) {
    return request.get(`/transactions/payment/${orderId}/qrcode`)
  },

  // 创建代付订单
  createPayout(payoutData) {
    return request.post('/transactions/payout', payoutData)
  },

  // 查询代付状态
  queryPayoutStatus(orderId) {
    return request.get(`/transactions/payout/${orderId}/status`)
  },

  // 创建充值订单
  createRecharge(rechargeData) {
    return request.post('/transactions/recharge', rechargeData)
  },

  // 刷新支付二维码
  refreshPaymentQrCode(orderId) {
    return request.post(`/transactions/payment/${orderId}/refresh-qrcode`)
  },

  // 获取充值统计
  getRechargeStats() {
    return request.get('/transactions/recharge/stats')
  },

  // 获取交易统计
  getTransactionStats(params = {}) {
    return request.get('/transactions/stats', { params })
  },

  // 导出交易记录
  exportTransactions(params = {}) {
    return request.get('/transactions/export', {
      params,
      responseType: 'blob'
    })
  }
}
