import request from './request'

export const settingsApi = {
  // 获取支付配置
  getPaymentConfig() {
    return request.get('/settings/payment')
  },

  // 更新支付配置
  updatePaymentConfig(configData) {
    return request.put('/settings/payment', configData)
  },

  // 测试支付连接
  testPaymentConnection(configData) {
    return request.post('/settings/payment/test', configData)
  },

  // 获取配置历史
  getConfigHistory() {
    return request.get('/settings/payment/history')
  },

  // 回滚配置
  rollbackConfig(historyId) {
    return request.post(`/settings/payment/rollback/${historyId}`)
  },

  // 获取系统设置
  getSystemSettings() {
    return request.get('/settings/system')
  },

  // 更新系统设置
  updateSystemSettings(settingsData) {
    return request.put('/settings/system', settingsData)
  },

  // 获取手续费设置
  getFeeSettings() {
    return request.get('/settings/fee')
  },

  // 更新手续费设置
  updateFeeSettings(feeData) {
    return request.put('/settings/fee', feeData)
  }
}
