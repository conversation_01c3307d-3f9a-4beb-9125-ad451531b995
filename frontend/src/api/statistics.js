import request from './request'

export const statisticsApi = {
  // 获取仪表盘统计数据
  getDashboardStats() {
    return request.get('/statistics/dashboard')
  },

  // 获取交易统计
  getTransactionStats(params = {}) {
    return request.get('/statistics/transactions', { params })
  },

  // 获取充值统计
  getRechargeStats(params = {}) {
    return request.get('/statistics/recharge', { params })
  },

  // 获取代付统计
  getPayoutStats(params = {}) {
    return request.get('/statistics/payouts', { params })
  },

  // 获取商户统计（平台管理员）
  getMerchantStats(params = {}) {
    return request.get('/statistics/merchants', { params })
  },

  // 获取收入统计
  getRevenueStats(params = {}) {
    return request.get('/statistics/revenue', { params })
  },

  // 获取趋势数据
  getTrendData(params = {}) {
    return request.get('/statistics/trends', { params })
  },

  // 获取实时统计
  getRealTimeStats() {
    return request.get('/statistics/realtime')
  },

  // 获取月度报告
  getMonthlyReport(year, month) {
    return request.get(`/statistics/monthly-report/${year}/${month}`)
  },

  // 获取年度报告
  getYearlyReport(year) {
    return request.get(`/statistics/yearly-report/${year}`)
  },

  // 获取热门商户排行
  getTopMerchants(params = {}) {
    return request.get('/statistics/top-merchants', { params })
  },

  // 获取交易量分析
  getVolumeAnalysis(params = {}) {
    return request.get('/statistics/volume-analysis', { params })
  },

  // 获取成功率统计
  getSuccessRateStats(params = {}) {
    return request.get('/statistics/success-rate', { params })
  },

  // 获取手续费统计
  getFeeStats(params = {}) {
    return request.get('/statistics/fees', { params })
  },

  // 获取用户活跃度统计
  getUserActivityStats(params = {}) {
    return request.get('/statistics/user-activity', { params })
  },

  // 获取地区分布统计
  getRegionStats(params = {}) {
    return request.get('/statistics/regions', { params })
  },

  // 获取设备统计
  getDeviceStats(params = {}) {
    return request.get('/statistics/devices', { params })
  },

  // 获取异常交易统计
  getAnomalyStats(params = {}) {
    return request.get('/statistics/anomalies', { params })
  },

  // 导出统计报告
  exportReport(reportType, params = {}) {
    return request.get(`/statistics/export/${reportType}`, {
      params,
      responseType: 'blob'
    })
  }
}
