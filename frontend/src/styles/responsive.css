/* 响应式设计样式 */

/* 移动端适配 */
@media (max-width: 768px) {
  /* 布局调整 */
  .el-aside {
    width: 200px !important;
  }
  
  .sidebar-menu .el-menu-item {
    padding: 0 15px !important;
  }
  
  .sidebar-menu .el-menu-item span {
    font-size: 14px;
  }
  
  /* 头部调整 */
  .header {
    padding: 0 15px !important;
  }
  
  .header-left .el-breadcrumb {
    display: none;
  }
  
  .user-info .username {
    display: none;
  }
  
  /* 主内容区调整 */
  .main-content {
    padding: 15px !important;
  }
  
  /* 页面头部 */
  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch !important;
  }
  
  .page-header h1 {
    font-size: 20px;
    margin-bottom: 10px;
  }
  
  /* 卡片间距 */
  .el-card {
    margin-bottom: 15px;
  }
  
  /* 表格调整 */
  .el-table {
    font-size: 12px;
  }
  
  .el-table .el-table__cell {
    padding: 8px 5px;
  }
  
  /* 表单调整 */
  .el-form--inline .el-form-item {
    display: block;
    margin-bottom: 15px;
  }
  
  .el-form--inline .el-form-item__content {
    margin-left: 0 !important;
  }
  
  /* 按钮组调整 */
  .el-button-group .el-button {
    padding: 8px 12px;
    font-size: 12px;
  }
  
  /* 分页调整 */
  .el-pagination {
    text-align: center;
  }
  
  .el-pagination .el-pagination__sizes {
    display: none;
  }
  
  .el-pagination .el-pagination__jump {
    display: none;
  }
  
  /* 对话框调整 */
  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }
  
  .el-dialog__body {
    padding: 15px;
  }
  
  /* 描述列表调整 */
  .el-descriptions {
    font-size: 12px;
  }
  
  .el-descriptions--small .el-descriptions__label {
    width: 80px;
  }
  
  /* 统计卡片调整 */
  .balance-info .el-col {
    margin-bottom: 15px;
  }
  
  .balance-card {
    text-align: center;
  }
  
  .balance-value {
    font-size: 20px !important;
  }
  
  /* 二维码支付调整 */
  .qr-payment {
    margin: 0;
    border-radius: 0;
  }
  
  .qr-code {
    width: 180px !important;
    height: 180px !important;
  }
  
  .payment-actions .el-button {
    min-width: 80px;
    font-size: 12px;
    padding: 8px 12px;
  }
  
  /* 搜索表单调整 */
  .search-card .el-form-item {
    margin-bottom: 15px;
  }
  
  .search-card .el-form-item__label {
    width: 80px !important;
  }
}

/* 平板端适配 */
@media (min-width: 769px) and (max-width: 1024px) {
  .el-aside {
    width: 220px !important;
  }
  
  .main-content {
    padding: 20px;
  }
  
  .page-header h1 {
    font-size: 22px;
  }
  
  .el-table {
    font-size: 13px;
  }
  
  .balance-value {
    font-size: 22px !important;
  }
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
  .main-content {
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .page-header {
    margin-bottom: 30px;
  }
  
  .el-card {
    margin-bottom: 25px;
  }
  
  .balance-value {
    font-size: 28px !important;
  }
}

/* 通用响应式类 */
.responsive-grid {
  display: grid;
  gap: 20px;
}

@media (max-width: 768px) {
  .responsive-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1025px) {
  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 隐藏/显示类 */
.mobile-only {
  display: none;
}

.desktop-only {
  display: block;
}

@media (max-width: 768px) {
  .mobile-only {
    display: block;
  }
  
  .desktop-only {
    display: none;
  }
  
  .mobile-hidden {
    display: none !important;
  }
}

/* 文本响应式 */
.responsive-text {
  font-size: 16px;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .responsive-text {
    font-size: 14px;
    line-height: 1.4;
  }
}

/* 间距响应式 */
.responsive-padding {
  padding: 20px;
}

@media (max-width: 768px) {
  .responsive-padding {
    padding: 15px;
  }
}

.responsive-margin {
  margin: 20px 0;
}

@media (max-width: 768px) {
  .responsive-margin {
    margin: 15px 0;
  }
}

/* 按钮响应式 */
.responsive-button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .responsive-button-group {
    flex-direction: column;
    gap: 8px;
  }
  
  .responsive-button-group .el-button {
    width: 100%;
    justify-content: center;
  }
}

/* 表格响应式 */
.responsive-table {
  overflow-x: auto;
}

@media (max-width: 768px) {
  .responsive-table .el-table {
    min-width: 600px;
  }
  
  .responsive-table .el-table__cell {
    white-space: nowrap;
  }
}

/* 卡片响应式 */
.responsive-card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

@media (max-width: 768px) {
  .responsive-card-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}

/* 表单响应式 */
.responsive-form .el-form-item {
  margin-bottom: 20px;
}

@media (max-width: 768px) {
  .responsive-form .el-form-item {
    margin-bottom: 15px;
  }
  
  .responsive-form .el-form-item__label {
    width: 100px !important;
    font-size: 14px;
  }
  
  .responsive-form .el-input,
  .responsive-form .el-select,
  .responsive-form .el-date-editor {
    width: 100% !important;
  }
}
