hoistPattern:
  - '*'
hoistedDependencies:
  registry.npmmirror.com/@babel/helper-string-parser/7.27.1:
    '@babel/helper-string-parser': private
  registry.npmmirror.com/@babel/helper-validator-identifier/7.27.1:
    '@babel/helper-validator-identifier': private
  registry.npmmirror.com/@babel/parser/7.27.7:
    '@babel/parser': private
  registry.npmmirror.com/@babel/types/7.27.7:
    '@babel/types': private
  registry.npmmirror.com/@esbuild/aix-ppc64/0.25.5:
    '@esbuild/aix-ppc64': private
  registry.npmmirror.com/@esbuild/android-arm/0.25.5:
    '@esbuild/android-arm': private
  registry.npmmirror.com/@esbuild/android-arm64/0.25.5:
    '@esbuild/android-arm64': private
  registry.npmmirror.com/@esbuild/android-x64/0.25.5:
    '@esbuild/android-x64': private
  registry.npmmirror.com/@esbuild/darwin-arm64/0.25.5:
    '@esbuild/darwin-arm64': private
  registry.npmmirror.com/@esbuild/darwin-x64/0.25.5:
    '@esbuild/darwin-x64': private
  registry.npmmirror.com/@esbuild/freebsd-arm64/0.25.5:
    '@esbuild/freebsd-arm64': private
  registry.npmmirror.com/@esbuild/freebsd-x64/0.25.5:
    '@esbuild/freebsd-x64': private
  registry.npmmirror.com/@esbuild/linux-arm/0.25.5:
    '@esbuild/linux-arm': private
  registry.npmmirror.com/@esbuild/linux-arm64/0.25.5:
    '@esbuild/linux-arm64': private
  registry.npmmirror.com/@esbuild/linux-ia32/0.25.5:
    '@esbuild/linux-ia32': private
  registry.npmmirror.com/@esbuild/linux-loong64/0.25.5:
    '@esbuild/linux-loong64': private
  registry.npmmirror.com/@esbuild/linux-mips64el/0.25.5:
    '@esbuild/linux-mips64el': private
  registry.npmmirror.com/@esbuild/linux-ppc64/0.25.5:
    '@esbuild/linux-ppc64': private
  registry.npmmirror.com/@esbuild/linux-riscv64/0.25.5:
    '@esbuild/linux-riscv64': private
  registry.npmmirror.com/@esbuild/linux-s390x/0.25.5:
    '@esbuild/linux-s390x': private
  registry.npmmirror.com/@esbuild/linux-x64/0.25.5:
    '@esbuild/linux-x64': private
  registry.npmmirror.com/@esbuild/netbsd-arm64/0.25.5:
    '@esbuild/netbsd-arm64': private
  registry.npmmirror.com/@esbuild/netbsd-x64/0.25.5:
    '@esbuild/netbsd-x64': private
  registry.npmmirror.com/@esbuild/openbsd-arm64/0.25.5:
    '@esbuild/openbsd-arm64': private
  registry.npmmirror.com/@esbuild/openbsd-x64/0.25.5:
    '@esbuild/openbsd-x64': private
  registry.npmmirror.com/@esbuild/sunos-x64/0.25.5:
    '@esbuild/sunos-x64': private
  registry.npmmirror.com/@esbuild/win32-arm64/0.25.5:
    '@esbuild/win32-arm64': private
  registry.npmmirror.com/@esbuild/win32-ia32/0.25.5:
    '@esbuild/win32-ia32': private
  registry.npmmirror.com/@esbuild/win32-x64/0.25.5:
    '@esbuild/win32-x64': private
  registry.npmmirror.com/@jridgewell/sourcemap-codec/1.5.1:
    '@jridgewell/sourcemap-codec': private
  registry.npmmirror.com/@rolldown/pluginutils/1.0.0-beta.19:
    '@rolldown/pluginutils': private
  registry.npmmirror.com/@rollup/rollup-android-arm-eabi/4.44.1:
    '@rollup/rollup-android-arm-eabi': private
  registry.npmmirror.com/@rollup/rollup-android-arm64/4.44.1:
    '@rollup/rollup-android-arm64': private
  registry.npmmirror.com/@rollup/rollup-darwin-arm64/4.44.1:
    '@rollup/rollup-darwin-arm64': private
  registry.npmmirror.com/@rollup/rollup-darwin-x64/4.44.1:
    '@rollup/rollup-darwin-x64': private
  registry.npmmirror.com/@rollup/rollup-freebsd-arm64/4.44.1:
    '@rollup/rollup-freebsd-arm64': private
  registry.npmmirror.com/@rollup/rollup-freebsd-x64/4.44.1:
    '@rollup/rollup-freebsd-x64': private
  registry.npmmirror.com/@rollup/rollup-linux-arm-gnueabihf/4.44.1:
    '@rollup/rollup-linux-arm-gnueabihf': private
  registry.npmmirror.com/@rollup/rollup-linux-arm-musleabihf/4.44.1:
    '@rollup/rollup-linux-arm-musleabihf': private
  registry.npmmirror.com/@rollup/rollup-linux-arm64-gnu/4.44.1:
    '@rollup/rollup-linux-arm64-gnu': private
  registry.npmmirror.com/@rollup/rollup-linux-arm64-musl/4.44.1:
    '@rollup/rollup-linux-arm64-musl': private
  registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/4.44.1:
    '@rollup/rollup-linux-loongarch64-gnu': private
  registry.npmmirror.com/@rollup/rollup-linux-powerpc64le-gnu/4.44.1:
    '@rollup/rollup-linux-powerpc64le-gnu': private
  registry.npmmirror.com/@rollup/rollup-linux-riscv64-gnu/4.44.1:
    '@rollup/rollup-linux-riscv64-gnu': private
  registry.npmmirror.com/@rollup/rollup-linux-riscv64-musl/4.44.1:
    '@rollup/rollup-linux-riscv64-musl': private
  registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/4.44.1:
    '@rollup/rollup-linux-s390x-gnu': private
  registry.npmmirror.com/@rollup/rollup-linux-x64-gnu/4.44.1:
    '@rollup/rollup-linux-x64-gnu': private
  registry.npmmirror.com/@rollup/rollup-linux-x64-musl/4.44.1:
    '@rollup/rollup-linux-x64-musl': private
  registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/4.44.1:
    '@rollup/rollup-win32-arm64-msvc': private
  registry.npmmirror.com/@rollup/rollup-win32-ia32-msvc/4.44.1:
    '@rollup/rollup-win32-ia32-msvc': private
  registry.npmmirror.com/@rollup/rollup-win32-x64-msvc/4.44.1:
    '@rollup/rollup-win32-x64-msvc': private
  registry.npmmirror.com/@types/estree/1.0.8:
    '@types/estree': private
  registry.npmmirror.com/@vue/compiler-core/3.5.17:
    '@vue/compiler-core': private
  registry.npmmirror.com/@vue/compiler-dom/3.5.17:
    '@vue/compiler-dom': private
  registry.npmmirror.com/@vue/compiler-sfc/3.5.17:
    '@vue/compiler-sfc': private
  registry.npmmirror.com/@vue/compiler-ssr/3.5.17:
    '@vue/compiler-ssr': private
  registry.npmmirror.com/@vue/reactivity/3.5.17:
    '@vue/reactivity': private
  registry.npmmirror.com/@vue/runtime-core/3.5.17:
    '@vue/runtime-core': private
  registry.npmmirror.com/@vue/runtime-dom/3.5.17:
    '@vue/runtime-dom': private
  registry.npmmirror.com/@vue/server-renderer/3.5.17(vue@3.5.17):
    '@vue/server-renderer': private
  registry.npmmirror.com/@vue/shared/3.5.17:
    '@vue/shared': private
  registry.npmmirror.com/csstype/3.1.3:
    csstype: private
  registry.npmmirror.com/entities/4.5.0:
    entities: private
  registry.npmmirror.com/esbuild/0.25.5:
    esbuild: private
  registry.npmmirror.com/estree-walker/2.0.2:
    estree-walker: private
  registry.npmmirror.com/fdir/6.4.6(picomatch@4.0.2):
    fdir: private
  registry.npmmirror.com/fsevents/2.3.3:
    fsevents: private
  registry.npmmirror.com/magic-string/0.30.17:
    magic-string: private
  registry.npmmirror.com/nanoid/3.3.11:
    nanoid: private
  registry.npmmirror.com/picocolors/1.1.1:
    picocolors: private
  registry.npmmirror.com/picomatch/4.0.2:
    picomatch: private
  registry.npmmirror.com/postcss/8.5.6:
    postcss: private
  registry.npmmirror.com/rollup/4.44.1:
    rollup: private
  registry.npmmirror.com/source-map-js/1.2.1:
    source-map-js: private
  registry.npmmirror.com/tinyglobby/0.2.14:
    tinyglobby: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@8.6.12
pendingBuilds: []
prunedAt: Mon, 30 Jun 2025 03:06:42 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: http://registry.npm.taobao.org/
skipped:
  - registry.npmmirror.com/@esbuild/aix-ppc64/0.25.5
  - registry.npmmirror.com/@esbuild/android-arm/0.25.5
  - registry.npmmirror.com/@esbuild/android-arm64/0.25.5
  - registry.npmmirror.com/@esbuild/android-x64/0.25.5
  - registry.npmmirror.com/@esbuild/darwin-x64/0.25.5
  - registry.npmmirror.com/@esbuild/freebsd-arm64/0.25.5
  - registry.npmmirror.com/@esbuild/freebsd-x64/0.25.5
  - registry.npmmirror.com/@esbuild/linux-arm/0.25.5
  - registry.npmmirror.com/@esbuild/linux-arm64/0.25.5
  - registry.npmmirror.com/@esbuild/linux-ia32/0.25.5
  - registry.npmmirror.com/@esbuild/linux-loong64/0.25.5
  - registry.npmmirror.com/@esbuild/linux-mips64el/0.25.5
  - registry.npmmirror.com/@esbuild/linux-ppc64/0.25.5
  - registry.npmmirror.com/@esbuild/linux-riscv64/0.25.5
  - registry.npmmirror.com/@esbuild/linux-s390x/0.25.5
  - registry.npmmirror.com/@esbuild/linux-x64/0.25.5
  - registry.npmmirror.com/@esbuild/netbsd-arm64/0.25.5
  - registry.npmmirror.com/@esbuild/netbsd-x64/0.25.5
  - registry.npmmirror.com/@esbuild/openbsd-arm64/0.25.5
  - registry.npmmirror.com/@esbuild/openbsd-x64/0.25.5
  - registry.npmmirror.com/@esbuild/sunos-x64/0.25.5
  - registry.npmmirror.com/@esbuild/win32-arm64/0.25.5
  - registry.npmmirror.com/@esbuild/win32-ia32/0.25.5
  - registry.npmmirror.com/@esbuild/win32-x64/0.25.5
  - registry.npmmirror.com/@rollup/rollup-android-arm-eabi/4.44.1
  - registry.npmmirror.com/@rollup/rollup-android-arm64/4.44.1
  - registry.npmmirror.com/@rollup/rollup-darwin-x64/4.44.1
  - registry.npmmirror.com/@rollup/rollup-freebsd-arm64/4.44.1
  - registry.npmmirror.com/@rollup/rollup-freebsd-x64/4.44.1
  - registry.npmmirror.com/@rollup/rollup-linux-arm-gnueabihf/4.44.1
  - registry.npmmirror.com/@rollup/rollup-linux-arm-musleabihf/4.44.1
  - registry.npmmirror.com/@rollup/rollup-linux-arm64-gnu/4.44.1
  - registry.npmmirror.com/@rollup/rollup-linux-arm64-musl/4.44.1
  - registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/4.44.1
  - registry.npmmirror.com/@rollup/rollup-linux-powerpc64le-gnu/4.44.1
  - registry.npmmirror.com/@rollup/rollup-linux-riscv64-gnu/4.44.1
  - registry.npmmirror.com/@rollup/rollup-linux-riscv64-musl/4.44.1
  - registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/4.44.1
  - registry.npmmirror.com/@rollup/rollup-linux-x64-gnu/4.44.1
  - registry.npmmirror.com/@rollup/rollup-linux-x64-musl/4.44.1
  - registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/4.44.1
  - registry.npmmirror.com/@rollup/rollup-win32-ia32-msvc/4.44.1
  - registry.npmmirror.com/@rollup/rollup-win32-x64-msvc/4.44.1
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
