lockfileVersion: '6.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

dependencies:
  vue:
    specifier: ^3.5.17
    version: registry.npmmirror.com/vue@3.5.17

devDependencies:
  '@vitejs/plugin-vue':
    specifier: ^6.0.0
    version: registry.npmmirror.com/@vitejs/plugin-vue@6.0.0(vite@7.0.0)(vue@3.5.17)
  vite:
    specifier: ^7.0.0
    version: registry.npmmirror.com/vite@7.0.0

packages:

  registry.npmmirror.com/@babel/helper-string-parser@7.27.1:
    resolution: {integrity: sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz}
    name: '@babel/helper-string-parser'
    version: 7.27.1
    engines: {node: '>=6.9.0'}

  registry.npmmirror.com/@babel/helper-validator-identifier@7.27.1:
    resolution: {integrity: sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz}
    name: '@babel/helper-validator-identifier'
    version: 7.27.1
    engines: {node: '>=6.9.0'}

  registry.npmmirror.com/@babel/parser@7.27.7:
    resolution: {integrity: sha512-qnzXzDXdr/po3bOTbTIQZ7+TxNKxpkN5IifVLXS+r7qwynkZfPyjZfE7hCXbo7IoO9TNcSyibgONsf2HauUd3Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/parser/-/parser-7.27.7.tgz}
    name: '@babel/parser'
    version: 7.27.7
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.7

  registry.npmmirror.com/@babel/types@7.27.7:
    resolution: {integrity: sha512-8OLQgDScAOHXnAz2cV+RfzzNMipuLVBz2biuAJFMV9bfkNf393je3VM8CLkjQodW5+iWsSJdSgSWT6rsZoXHPw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/types/-/types-7.27.7.tgz}
    name: '@babel/types'
    version: 7.27.7
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': registry.npmmirror.com/@babel/helper-string-parser@7.27.1
      '@babel/helper-validator-identifier': registry.npmmirror.com/@babel/helper-validator-identifier@7.27.1

  registry.npmmirror.com/@esbuild/aix-ppc64@0.25.5:
    resolution: {integrity: sha512-9o3TMmpmftaCMepOdA5k/yDw8SfInyzWWTjYTFCX3kPSDJMROQTb8jg+h9Cnwnmm1vOzvxN7gIfB5V2ewpjtGA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/aix-ppc64/-/aix-ppc64-0.25.5.tgz}
    name: '@esbuild/aix-ppc64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [aix]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/android-arm64@0.25.5:
    resolution: {integrity: sha512-VGzGhj4lJO+TVGV1v8ntCZWJktV7SGCs3Pn1GRWI1SBFtRALoomm8k5E9Pmwg3HOAal2VDc2F9+PM/rEY6oIDg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/android-arm64/-/android-arm64-0.25.5.tgz}
    name: '@esbuild/android-arm64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/android-arm@0.25.5:
    resolution: {integrity: sha512-AdJKSPeEHgi7/ZhuIPtcQKr5RQdo6OO2IL87JkianiMYMPbCtot9fxPbrMiBADOWWm3T2si9stAiVsGbTQFkbA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/android-arm/-/android-arm-0.25.5.tgz}
    name: '@esbuild/android-arm'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/android-x64@0.25.5:
    resolution: {integrity: sha512-D2GyJT1kjvO//drbRT3Hib9XPwQeWd9vZoBJn+bu/lVsOZ13cqNdDeqIF/xQ5/VmWvMduP6AmXvylO/PIc2isw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/android-x64/-/android-x64-0.25.5.tgz}
    name: '@esbuild/android-x64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [x64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/darwin-arm64@0.25.5:
    resolution: {integrity: sha512-GtaBgammVvdF7aPIgH2jxMDdivezgFu6iKpmT+48+F8Hhg5J/sfnDieg0aeG/jfSvkYQU2/pceFPDKlqZzwnfQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/darwin-arm64/-/darwin-arm64-0.25.5.tgz}
    name: '@esbuild/darwin-arm64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/darwin-x64@0.25.5:
    resolution: {integrity: sha512-1iT4FVL0dJ76/q1wd7XDsXrSW+oLoquptvh4CLR4kITDtqi2e/xwXwdCVH8hVHU43wgJdsq7Gxuzcs6Iq/7bxQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.25.5.tgz}
    name: '@esbuild/darwin-x64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/freebsd-arm64@0.25.5:
    resolution: {integrity: sha512-nk4tGP3JThz4La38Uy/gzyXtpkPW8zSAmoUhK9xKKXdBCzKODMc2adkB2+8om9BDYugz+uGV7sLmpTYzvmz6Sw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.5.tgz}
    name: '@esbuild/freebsd-arm64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/freebsd-x64@0.25.5:
    resolution: {integrity: sha512-PrikaNjiXdR2laW6OIjlbeuCPrPaAl0IwPIaRv+SMV8CiM8i2LqVUHFC1+8eORgWyY7yhQY+2U2fA55mBzReaw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/freebsd-x64/-/freebsd-x64-0.25.5.tgz}
    name: '@esbuild/freebsd-x64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-arm64@0.25.5:
    resolution: {integrity: sha512-Z9kfb1v6ZlGbWj8EJk9T6czVEjjq2ntSYLY2cw6pAZl4oKtfgQuS4HOq41M/BcoLPzrUbNd+R4BXFyH//nHxVg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-arm64/-/linux-arm64-0.25.5.tgz}
    name: '@esbuild/linux-arm64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-arm@0.25.5:
    resolution: {integrity: sha512-cPzojwW2okgh7ZlRpcBEtsX7WBuqbLrNXqLU89GxWbNt6uIg78ET82qifUy3W6OVww6ZWobWub5oqZOVtwolfw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-arm/-/linux-arm-0.25.5.tgz}
    name: '@esbuild/linux-arm'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-ia32@0.25.5:
    resolution: {integrity: sha512-sQ7l00M8bSv36GLV95BVAdhJ2QsIbCuCjh/uYrWiMQSUuV+LpXwIqhgJDcvMTj+VsQmqAHL2yYaasENvJ7CDKA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.25.5.tgz}
    name: '@esbuild/linux-ia32'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-loong64@0.25.5:
    resolution: {integrity: sha512-0ur7ae16hDUC4OL5iEnDb0tZHDxYmuQyhKhsPBV8f99f6Z9KQM02g33f93rNH5A30agMS46u2HP6qTdEt6Q1kg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-loong64/-/linux-loong64-0.25.5.tgz}
    name: '@esbuild/linux-loong64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [loong64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-mips64el@0.25.5:
    resolution: {integrity: sha512-kB/66P1OsHO5zLz0i6X0RxlQ+3cu0mkxS3TKFvkb5lin6uwZ/ttOkP3Z8lfR9mJOBk14ZwZ9182SIIWFGNmqmg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-mips64el/-/linux-mips64el-0.25.5.tgz}
    name: '@esbuild/linux-mips64el'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [mips64el]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-ppc64@0.25.5:
    resolution: {integrity: sha512-UZCmJ7r9X2fe2D6jBmkLBMQetXPXIsZjQJCjgwpVDz+YMcS6oFR27alkgGv3Oqkv07bxdvw7fyB71/olceJhkQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.25.5.tgz}
    name: '@esbuild/linux-ppc64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-riscv64@0.25.5:
    resolution: {integrity: sha512-kTxwu4mLyeOlsVIFPfQo+fQJAV9mh24xL+y+Bm6ej067sYANjyEw1dNHmvoqxJUCMnkBdKpvOn0Ahql6+4VyeA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-riscv64/-/linux-riscv64-0.25.5.tgz}
    name: '@esbuild/linux-riscv64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-s390x@0.25.5:
    resolution: {integrity: sha512-K2dSKTKfmdh78uJ3NcWFiqyRrimfdinS5ErLSn3vluHNeHVnBAFWC8a4X5N+7FgVE1EjXS1QDZbpqZBjfrqMTQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-s390x/-/linux-s390x-0.25.5.tgz}
    name: '@esbuild/linux-s390x'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [s390x]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-x64@0.25.5:
    resolution: {integrity: sha512-uhj8N2obKTE6pSZ+aMUbqq+1nXxNjZIIjCjGLfsWvVpy7gKCOL6rsY1MhRh9zLtUtAI7vpgLMK6DxjO8Qm9lJw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-x64/-/linux-x64-0.25.5.tgz}
    name: '@esbuild/linux-x64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/netbsd-arm64@0.25.5:
    resolution: {integrity: sha512-pwHtMP9viAy1oHPvgxtOv+OkduK5ugofNTVDilIzBLpoWAM16r7b/mxBvfpuQDpRQFMfuVr5aLcn4yveGvBZvw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.5.tgz}
    name: '@esbuild/netbsd-arm64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [netbsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/netbsd-x64@0.25.5:
    resolution: {integrity: sha512-WOb5fKrvVTRMfWFNCroYWWklbnXH0Q5rZppjq0vQIdlsQKuw6mdSihwSo4RV/YdQ5UCKKvBy7/0ZZYLBZKIbwQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/netbsd-x64/-/netbsd-x64-0.25.5.tgz}
    name: '@esbuild/netbsd-x64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [x64]
    os: [netbsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/openbsd-arm64@0.25.5:
    resolution: {integrity: sha512-7A208+uQKgTxHd0G0uqZO8UjK2R0DDb4fDmERtARjSHWxqMTye4Erz4zZafx7Di9Cv+lNHYuncAkiGFySoD+Mw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.5.tgz}
    name: '@esbuild/openbsd-arm64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openbsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/openbsd-x64@0.25.5:
    resolution: {integrity: sha512-G4hE405ErTWraiZ8UiSoesH8DaCsMm0Cay4fsFWOOUcz8b8rC6uCvnagr+gnioEjWn0wC+o1/TAHt+It+MpIMg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/openbsd-x64/-/openbsd-x64-0.25.5.tgz}
    name: '@esbuild/openbsd-x64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [x64]
    os: [openbsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/sunos-x64@0.25.5:
    resolution: {integrity: sha512-l+azKShMy7FxzY0Rj4RCt5VD/q8mG/e+mDivgspo+yL8zW7qEwctQ6YqKX34DTEleFAvCIUviCFX1SDZRSyMQA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/sunos-x64/-/sunos-x64-0.25.5.tgz}
    name: '@esbuild/sunos-x64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [x64]
    os: [sunos]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/win32-arm64@0.25.5:
    resolution: {integrity: sha512-O2S7SNZzdcFG7eFKgvwUEZ2VG9D/sn/eIiz8XRZ1Q/DO5a3s76Xv0mdBzVM5j5R639lXQmPmSo0iRpHqUUrsxw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/win32-arm64/-/win32-arm64-0.25.5.tgz}
    name: '@esbuild/win32-arm64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/win32-ia32@0.25.5:
    resolution: {integrity: sha512-onOJ02pqs9h1iMJ1PQphR+VZv8qBMQ77Klcsqv9CNW2w6yLqoURLcgERAIurY6QE63bbLuqgP9ATqajFLK5AMQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/win32-ia32/-/win32-ia32-0.25.5.tgz}
    name: '@esbuild/win32-ia32'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/win32-x64@0.25.5:
    resolution: {integrity: sha512-TXv6YnJ8ZMVdX+SXWVBo/0p8LTcrUYngpWjvm91TMjjBQii7Oz11Lw5lbDV5Y0TzuhSJHwiH4hEtC1I42mMS0g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/win32-x64/-/win32-x64-0.25.5.tgz}
    name: '@esbuild/win32-x64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@jridgewell/sourcemap-codec@1.5.1:
    resolution: {integrity: sha512-mBLKRHc7Ffw/hObYb9+cunuGNjshQk+vZdwZBJoqiysK/mW3Jq0UXosq8aIhMnLevANhR9yoYfdUEOHg6M9y0g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.1.tgz}
    name: '@jridgewell/sourcemap-codec'
    version: 1.5.1

  registry.npmmirror.com/@rolldown/pluginutils@1.0.0-beta.19:
    resolution: {integrity: sha512-3FL3mnMbPu0muGOCaKAhhFEYmqv9eTfPSJRJmANrCwtgK8VuxpsZDGK+m0LYAGoyO8+0j5uRe4PeyPDK1yA/hA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.19.tgz}
    name: '@rolldown/pluginutils'
    version: 1.0.0-beta.19
    dev: true

  registry.npmmirror.com/@rollup/rollup-android-arm-eabi@4.44.1:
    resolution: {integrity: sha512-JAcBr1+fgqx20m7Fwe1DxPUl/hPkee6jA6Pl7n1v2EFiktAHenTaXl5aIFjUIEsfn9w3HE4gK1lEgNGMzBDs1w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.44.1.tgz}
    name: '@rollup/rollup-android-arm-eabi'
    version: 4.44.1
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-android-arm64@4.44.1:
    resolution: {integrity: sha512-RurZetXqTu4p+G0ChbnkwBuAtwAbIwJkycw1n6GvlGlBuS4u5qlr5opix8cBAYFJgaY05TWtM+LaoFggUmbZEQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.44.1.tgz}
    name: '@rollup/rollup-android-arm64'
    version: 4.44.1
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-darwin-arm64@4.44.1:
    resolution: {integrity: sha512-fM/xPesi7g2M7chk37LOnmnSTHLG/v2ggWqKj3CCA1rMA4mm5KVBT1fNoswbo1JhPuNNZrVwpTvlCVggv8A2zg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.44.1.tgz}
    name: '@rollup/rollup-darwin-arm64'
    version: 4.44.1
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-darwin-x64@4.44.1:
    resolution: {integrity: sha512-gDnWk57urJrkrHQ2WVx9TSVTH7lSlU7E3AFqiko+bgjlh78aJ88/3nycMax52VIVjIm3ObXnDL2H00e/xzoipw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.44.1.tgz}
    name: '@rollup/rollup-darwin-x64'
    version: 4.44.1
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-freebsd-arm64@4.44.1:
    resolution: {integrity: sha512-wnFQmJ/zPThM5zEGcnDcCJeYJgtSLjh1d//WuHzhf6zT3Md1BvvhJnWoy+HECKu2bMxaIcfWiu3bJgx6z4g2XA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.44.1.tgz}
    name: '@rollup/rollup-freebsd-arm64'
    version: 4.44.1
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-freebsd-x64@4.44.1:
    resolution: {integrity: sha512-uBmIxoJ4493YATvU2c0upGz87f99e3wop7TJgOA/bXMFd2SvKCI7xkxY/5k50bv7J6dw1SXT4MQBQSLn8Bb/Uw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.44.1.tgz}
    name: '@rollup/rollup-freebsd-x64'
    version: 4.44.1
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-linux-arm-gnueabihf@4.44.1:
    resolution: {integrity: sha512-n0edDmSHlXFhrlmTK7XBuwKlG5MbS7yleS1cQ9nn4kIeW+dJH+ExqNgQ0RrFRew8Y+0V/x6C5IjsHrJmiHtkxQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.44.1.tgz}
    name: '@rollup/rollup-linux-arm-gnueabihf'
    version: 4.44.1
    cpu: [arm]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-linux-arm-musleabihf@4.44.1:
    resolution: {integrity: sha512-8WVUPy3FtAsKSpyk21kV52HCxB+me6YkbkFHATzC2Yd3yuqHwy2lbFL4alJOLXKljoRw08Zk8/xEj89cLQ/4Nw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.44.1.tgz}
    name: '@rollup/rollup-linux-arm-musleabihf'
    version: 4.44.1
    cpu: [arm]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-linux-arm64-gnu@4.44.1:
    resolution: {integrity: sha512-yuktAOaeOgorWDeFJggjuCkMGeITfqvPgkIXhDqsfKX8J3jGyxdDZgBV/2kj/2DyPaLiX6bPdjJDTu9RB8lUPQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.44.1.tgz}
    name: '@rollup/rollup-linux-arm64-gnu'
    version: 4.44.1
    cpu: [arm64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-linux-arm64-musl@4.44.1:
    resolution: {integrity: sha512-W+GBM4ifET1Plw8pdVaecwUgxmiH23CfAUj32u8knq0JPFyK4weRy6H7ooxYFD19YxBulL0Ktsflg5XS7+7u9g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.44.1.tgz}
    name: '@rollup/rollup-linux-arm64-musl'
    version: 4.44.1
    cpu: [arm64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu@4.44.1:
    resolution: {integrity: sha512-1zqnUEMWp9WrGVuVak6jWTl4fEtrVKfZY7CvcBmUUpxAJ7WcSowPSAWIKa/0o5mBL/Ij50SIf9tuirGx63Ovew==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.44.1.tgz}
    name: '@rollup/rollup-linux-loongarch64-gnu'
    version: 4.44.1
    cpu: [loong64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-linux-powerpc64le-gnu@4.44.1:
    resolution: {integrity: sha512-Rl3JKaRu0LHIx7ExBAAnf0JcOQetQffaw34T8vLlg9b1IhzcBgaIdnvEbbsZq9uZp3uAH+JkHd20Nwn0h9zPjA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.44.1.tgz}
    name: '@rollup/rollup-linux-powerpc64le-gnu'
    version: 4.44.1
    cpu: [ppc64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-linux-riscv64-gnu@4.44.1:
    resolution: {integrity: sha512-j5akelU3snyL6K3N/iX7otLBIl347fGwmd95U5gS/7z6T4ftK288jKq3A5lcFKcx7wwzb5rgNvAg3ZbV4BqUSw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.44.1.tgz}
    name: '@rollup/rollup-linux-riscv64-gnu'
    version: 4.44.1
    cpu: [riscv64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-linux-riscv64-musl@4.44.1:
    resolution: {integrity: sha512-ppn5llVGgrZw7yxbIm8TTvtj1EoPgYUAbfw0uDjIOzzoqlZlZrLJ/KuiE7uf5EpTpCTrNt1EdtzF0naMm0wGYg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.44.1.tgz}
    name: '@rollup/rollup-linux-riscv64-musl'
    version: 4.44.1
    cpu: [riscv64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu@4.44.1:
    resolution: {integrity: sha512-Hu6hEdix0oxtUma99jSP7xbvjkUM/ycke/AQQ4EC5g7jNRLLIwjcNwaUy95ZKBJJwg1ZowsclNnjYqzN4zwkAw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.44.1.tgz}
    name: '@rollup/rollup-linux-s390x-gnu'
    version: 4.44.1
    cpu: [s390x]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-linux-x64-gnu@4.44.1:
    resolution: {integrity: sha512-EtnsrmZGomz9WxK1bR5079zee3+7a+AdFlghyd6VbAjgRJDbTANJ9dcPIPAi76uG05micpEL+gPGmAKYTschQw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.44.1.tgz}
    name: '@rollup/rollup-linux-x64-gnu'
    version: 4.44.1
    cpu: [x64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-linux-x64-musl@4.44.1:
    resolution: {integrity: sha512-iAS4p+J1az6Usn0f8xhgL4PaU878KEtutP4hqw52I4IO6AGoyOkHCxcc4bqufv1tQLdDWFx8lR9YlwxKuv3/3g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.44.1.tgz}
    name: '@rollup/rollup-linux-x64-musl'
    version: 4.44.1
    cpu: [x64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc@4.44.1:
    resolution: {integrity: sha512-NtSJVKcXwcqozOl+FwI41OH3OApDyLk3kqTJgx8+gp6On9ZEt5mYhIsKNPGuaZr3p9T6NWPKGU/03Vw4CNU9qg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.44.1.tgz}
    name: '@rollup/rollup-win32-arm64-msvc'
    version: 4.44.1
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-win32-ia32-msvc@4.44.1:
    resolution: {integrity: sha512-JYA3qvCOLXSsnTR3oiyGws1Dm0YTuxAAeaYGVlGpUsHqloPcFjPg+X0Fj2qODGLNwQOAcCiQmHub/V007kiH5A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.44.1.tgz}
    name: '@rollup/rollup-win32-ia32-msvc'
    version: 4.44.1
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-win32-x64-msvc@4.44.1:
    resolution: {integrity: sha512-J8o22LuF0kTe7m+8PvW9wk3/bRq5+mRo5Dqo6+vXb7otCm3TPhYOJqOaQtGU9YMWQSL3krMnoOxMr0+9E6F3Ug==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.44.1.tgz}
    name: '@rollup/rollup-win32-x64-msvc'
    version: 4.44.1
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@types/estree@1.0.8:
    resolution: {integrity: sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/estree/-/estree-1.0.8.tgz}
    name: '@types/estree'
    version: 1.0.8
    dev: true

  registry.npmmirror.com/@vitejs/plugin-vue@6.0.0(vite@7.0.0)(vue@3.5.17):
    resolution: {integrity: sha512-iAliE72WsdhjzTOp2DtvKThq1VBC4REhwRcaA+zPAAph6I+OQhUXv+Xu2KS7ElxYtb7Zc/3R30Hwv1DxEo7NXQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vitejs/plugin-vue/-/plugin-vue-6.0.0.tgz}
    id: registry.npmmirror.com/@vitejs/plugin-vue/6.0.0
    name: '@vitejs/plugin-vue'
    version: 6.0.0
    engines: {node: ^20.19.0 || >=22.12.0}
    peerDependencies:
      vite: ^5.0.0 || ^6.0.0 || ^7.0.0
      vue: ^3.2.25
    dependencies:
      '@rolldown/pluginutils': registry.npmmirror.com/@rolldown/pluginutils@1.0.0-beta.19
      vite: registry.npmmirror.com/vite@7.0.0
      vue: registry.npmmirror.com/vue@3.5.17
    dev: true

  registry.npmmirror.com/@vue/compiler-core@3.5.17:
    resolution: {integrity: sha512-Xe+AittLbAyV0pabcN7cP7/BenRBNcteM4aSDCtRvGw0d9OL+HG1u/XHLY/kt1q4fyMeZYXyIYrsHuPSiDPosA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/compiler-core/-/compiler-core-3.5.17.tgz}
    name: '@vue/compiler-core'
    version: 3.5.17
    dependencies:
      '@babel/parser': registry.npmmirror.com/@babel/parser@7.27.7
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.5.17
      entities: registry.npmmirror.com/entities@4.5.0
      estree-walker: registry.npmmirror.com/estree-walker@2.0.2
      source-map-js: registry.npmmirror.com/source-map-js@1.2.1

  registry.npmmirror.com/@vue/compiler-dom@3.5.17:
    resolution: {integrity: sha512-+2UgfLKoaNLhgfhV5Ihnk6wB4ljyW1/7wUIog2puUqajiC29Lp5R/IKDdkebh9jTbTogTbsgB+OY9cEWzG95JQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/compiler-dom/-/compiler-dom-3.5.17.tgz}
    name: '@vue/compiler-dom'
    version: 3.5.17
    dependencies:
      '@vue/compiler-core': registry.npmmirror.com/@vue/compiler-core@3.5.17
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.5.17

  registry.npmmirror.com/@vue/compiler-sfc@3.5.17:
    resolution: {integrity: sha512-rQQxbRJMgTqwRugtjw0cnyQv9cP4/4BxWfTdRBkqsTfLOHWykLzbOc3C4GGzAmdMDxhzU/1Ija5bTjMVrddqww==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/compiler-sfc/-/compiler-sfc-3.5.17.tgz}
    name: '@vue/compiler-sfc'
    version: 3.5.17
    dependencies:
      '@babel/parser': registry.npmmirror.com/@babel/parser@7.27.7
      '@vue/compiler-core': registry.npmmirror.com/@vue/compiler-core@3.5.17
      '@vue/compiler-dom': registry.npmmirror.com/@vue/compiler-dom@3.5.17
      '@vue/compiler-ssr': registry.npmmirror.com/@vue/compiler-ssr@3.5.17
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.5.17
      estree-walker: registry.npmmirror.com/estree-walker@2.0.2
      magic-string: registry.npmmirror.com/magic-string@0.30.17
      postcss: registry.npmmirror.com/postcss@8.5.6
      source-map-js: registry.npmmirror.com/source-map-js@1.2.1

  registry.npmmirror.com/@vue/compiler-ssr@3.5.17:
    resolution: {integrity: sha512-hkDbA0Q20ZzGgpj5uZjb9rBzQtIHLS78mMilwrlpWk2Ep37DYntUz0PonQ6kr113vfOEdM+zTBuJDaceNIW0tQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/compiler-ssr/-/compiler-ssr-3.5.17.tgz}
    name: '@vue/compiler-ssr'
    version: 3.5.17
    dependencies:
      '@vue/compiler-dom': registry.npmmirror.com/@vue/compiler-dom@3.5.17
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.5.17

  registry.npmmirror.com/@vue/reactivity@3.5.17:
    resolution: {integrity: sha512-l/rmw2STIscWi7SNJp708FK4Kofs97zc/5aEPQh4bOsReD/8ICuBcEmS7KGwDj5ODQLYWVN2lNibKJL1z5b+Lw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/reactivity/-/reactivity-3.5.17.tgz}
    name: '@vue/reactivity'
    version: 3.5.17
    dependencies:
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.5.17

  registry.npmmirror.com/@vue/runtime-core@3.5.17:
    resolution: {integrity: sha512-QQLXa20dHg1R0ri4bjKeGFKEkJA7MMBxrKo2G+gJikmumRS7PTD4BOU9FKrDQWMKowz7frJJGqBffYMgQYS96Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/runtime-core/-/runtime-core-3.5.17.tgz}
    name: '@vue/runtime-core'
    version: 3.5.17
    dependencies:
      '@vue/reactivity': registry.npmmirror.com/@vue/reactivity@3.5.17
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.5.17

  registry.npmmirror.com/@vue/runtime-dom@3.5.17:
    resolution: {integrity: sha512-8El0M60TcwZ1QMz4/os2MdlQECgGoVHPuLnQBU3m9h3gdNRW9xRmI8iLS4t/22OQlOE6aJvNNlBiCzPHur4H9g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/runtime-dom/-/runtime-dom-3.5.17.tgz}
    name: '@vue/runtime-dom'
    version: 3.5.17
    dependencies:
      '@vue/reactivity': registry.npmmirror.com/@vue/reactivity@3.5.17
      '@vue/runtime-core': registry.npmmirror.com/@vue/runtime-core@3.5.17
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.5.17
      csstype: registry.npmmirror.com/csstype@3.1.3

  registry.npmmirror.com/@vue/server-renderer@3.5.17(vue@3.5.17):
    resolution: {integrity: sha512-BOHhm8HalujY6lmC3DbqF6uXN/K00uWiEeF22LfEsm9Q93XeJ/plHTepGwf6tqFcF7GA5oGSSAAUock3VvzaCA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/server-renderer/-/server-renderer-3.5.17.tgz}
    id: registry.npmmirror.com/@vue/server-renderer/3.5.17
    name: '@vue/server-renderer'
    version: 3.5.17
    peerDependencies:
      vue: 3.5.17
    dependencies:
      '@vue/compiler-ssr': registry.npmmirror.com/@vue/compiler-ssr@3.5.17
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.5.17
      vue: registry.npmmirror.com/vue@3.5.17

  registry.npmmirror.com/@vue/shared@3.5.17:
    resolution: {integrity: sha512-CabR+UN630VnsJO/jHWYBC1YVXyMq94KKp6iF5MQgZJs5I8cmjw6oVMO1oDbtBkENSHSSn/UadWlW/OAgdmKrg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/shared/-/shared-3.5.17.tgz}
    name: '@vue/shared'
    version: 3.5.17

  registry.npmmirror.com/csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/csstype/-/csstype-3.1.3.tgz}
    name: csstype
    version: 3.1.3

  registry.npmmirror.com/entities@4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/entities/-/entities-4.5.0.tgz}
    name: entities
    version: 4.5.0
    engines: {node: '>=0.12'}

  registry.npmmirror.com/esbuild@0.25.5:
    resolution: {integrity: sha512-P8OtKZRv/5J5hhz0cUAdu/cLuPIKXpQl1R9pZtvmHWQvrAUVd0UNIPT4IB4W3rNOqVO0rlqHmCIbSwxh/c9yUQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esbuild/-/esbuild-0.25.5.tgz}
    name: esbuild
    version: 0.25.5
    engines: {node: '>=18'}
    hasBin: true
    requiresBuild: true
    optionalDependencies:
      '@esbuild/aix-ppc64': registry.npmmirror.com/@esbuild/aix-ppc64@0.25.5
      '@esbuild/android-arm': registry.npmmirror.com/@esbuild/android-arm@0.25.5
      '@esbuild/android-arm64': registry.npmmirror.com/@esbuild/android-arm64@0.25.5
      '@esbuild/android-x64': registry.npmmirror.com/@esbuild/android-x64@0.25.5
      '@esbuild/darwin-arm64': registry.npmmirror.com/@esbuild/darwin-arm64@0.25.5
      '@esbuild/darwin-x64': registry.npmmirror.com/@esbuild/darwin-x64@0.25.5
      '@esbuild/freebsd-arm64': registry.npmmirror.com/@esbuild/freebsd-arm64@0.25.5
      '@esbuild/freebsd-x64': registry.npmmirror.com/@esbuild/freebsd-x64@0.25.5
      '@esbuild/linux-arm': registry.npmmirror.com/@esbuild/linux-arm@0.25.5
      '@esbuild/linux-arm64': registry.npmmirror.com/@esbuild/linux-arm64@0.25.5
      '@esbuild/linux-ia32': registry.npmmirror.com/@esbuild/linux-ia32@0.25.5
      '@esbuild/linux-loong64': registry.npmmirror.com/@esbuild/linux-loong64@0.25.5
      '@esbuild/linux-mips64el': registry.npmmirror.com/@esbuild/linux-mips64el@0.25.5
      '@esbuild/linux-ppc64': registry.npmmirror.com/@esbuild/linux-ppc64@0.25.5
      '@esbuild/linux-riscv64': registry.npmmirror.com/@esbuild/linux-riscv64@0.25.5
      '@esbuild/linux-s390x': registry.npmmirror.com/@esbuild/linux-s390x@0.25.5
      '@esbuild/linux-x64': registry.npmmirror.com/@esbuild/linux-x64@0.25.5
      '@esbuild/netbsd-arm64': registry.npmmirror.com/@esbuild/netbsd-arm64@0.25.5
      '@esbuild/netbsd-x64': registry.npmmirror.com/@esbuild/netbsd-x64@0.25.5
      '@esbuild/openbsd-arm64': registry.npmmirror.com/@esbuild/openbsd-arm64@0.25.5
      '@esbuild/openbsd-x64': registry.npmmirror.com/@esbuild/openbsd-x64@0.25.5
      '@esbuild/sunos-x64': registry.npmmirror.com/@esbuild/sunos-x64@0.25.5
      '@esbuild/win32-arm64': registry.npmmirror.com/@esbuild/win32-arm64@0.25.5
      '@esbuild/win32-ia32': registry.npmmirror.com/@esbuild/win32-ia32@0.25.5
      '@esbuild/win32-x64': registry.npmmirror.com/@esbuild/win32-x64@0.25.5
    dev: true

  registry.npmmirror.com/estree-walker@2.0.2:
    resolution: {integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/estree-walker/-/estree-walker-2.0.2.tgz}
    name: estree-walker
    version: 2.0.2

  registry.npmmirror.com/fdir@6.4.6(picomatch@4.0.2):
    resolution: {integrity: sha512-hiFoqpyZcfNm1yc4u8oWCf9A2c4D3QjCrks3zmoVKVxpQRzmPNar1hUJcBG2RQHvEVGDN+Jm81ZheVLAQMK6+w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fdir/-/fdir-6.4.6.tgz}
    id: registry.npmmirror.com/fdir/6.4.6
    name: fdir
    version: 6.4.6
    peerDependencies:
      picomatch: ^3 || ^4
    peerDependenciesMeta:
      picomatch:
        optional: true
    dependencies:
      picomatch: registry.npmmirror.com/picomatch@4.0.2
    dev: true

  registry.npmmirror.com/fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fsevents/-/fsevents-2.3.3.tgz}
    name: fsevents
    version: 2.3.3
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/magic-string@0.30.17:
    resolution: {integrity: sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/magic-string/-/magic-string-0.30.17.tgz}
    name: magic-string
    version: 0.30.17
    dependencies:
      '@jridgewell/sourcemap-codec': registry.npmmirror.com/@jridgewell/sourcemap-codec@1.5.1

  registry.npmmirror.com/nanoid@3.3.11:
    resolution: {integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/nanoid/-/nanoid-3.3.11.tgz}
    name: nanoid
    version: 3.3.11
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  registry.npmmirror.com/picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/picocolors/-/picocolors-1.1.1.tgz}
    name: picocolors
    version: 1.1.1

  registry.npmmirror.com/picomatch@4.0.2:
    resolution: {integrity: sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/picomatch/-/picomatch-4.0.2.tgz}
    name: picomatch
    version: 4.0.2
    engines: {node: '>=12'}
    dev: true

  registry.npmmirror.com/postcss@8.5.6:
    resolution: {integrity: sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/postcss/-/postcss-8.5.6.tgz}
    name: postcss
    version: 8.5.6
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: registry.npmmirror.com/nanoid@3.3.11
      picocolors: registry.npmmirror.com/picocolors@1.1.1
      source-map-js: registry.npmmirror.com/source-map-js@1.2.1

  registry.npmmirror.com/rollup@4.44.1:
    resolution: {integrity: sha512-x8H8aPvD+xbl0Do8oez5f5o8eMS3trfCghc4HhLAnCkj7Vl0d1JWGs0UF/D886zLW2rOj2QymV/JcSSsw+XDNg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/rollup/-/rollup-4.44.1.tgz}
    name: rollup
    version: 4.44.1
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true
    dependencies:
      '@types/estree': registry.npmmirror.com/@types/estree@1.0.8
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': registry.npmmirror.com/@rollup/rollup-android-arm-eabi@4.44.1
      '@rollup/rollup-android-arm64': registry.npmmirror.com/@rollup/rollup-android-arm64@4.44.1
      '@rollup/rollup-darwin-arm64': registry.npmmirror.com/@rollup/rollup-darwin-arm64@4.44.1
      '@rollup/rollup-darwin-x64': registry.npmmirror.com/@rollup/rollup-darwin-x64@4.44.1
      '@rollup/rollup-freebsd-arm64': registry.npmmirror.com/@rollup/rollup-freebsd-arm64@4.44.1
      '@rollup/rollup-freebsd-x64': registry.npmmirror.com/@rollup/rollup-freebsd-x64@4.44.1
      '@rollup/rollup-linux-arm-gnueabihf': registry.npmmirror.com/@rollup/rollup-linux-arm-gnueabihf@4.44.1
      '@rollup/rollup-linux-arm-musleabihf': registry.npmmirror.com/@rollup/rollup-linux-arm-musleabihf@4.44.1
      '@rollup/rollup-linux-arm64-gnu': registry.npmmirror.com/@rollup/rollup-linux-arm64-gnu@4.44.1
      '@rollup/rollup-linux-arm64-musl': registry.npmmirror.com/@rollup/rollup-linux-arm64-musl@4.44.1
      '@rollup/rollup-linux-loongarch64-gnu': registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu@4.44.1
      '@rollup/rollup-linux-powerpc64le-gnu': registry.npmmirror.com/@rollup/rollup-linux-powerpc64le-gnu@4.44.1
      '@rollup/rollup-linux-riscv64-gnu': registry.npmmirror.com/@rollup/rollup-linux-riscv64-gnu@4.44.1
      '@rollup/rollup-linux-riscv64-musl': registry.npmmirror.com/@rollup/rollup-linux-riscv64-musl@4.44.1
      '@rollup/rollup-linux-s390x-gnu': registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu@4.44.1
      '@rollup/rollup-linux-x64-gnu': registry.npmmirror.com/@rollup/rollup-linux-x64-gnu@4.44.1
      '@rollup/rollup-linux-x64-musl': registry.npmmirror.com/@rollup/rollup-linux-x64-musl@4.44.1
      '@rollup/rollup-win32-arm64-msvc': registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc@4.44.1
      '@rollup/rollup-win32-ia32-msvc': registry.npmmirror.com/@rollup/rollup-win32-ia32-msvc@4.44.1
      '@rollup/rollup-win32-x64-msvc': registry.npmmirror.com/@rollup/rollup-win32-x64-msvc@4.44.1
      fsevents: registry.npmmirror.com/fsevents@2.3.3
    dev: true

  registry.npmmirror.com/source-map-js@1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/source-map-js/-/source-map-js-1.2.1.tgz}
    name: source-map-js
    version: 1.2.1
    engines: {node: '>=0.10.0'}

  registry.npmmirror.com/tinyglobby@0.2.14:
    resolution: {integrity: sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/tinyglobby/-/tinyglobby-0.2.14.tgz}
    name: tinyglobby
    version: 0.2.14
    engines: {node: '>=12.0.0'}
    dependencies:
      fdir: registry.npmmirror.com/fdir@6.4.6(picomatch@4.0.2)
      picomatch: registry.npmmirror.com/picomatch@4.0.2
    dev: true

  registry.npmmirror.com/vite@7.0.0:
    resolution: {integrity: sha512-ixXJB1YRgDIw2OszKQS9WxGHKwLdCsbQNkpJN171udl6szi/rIySHL6/Os3s2+oE4P/FLD4dxg4mD7Wust+u5g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/vite/-/vite-7.0.0.tgz}
    name: vite
    version: 7.0.0
    engines: {node: ^20.19.0 || >=22.12.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^20.19.0 || >=22.12.0
      jiti: '>=1.21.0'
      less: ^4.0.0
      lightningcss: ^1.21.0
      sass: ^1.70.0
      sass-embedded: ^1.70.0
      stylus: '>=0.54.8'
      sugarss: ^5.0.0
      terser: ^5.16.0
      tsx: ^4.8.1
      yaml: ^2.4.2
    peerDependenciesMeta:
      '@types/node':
        optional: true
      jiti:
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
      tsx:
        optional: true
      yaml:
        optional: true
    dependencies:
      esbuild: registry.npmmirror.com/esbuild@0.25.5
      fdir: registry.npmmirror.com/fdir@6.4.6(picomatch@4.0.2)
      picomatch: registry.npmmirror.com/picomatch@4.0.2
      postcss: registry.npmmirror.com/postcss@8.5.6
      rollup: registry.npmmirror.com/rollup@4.44.1
      tinyglobby: registry.npmmirror.com/tinyglobby@0.2.14
    optionalDependencies:
      fsevents: registry.npmmirror.com/fsevents@2.3.3
    dev: true

  registry.npmmirror.com/vue@3.5.17:
    resolution: {integrity: sha512-LbHV3xPN9BeljML+Xctq4lbz2lVHCR6DtbpTf5XIO6gugpXUN49j2QQPcMj086r9+AkJ0FfUT8xjulKKBkkr9g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/vue/-/vue-3.5.17.tgz}
    name: vue
    version: 3.5.17
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@vue/compiler-dom': registry.npmmirror.com/@vue/compiler-dom@3.5.17
      '@vue/compiler-sfc': registry.npmmirror.com/@vue/compiler-sfc@3.5.17
      '@vue/runtime-dom': registry.npmmirror.com/@vue/runtime-dom@3.5.17
      '@vue/server-renderer': registry.npmmirror.com/@vue/server-renderer@3.5.17(vue@3.5.17)
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.5.17
