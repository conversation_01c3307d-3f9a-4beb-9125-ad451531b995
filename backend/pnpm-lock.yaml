lockfileVersion: '6.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

dependencies:
  '@nestjs/common':
    specifier: ^11.0.1
    version: registry.npmmirror.com/@nestjs/common@11.0.1(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.1)
  '@nestjs/config':
    specifier: ^4.0.2
    version: registry.npmmirror.com/@nestjs/config@4.0.2(@nestjs/common@11.0.1)(rxjs@7.8.1)
  '@nestjs/core':
    specifier: ^11.0.1
    version: registry.npmmirror.com/@nestjs/core@11.0.1(@nestjs/common@11.0.1)(@nestjs/platform-express@11.0.1)(reflect-metadata@0.2.2)(rxjs@7.8.1)
  '@nestjs/jwt':
    specifier: ^11.0.0
    version: registry.npmmirror.com/@nestjs/jwt@11.0.0(@nestjs/common@11.0.1)
  '@nestjs/passport':
    specifier: ^11.0.5
    version: registry.npmmirror.com/@nestjs/passport@11.0.5(@nestjs/common@11.0.1)(passport@0.7.0)
  '@nestjs/platform-express':
    specifier: ^11.0.1
    version: registry.npmmirror.com/@nestjs/platform-express@11.0.1(@nestjs/common@11.0.1)(@nestjs/core@11.0.1)
  '@nestjs/swagger':
    specifier: ^11.2.0
    version: registry.npmmirror.com/@nestjs/swagger@11.2.0(@nestjs/common@11.0.1)(@nestjs/core@11.0.1)(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)
  '@nestjs/typeorm':
    specifier: ^11.0.0
    version: registry.npmmirror.com/@nestjs/typeorm@11.0.0(@nestjs/common@11.0.1)(@nestjs/core@11.0.1)(reflect-metadata@0.2.2)(rxjs@7.8.1)(typeorm@0.3.25)
  alipay-sdk:
    specifier: ^4.14.0
    version: registry.npmmirror.com/alipay-sdk@4.14.0
  bcryptjs:
    specifier: ^3.0.2
    version: registry.npmmirror.com/bcryptjs@3.0.2
  class-transformer:
    specifier: ^0.5.1
    version: registry.npmmirror.com/class-transformer@0.5.1
  class-validator:
    specifier: ^0.14.2
    version: registry.npmmirror.com/class-validator@0.14.2
  mysql2:
    specifier: ^3.14.1
    version: registry.npmmirror.com/mysql2@3.14.1
  passport:
    specifier: ^0.7.0
    version: registry.npmmirror.com/passport@0.7.0
  passport-jwt:
    specifier: ^4.0.1
    version: registry.npmmirror.com/passport-jwt@4.0.1
  reflect-metadata:
    specifier: ^0.2.2
    version: registry.npmmirror.com/reflect-metadata@0.2.2
  rxjs:
    specifier: ^7.8.1
    version: registry.npmmirror.com/rxjs@7.8.1
  swagger-ui-express:
    specifier: ^5.0.1
    version: registry.npmmirror.com/swagger-ui-express@5.0.1(express@5.0.1)
  typeorm:
    specifier: ^0.3.25
    version: registry.npmmirror.com/typeorm@0.3.25(mysql2@3.14.1)(reflect-metadata@0.2.2)(ts-node@10.9.2)

devDependencies:
  '@eslint/eslintrc':
    specifier: ^3.2.0
    version: registry.npmmirror.com/@eslint/eslintrc@3.2.0
  '@eslint/js':
    specifier: ^9.18.0
    version: registry.npmmirror.com/@eslint/js@9.18.0
  '@nestjs/cli':
    specifier: ^11.0.0
    version: registry.npmmirror.com/@nestjs/cli@11.0.0(@swc/cli@0.6.0)(@swc/core@1.10.7)(@types/node@22.10.7)
  '@nestjs/schematics':
    specifier: ^11.0.0
    version: registry.npmmirror.com/@nestjs/schematics@11.0.0(chokidar@4.0.3)(typescript@5.7.3)
  '@nestjs/testing':
    specifier: ^11.0.1
    version: registry.npmmirror.com/@nestjs/testing@11.0.1(@nestjs/common@11.0.1)(@nestjs/core@11.0.1)(@nestjs/platform-express@11.0.1)
  '@swc/cli':
    specifier: ^0.6.0
    version: registry.npmmirror.com/@swc/cli@0.6.0(@swc/core@1.10.7)
  '@swc/core':
    specifier: ^1.10.7
    version: registry.npmmirror.com/@swc/core@1.10.7
  '@types/bcryptjs':
    specifier: ^3.0.0
    version: registry.npmmirror.com/@types/bcryptjs@3.0.0
  '@types/express':
    specifier: ^5.0.0
    version: registry.npmmirror.com/@types/express@5.0.0
  '@types/jest':
    specifier: ^29.5.14
    version: registry.npmmirror.com/@types/jest@29.5.14
  '@types/node':
    specifier: ^22.10.7
    version: registry.npmmirror.com/@types/node@22.10.7
  '@types/passport-jwt':
    specifier: ^4.0.1
    version: registry.npmmirror.com/@types/passport-jwt@4.0.1
  '@types/supertest':
    specifier: ^6.0.2
    version: registry.npmmirror.com/@types/supertest@6.0.2
  eslint:
    specifier: ^9.18.0
    version: registry.npmmirror.com/eslint@9.18.0
  eslint-config-prettier:
    specifier: ^10.0.1
    version: registry.npmmirror.com/eslint-config-prettier@10.0.1(eslint@9.18.0)
  eslint-plugin-prettier:
    specifier: ^5.2.2
    version: registry.npmmirror.com/eslint-plugin-prettier@5.2.2(eslint-config-prettier@10.0.1)(eslint@9.18.0)(prettier@3.4.2)
  globals:
    specifier: ^16.0.0
    version: registry.npmmirror.com/globals@16.0.0
  jest:
    specifier: ^29.7.0
    version: registry.npmmirror.com/jest@29.7.0(@types/node@22.10.7)(ts-node@10.9.2)
  prettier:
    specifier: ^3.4.2
    version: registry.npmmirror.com/prettier@3.4.2
  source-map-support:
    specifier: ^0.5.21
    version: registry.npmmirror.com/source-map-support@0.5.21
  supertest:
    specifier: ^7.0.0
    version: registry.npmmirror.com/supertest@7.0.0
  ts-jest:
    specifier: ^29.2.5
    version: registry.npmmirror.com/ts-jest@29.2.5(@babel/core@7.27.7)(jest@29.7.0)(typescript@5.7.3)
  ts-loader:
    specifier: ^9.5.2
    version: registry.npmmirror.com/ts-loader@9.5.2(typescript@5.7.3)(webpack@5.99.9)
  ts-node:
    specifier: ^10.9.2
    version: registry.npmmirror.com/ts-node@10.9.2(@swc/core@1.10.7)(@types/node@22.10.7)(typescript@5.7.3)
  tsconfig-paths:
    specifier: ^4.2.0
    version: registry.npmmirror.com/tsconfig-paths@4.2.0
  typescript:
    specifier: ^5.7.3
    version: registry.npmmirror.com/typescript@5.7.3
  typescript-eslint:
    specifier: ^8.20.0
    version: registry.npmmirror.com/typescript-eslint@8.20.0(eslint@9.18.0)(typescript@5.7.3)

packages:

  registry.npmmirror.com/@ampproject/remapping@2.3.0:
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@ampproject/remapping/-/remapping-2.3.0.tgz}
    name: '@ampproject/remapping'
    version: 2.3.0
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/gen-mapping': registry.npmmirror.com/@jridgewell/gen-mapping@0.3.8
      '@jridgewell/trace-mapping': registry.npmmirror.com/@jridgewell/trace-mapping@0.3.25
    dev: true

  registry.npmmirror.com/@angular-devkit/core@19.0.1(chokidar@4.0.3):
    resolution: {integrity: sha512-oXIAV3hXqUW3Pmm95pvEmb+24n1cKQG62FzhQSjOIrMeHiCbGLNuc8zHosIi2oMrcCJJxR6KzWjThvbuzDwWlw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@angular-devkit/core/-/core-19.0.1.tgz}
    id: registry.npmmirror.com/@angular-devkit/core/19.0.1
    name: '@angular-devkit/core'
    version: 19.0.1
    engines: {node: ^18.19.1 || ^20.11.1 || >=22.0.0, npm: ^6.11.0 || ^7.5.6 || >=8.0.0, yarn: '>= 1.13.0'}
    peerDependencies:
      chokidar: ^4.0.0
    peerDependenciesMeta:
      chokidar:
        optional: true
    dependencies:
      ajv: registry.npmmirror.com/ajv@8.17.1
      ajv-formats: registry.npmmirror.com/ajv-formats@3.0.1(ajv@8.17.1)
      chokidar: registry.npmmirror.com/chokidar@4.0.3
      jsonc-parser: registry.npmmirror.com/jsonc-parser@3.3.1
      picomatch: registry.npmmirror.com/picomatch@4.0.2
      rxjs: registry.npmmirror.com/rxjs@7.8.1
      source-map: registry.npmmirror.com/source-map@0.7.4
    dev: true

  registry.npmmirror.com/@angular-devkit/core@19.1.2(chokidar@4.0.3):
    resolution: {integrity: sha512-sCYu96C7ToeLGi+ggs2+TxOt1/5eC6WZe/SR7cLpf9rv+gi3ijbH2w+JP6aR4Ne12EL6WGkes42rfT5fL+Z4Gg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@angular-devkit/core/-/core-19.1.2.tgz}
    id: registry.npmmirror.com/@angular-devkit/core/19.1.2
    name: '@angular-devkit/core'
    version: 19.1.2
    engines: {node: ^18.19.1 || ^20.11.1 || >=22.0.0, npm: ^6.11.0 || ^7.5.6 || >=8.0.0, yarn: '>= 1.13.0'}
    peerDependencies:
      chokidar: ^4.0.0
    peerDependenciesMeta:
      chokidar:
        optional: true
    dependencies:
      ajv: registry.npmmirror.com/ajv@8.17.1
      ajv-formats: registry.npmmirror.com/ajv-formats@3.0.1(ajv@8.17.1)
      chokidar: registry.npmmirror.com/chokidar@4.0.3
      jsonc-parser: registry.npmmirror.com/jsonc-parser@3.3.1
      picomatch: registry.npmmirror.com/picomatch@4.0.2
      rxjs: registry.npmmirror.com/rxjs@7.8.1
      source-map: registry.npmmirror.com/source-map@0.7.4
    dev: true

  registry.npmmirror.com/@angular-devkit/schematics-cli@19.1.2(@types/node@22.10.7)(chokidar@4.0.3):
    resolution: {integrity: sha512-LMaZXLM5RD+W4ArvKSDrzVPtxXjlKm8Rzf9fs4rDBzEJo3JUy1MRXImhn08Nm4kBL0J/mllZCnXAifWWAygyYQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@angular-devkit/schematics-cli/-/schematics-cli-19.1.2.tgz}
    id: registry.npmmirror.com/@angular-devkit/schematics-cli/19.1.2
    name: '@angular-devkit/schematics-cli'
    version: 19.1.2
    engines: {node: ^18.19.1 || ^20.11.1 || >=22.0.0, npm: ^6.11.0 || ^7.5.6 || >=8.0.0, yarn: '>= 1.13.0'}
    hasBin: true
    dependencies:
      '@angular-devkit/core': registry.npmmirror.com/@angular-devkit/core@19.1.2(chokidar@4.0.3)
      '@angular-devkit/schematics': registry.npmmirror.com/@angular-devkit/schematics@19.1.2(chokidar@4.0.3)
      '@inquirer/prompts': registry.npmmirror.com/@inquirer/prompts@7.2.1(@types/node@22.10.7)
      ansi-colors: registry.npmmirror.com/ansi-colors@4.1.3
      symbol-observable: registry.npmmirror.com/symbol-observable@4.0.0
      yargs-parser: registry.npmmirror.com/yargs-parser@21.1.1
    transitivePeerDependencies:
      - '@types/node'
      - chokidar
    dev: true

  registry.npmmirror.com/@angular-devkit/schematics@19.0.1(chokidar@4.0.3):
    resolution: {integrity: sha512-N9dV8WpNRULykNj8fSxQrta85gPKxb315J3xugLS2uwiFWhz7wo5EY1YeYhoVKoVcNB2ng9imJgC5aO52AHZwg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@angular-devkit/schematics/-/schematics-19.0.1.tgz}
    id: registry.npmmirror.com/@angular-devkit/schematics/19.0.1
    name: '@angular-devkit/schematics'
    version: 19.0.1
    engines: {node: ^18.19.1 || ^20.11.1 || >=22.0.0, npm: ^6.11.0 || ^7.5.6 || >=8.0.0, yarn: '>= 1.13.0'}
    dependencies:
      '@angular-devkit/core': registry.npmmirror.com/@angular-devkit/core@19.0.1(chokidar@4.0.3)
      jsonc-parser: registry.npmmirror.com/jsonc-parser@3.3.1
      magic-string: registry.npmmirror.com/magic-string@0.30.12
      ora: registry.npmmirror.com/ora@5.4.1
      rxjs: registry.npmmirror.com/rxjs@7.8.1
    transitivePeerDependencies:
      - chokidar
    dev: true

  registry.npmmirror.com/@angular-devkit/schematics@19.1.2(chokidar@4.0.3):
    resolution: {integrity: sha512-+eeDvbTj37Yczs3S1Hy42Nr1OG5BHORDPdY+bLIP383aTFjfTtlMIya46iNvMCYjinvnQkC4GcKdsa4ews3c9A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@angular-devkit/schematics/-/schematics-19.1.2.tgz}
    id: registry.npmmirror.com/@angular-devkit/schematics/19.1.2
    name: '@angular-devkit/schematics'
    version: 19.1.2
    engines: {node: ^18.19.1 || ^20.11.1 || >=22.0.0, npm: ^6.11.0 || ^7.5.6 || >=8.0.0, yarn: '>= 1.13.0'}
    dependencies:
      '@angular-devkit/core': registry.npmmirror.com/@angular-devkit/core@19.1.2(chokidar@4.0.3)
      jsonc-parser: registry.npmmirror.com/jsonc-parser@3.3.1
      magic-string: registry.npmmirror.com/magic-string@0.30.17
      ora: registry.npmmirror.com/ora@5.4.1
      rxjs: registry.npmmirror.com/rxjs@7.8.1
    transitivePeerDependencies:
      - chokidar
    dev: true

  registry.npmmirror.com/@babel/code-frame@7.27.1:
    resolution: {integrity: sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.27.1.tgz}
    name: '@babel/code-frame'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': registry.npmmirror.com/@babel/helper-validator-identifier@7.27.1
      js-tokens: registry.npmmirror.com/js-tokens@4.0.0
      picocolors: registry.npmmirror.com/picocolors@1.1.1
    dev: true

  registry.npmmirror.com/@babel/compat-data@7.27.7:
    resolution: {integrity: sha512-xgu/ySj2mTiUFmdE9yCMfBxLp4DHd5DwmbbD05YAuICfodYT3VvRxbrh81LGQ/8UpSdtMdfKMn3KouYDX59DGQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/compat-data/-/compat-data-7.27.7.tgz}
    name: '@babel/compat-data'
    version: 7.27.7
    engines: {node: '>=6.9.0'}
    dev: true

  registry.npmmirror.com/@babel/core@7.27.7:
    resolution: {integrity: sha512-BU2f9tlKQ5CAthiMIgpzAh4eDTLWo1mqi9jqE2OxMG0E/OM199VJt2q8BztTxpnSW0i1ymdwLXRJnYzvDM5r2w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/core/-/core-7.27.7.tgz}
    name: '@babel/core'
    version: 7.27.7
    engines: {node: '>=6.9.0'}
    dependencies:
      '@ampproject/remapping': registry.npmmirror.com/@ampproject/remapping@2.3.0
      '@babel/code-frame': registry.npmmirror.com/@babel/code-frame@7.27.1
      '@babel/generator': registry.npmmirror.com/@babel/generator@7.27.5
      '@babel/helper-compilation-targets': registry.npmmirror.com/@babel/helper-compilation-targets@7.27.2
      '@babel/helper-module-transforms': registry.npmmirror.com/@babel/helper-module-transforms@7.27.3(@babel/core@7.27.7)
      '@babel/helpers': registry.npmmirror.com/@babel/helpers@7.27.6
      '@babel/parser': registry.npmmirror.com/@babel/parser@7.27.7
      '@babel/template': registry.npmmirror.com/@babel/template@7.27.2
      '@babel/traverse': registry.npmmirror.com/@babel/traverse@7.27.7
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.7
      convert-source-map: registry.npmmirror.com/convert-source-map@2.0.0
      debug: registry.npmmirror.com/debug@4.4.1
      gensync: registry.npmmirror.com/gensync@1.0.0-beta.2
      json5: registry.npmmirror.com/json5@2.2.3
      semver: registry.npmmirror.com/semver@6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@babel/generator@7.27.5:
    resolution: {integrity: sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/generator/-/generator-7.27.5.tgz}
    name: '@babel/generator'
    version: 7.27.5
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/parser': registry.npmmirror.com/@babel/parser@7.27.7
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.7
      '@jridgewell/gen-mapping': registry.npmmirror.com/@jridgewell/gen-mapping@0.3.8
      '@jridgewell/trace-mapping': registry.npmmirror.com/@jridgewell/trace-mapping@0.3.25
      jsesc: registry.npmmirror.com/jsesc@3.1.0
    dev: true

  registry.npmmirror.com/@babel/helper-compilation-targets@7.27.2:
    resolution: {integrity: sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz}
    name: '@babel/helper-compilation-targets'
    version: 7.27.2
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/compat-data': registry.npmmirror.com/@babel/compat-data@7.27.7
      '@babel/helper-validator-option': registry.npmmirror.com/@babel/helper-validator-option@7.27.1
      browserslist: registry.npmmirror.com/browserslist@4.25.1
      lru-cache: registry.npmmirror.com/lru-cache@5.1.1
      semver: registry.npmmirror.com/semver@6.3.1
    dev: true

  registry.npmmirror.com/@babel/helper-module-imports@7.27.1:
    resolution: {integrity: sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz}
    name: '@babel/helper-module-imports'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': registry.npmmirror.com/@babel/traverse@7.27.7
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.7
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@babel/helper-module-transforms@7.27.3(@babel/core@7.27.7):
    resolution: {integrity: sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz}
    id: registry.npmmirror.com/@babel/helper-module-transforms/7.27.3
    name: '@babel/helper-module-transforms'
    version: 7.27.3
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@babel/helper-module-imports': registry.npmmirror.com/@babel/helper-module-imports@7.27.1
      '@babel/helper-validator-identifier': registry.npmmirror.com/@babel/helper-validator-identifier@7.27.1
      '@babel/traverse': registry.npmmirror.com/@babel/traverse@7.27.7
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1:
    resolution: {integrity: sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz}
    name: '@babel/helper-plugin-utils'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dev: true

  registry.npmmirror.com/@babel/helper-string-parser@7.27.1:
    resolution: {integrity: sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz}
    name: '@babel/helper-string-parser'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dev: true

  registry.npmmirror.com/@babel/helper-validator-identifier@7.27.1:
    resolution: {integrity: sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz}
    name: '@babel/helper-validator-identifier'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dev: true

  registry.npmmirror.com/@babel/helper-validator-option@7.27.1:
    resolution: {integrity: sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz}
    name: '@babel/helper-validator-option'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dev: true

  registry.npmmirror.com/@babel/helpers@7.27.6:
    resolution: {integrity: sha512-muE8Tt8M22638HU31A3CgfSUciwz1fhATfoVai05aPXGor//CdWDCbnlY1yvBPo07njuVOCNGCSp/GTt12lIug==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helpers/-/helpers-7.27.6.tgz}
    name: '@babel/helpers'
    version: 7.27.6
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': registry.npmmirror.com/@babel/template@7.27.2
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.7
    dev: true

  registry.npmmirror.com/@babel/parser@7.27.7:
    resolution: {integrity: sha512-qnzXzDXdr/po3bOTbTIQZ7+TxNKxpkN5IifVLXS+r7qwynkZfPyjZfE7hCXbo7IoO9TNcSyibgONsf2HauUd3Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/parser/-/parser-7.27.7.tgz}
    name: '@babel/parser'
    version: 7.27.7
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.7
    dev: true

  registry.npmmirror.com/@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.27.7):
    resolution: {integrity: sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz}
    id: registry.npmmirror.com/@babel/plugin-syntax-async-generators/7.8.4
    name: '@babel/plugin-syntax-async-generators'
    version: 7.8.4
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
    dev: true

  registry.npmmirror.com/@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.27.7):
    resolution: {integrity: sha512-wnTnFlG+YxQm3vDxpGE57Pj0srRU4sHE/mDkt1qv2YJJSeUAec2ma4WLUnUPeKjyrfntVwe/N6dCXpU+zL3Npg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz}
    id: registry.npmmirror.com/@babel/plugin-syntax-bigint/7.8.3
    name: '@babel/plugin-syntax-bigint'
    version: 7.8.3
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
    dev: true

  registry.npmmirror.com/@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.27.7):
    resolution: {integrity: sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz}
    id: registry.npmmirror.com/@babel/plugin-syntax-class-properties/7.12.13
    name: '@babel/plugin-syntax-class-properties'
    version: 7.12.13
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
    dev: true

  registry.npmmirror.com/@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.27.7):
    resolution: {integrity: sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz}
    id: registry.npmmirror.com/@babel/plugin-syntax-class-static-block/7.14.5
    name: '@babel/plugin-syntax-class-static-block'
    version: 7.14.5
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
    dev: true

  registry.npmmirror.com/@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.27.7):
    resolution: {integrity: sha512-oFT0FrKHgF53f4vOsZGi2Hh3I35PfSmVs4IBFLFj4dnafP+hIWDLg3VyKmUHfLoLHlyxY4C7DGtmHuJgn+IGww==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz}
    id: registry.npmmirror.com/@babel/plugin-syntax-import-attributes/7.27.1
    name: '@babel/plugin-syntax-import-attributes'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
    dev: true

  registry.npmmirror.com/@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.27.7):
    resolution: {integrity: sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz}
    id: registry.npmmirror.com/@babel/plugin-syntax-import-meta/7.10.4
    name: '@babel/plugin-syntax-import-meta'
    version: 7.10.4
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
    dev: true

  registry.npmmirror.com/@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.27.7):
    resolution: {integrity: sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz}
    id: registry.npmmirror.com/@babel/plugin-syntax-json-strings/7.8.3
    name: '@babel/plugin-syntax-json-strings'
    version: 7.8.3
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
    dev: true

  registry.npmmirror.com/@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.27.7):
    resolution: {integrity: sha512-y8YTNIeKoyhGd9O0Jiyzyyqk8gdjnumGTQPsz0xOZOQ2RmkVJeZ1vmmfIvFEKqucBG6axJGBZDE/7iI5suUI/w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz}
    id: registry.npmmirror.com/@babel/plugin-syntax-jsx/7.27.1
    name: '@babel/plugin-syntax-jsx'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
    dev: true

  registry.npmmirror.com/@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.27.7):
    resolution: {integrity: sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz}
    id: registry.npmmirror.com/@babel/plugin-syntax-logical-assignment-operators/7.10.4
    name: '@babel/plugin-syntax-logical-assignment-operators'
    version: 7.10.4
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
    dev: true

  registry.npmmirror.com/@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.27.7):
    resolution: {integrity: sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz}
    id: registry.npmmirror.com/@babel/plugin-syntax-nullish-coalescing-operator/7.8.3
    name: '@babel/plugin-syntax-nullish-coalescing-operator'
    version: 7.8.3
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
    dev: true

  registry.npmmirror.com/@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.27.7):
    resolution: {integrity: sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz}
    id: registry.npmmirror.com/@babel/plugin-syntax-numeric-separator/7.10.4
    name: '@babel/plugin-syntax-numeric-separator'
    version: 7.10.4
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
    dev: true

  registry.npmmirror.com/@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.27.7):
    resolution: {integrity: sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz}
    id: registry.npmmirror.com/@babel/plugin-syntax-object-rest-spread/7.8.3
    name: '@babel/plugin-syntax-object-rest-spread'
    version: 7.8.3
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
    dev: true

  registry.npmmirror.com/@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.27.7):
    resolution: {integrity: sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz}
    id: registry.npmmirror.com/@babel/plugin-syntax-optional-catch-binding/7.8.3
    name: '@babel/plugin-syntax-optional-catch-binding'
    version: 7.8.3
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
    dev: true

  registry.npmmirror.com/@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.27.7):
    resolution: {integrity: sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz}
    id: registry.npmmirror.com/@babel/plugin-syntax-optional-chaining/7.8.3
    name: '@babel/plugin-syntax-optional-chaining'
    version: 7.8.3
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
    dev: true

  registry.npmmirror.com/@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.27.7):
    resolution: {integrity: sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz}
    id: registry.npmmirror.com/@babel/plugin-syntax-private-property-in-object/7.14.5
    name: '@babel/plugin-syntax-private-property-in-object'
    version: 7.14.5
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
    dev: true

  registry.npmmirror.com/@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.27.7):
    resolution: {integrity: sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz}
    id: registry.npmmirror.com/@babel/plugin-syntax-top-level-await/7.14.5
    name: '@babel/plugin-syntax-top-level-await'
    version: 7.14.5
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
    dev: true

  registry.npmmirror.com/@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.27.7):
    resolution: {integrity: sha512-xfYCBMxveHrRMnAWl1ZlPXOZjzkN82THFvLhQhFXFt81Z5HnN+EtUkZhv/zcKpmT3fzmWZB0ywiBrbC3vogbwQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz}
    id: registry.npmmirror.com/@babel/plugin-syntax-typescript/7.27.1
    name: '@babel/plugin-syntax-typescript'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
    dev: true

  registry.npmmirror.com/@babel/template@7.27.2:
    resolution: {integrity: sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/template/-/template-7.27.2.tgz}
    name: '@babel/template'
    version: 7.27.2
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': registry.npmmirror.com/@babel/code-frame@7.27.1
      '@babel/parser': registry.npmmirror.com/@babel/parser@7.27.7
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.7
    dev: true

  registry.npmmirror.com/@babel/traverse@7.27.7:
    resolution: {integrity: sha512-X6ZlfR/O/s5EQ/SnUSLzr+6kGnkg8HXGMzpgsMsrJVcfDtH1vIp6ctCN4eZ1LS5c0+te5Cb6Y514fASjMRJ1nw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/traverse/-/traverse-7.27.7.tgz}
    name: '@babel/traverse'
    version: 7.27.7
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': registry.npmmirror.com/@babel/code-frame@7.27.1
      '@babel/generator': registry.npmmirror.com/@babel/generator@7.27.5
      '@babel/parser': registry.npmmirror.com/@babel/parser@7.27.7
      '@babel/template': registry.npmmirror.com/@babel/template@7.27.2
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.7
      debug: registry.npmmirror.com/debug@4.4.1
      globals: registry.npmmirror.com/globals@11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@babel/types@7.27.7:
    resolution: {integrity: sha512-8OLQgDScAOHXnAz2cV+RfzzNMipuLVBz2biuAJFMV9bfkNf393je3VM8CLkjQodW5+iWsSJdSgSWT6rsZoXHPw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/types/-/types-7.27.7.tgz}
    name: '@babel/types'
    version: 7.27.7
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': registry.npmmirror.com/@babel/helper-string-parser@7.27.1
      '@babel/helper-validator-identifier': registry.npmmirror.com/@babel/helper-validator-identifier@7.27.1
    dev: true

  registry.npmmirror.com/@bcoe/v8-coverage@0.2.3:
    resolution: {integrity: sha512-0hYQ8SB4Db5zvZB4axdMHGwEaQjkZzFjQiN9LVYvIFB2nSUHW9tYpxWriPrWDASIxiaXax83REcLxuSdnGPZtw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz}
    name: '@bcoe/v8-coverage'
    version: 0.2.3
    dev: true

  registry.npmmirror.com/@colors/colors@1.5.0:
    resolution: {integrity: sha512-ooWCrlZP11i8GImSjTHYHLkvFDP48nS4+204nGb1RiX/WXYHmJA2III9/e2DWVabCESdW7hBAEzHRqUn9OUVvQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@colors/colors/-/colors-1.5.0.tgz}
    name: '@colors/colors'
    version: 1.5.0
    engines: {node: '>=0.1.90'}
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@cspotcode/source-map-support@0.8.1:
    resolution: {integrity: sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@cspotcode/source-map-support/-/source-map-support-0.8.1.tgz}
    name: '@cspotcode/source-map-support'
    version: 0.8.1
    engines: {node: '>=12'}
    dependencies:
      '@jridgewell/trace-mapping': registry.npmmirror.com/@jridgewell/trace-mapping@0.3.9

  registry.npmmirror.com/@eslint-community/eslint-utils@4.7.0(eslint@9.18.0):
    resolution: {integrity: sha512-dyybb3AcajC7uha6CvhdVRJqaKyn7w2YKqKyAN37NKYgZT36w+iRb0Dymmc5qEJ549c/S31cMMSFd75bteCpCw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz}
    id: registry.npmmirror.com/@eslint-community/eslint-utils/4.7.0
    name: '@eslint-community/eslint-utils'
    version: 4.7.0
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
    dependencies:
      eslint: registry.npmmirror.com/eslint@9.18.0
      eslint-visitor-keys: registry.npmmirror.com/eslint-visitor-keys@3.4.3
    dev: true

  registry.npmmirror.com/@eslint-community/regexpp@4.12.1:
    resolution: {integrity: sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@eslint-community/regexpp/-/regexpp-4.12.1.tgz}
    name: '@eslint-community/regexpp'
    version: 4.12.1
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}
    dev: true

  registry.npmmirror.com/@eslint/config-array@0.19.2:
    resolution: {integrity: sha512-GNKqxfHG2ySmJOBSHg7LxeUx4xpuCoFjacmlCoYWEbaPXLwvfIjixRI12xCQZeULksQb23uiA8F40w5TojpV7w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@eslint/config-array/-/config-array-0.19.2.tgz}
    name: '@eslint/config-array'
    version: 0.19.2
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      '@eslint/object-schema': registry.npmmirror.com/@eslint/object-schema@2.1.6
      debug: registry.npmmirror.com/debug@4.4.1
      minimatch: registry.npmmirror.com/minimatch@3.1.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@eslint/core@0.10.0:
    resolution: {integrity: sha512-gFHJ+xBOo4G3WRlR1e/3G8A6/KZAH6zcE/hkLRCZTi/B9avAG365QhFA8uOGzTMqgTghpn7/fSnscW++dpMSAw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@eslint/core/-/core-0.10.0.tgz}
    name: '@eslint/core'
    version: 0.10.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      '@types/json-schema': registry.npmmirror.com/@types/json-schema@7.0.15
    dev: true

  registry.npmmirror.com/@eslint/core@0.13.0:
    resolution: {integrity: sha512-yfkgDw1KR66rkT5A8ci4irzDysN7FRpq3ttJolR88OqQikAWqwA8j5VZyas+vjyBNFIJ7MfybJ9plMILI2UrCw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@eslint/core/-/core-0.13.0.tgz}
    name: '@eslint/core'
    version: 0.13.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      '@types/json-schema': registry.npmmirror.com/@types/json-schema@7.0.15
    dev: true

  registry.npmmirror.com/@eslint/eslintrc@3.2.0:
    resolution: {integrity: sha512-grOjVNN8P3hjJn/eIETF1wwd12DdnwFDoyceUJLYYdkpbwq3nLi+4fqrTAONx7XDALqlL220wC/RHSC/QTI/0w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@eslint/eslintrc/-/eslintrc-3.2.0.tgz}
    name: '@eslint/eslintrc'
    version: 3.2.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      ajv: registry.npmmirror.com/ajv@6.12.6
      debug: registry.npmmirror.com/debug@4.4.1
      espree: registry.npmmirror.com/espree@10.4.0
      globals: registry.npmmirror.com/globals@14.0.0
      ignore: registry.npmmirror.com/ignore@5.3.2
      import-fresh: registry.npmmirror.com/import-fresh@3.3.1
      js-yaml: registry.npmmirror.com/js-yaml@4.1.0
      minimatch: registry.npmmirror.com/minimatch@3.1.2
      strip-json-comments: registry.npmmirror.com/strip-json-comments@3.1.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@eslint/js@9.18.0:
    resolution: {integrity: sha512-fK6L7rxcq6/z+AaQMtiFTkvbHkBLNlwyRxHpKawP0x3u9+NC6MQTnFW+AdpwC6gfHTW0051cokQgtTN2FqlxQA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@eslint/js/-/js-9.18.0.tgz}
    name: '@eslint/js'
    version: 9.18.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dev: true

  registry.npmmirror.com/@eslint/object-schema@2.1.6:
    resolution: {integrity: sha512-RBMg5FRL0I0gs51M/guSAj5/e14VQ4tpZnQNWwuDT66P14I43ItmPfIZRhO9fUVIPOAQXU47atlywZ/czoqFPA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@eslint/object-schema/-/object-schema-2.1.6.tgz}
    name: '@eslint/object-schema'
    version: 2.1.6
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dev: true

  registry.npmmirror.com/@eslint/plugin-kit@0.2.8:
    resolution: {integrity: sha512-ZAoA40rNMPwSm+AeHpCq8STiNAwzWLJuP8Xv4CHIc9wv/PSuExjMrmjfYNj682vW0OOiZ1HKxzvjQr9XZIisQA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@eslint/plugin-kit/-/plugin-kit-0.2.8.tgz}
    name: '@eslint/plugin-kit'
    version: 0.2.8
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      '@eslint/core': registry.npmmirror.com/@eslint/core@0.13.0
      levn: registry.npmmirror.com/levn@0.4.1
    dev: true

  registry.npmmirror.com/@fidm/asn1@1.0.4:
    resolution: {integrity: sha512-esd1jyNvRb2HVaQGq2Gg8Z0kbQPXzV9Tq5Z14KNIov6KfFD6PTaRIO8UpcsYiTNzOqJpmyzWgVTrUwFV3UF4TQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@fidm/asn1/-/asn1-1.0.4.tgz}
    name: '@fidm/asn1'
    version: 1.0.4
    engines: {node: '>= 8'}
    dev: false

  registry.npmmirror.com/@fidm/x509@1.2.1:
    resolution: {integrity: sha512-nwc2iesjyc9hkuzcrMCBXQRn653XuAUKorfWM8PZyJawiy1QzLj4vahwzaI25+pfpwOLvMzbJ0uKpWLDNmo16w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@fidm/x509/-/x509-1.2.1.tgz}
    name: '@fidm/x509'
    version: 1.2.1
    engines: {node: '>= 8'}
    dependencies:
      '@fidm/asn1': registry.npmmirror.com/@fidm/asn1@1.0.4
      tweetnacl: registry.npmmirror.com/tweetnacl@1.0.3
    dev: false

  registry.npmmirror.com/@humanfs/core@0.19.1:
    resolution: {integrity: sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@humanfs/core/-/core-0.19.1.tgz}
    name: '@humanfs/core'
    version: 0.19.1
    engines: {node: '>=18.18.0'}
    dev: true

  registry.npmmirror.com/@humanfs/node@0.16.6:
    resolution: {integrity: sha512-YuI2ZHQL78Q5HbhDiBA1X4LmYdXCKCMQIfw0pw7piHJwyREFebJUvrQN4cMssyES6x+vfUbx1CIpaQUKYdQZOw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@humanfs/node/-/node-0.16.6.tgz}
    name: '@humanfs/node'
    version: 0.16.6
    engines: {node: '>=18.18.0'}
    dependencies:
      '@humanfs/core': registry.npmmirror.com/@humanfs/core@0.19.1
      '@humanwhocodes/retry': registry.npmmirror.com/@humanwhocodes/retry@0.3.1
    dev: true

  registry.npmmirror.com/@humanwhocodes/module-importer@1.0.1:
    resolution: {integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz}
    name: '@humanwhocodes/module-importer'
    version: 1.0.1
    engines: {node: '>=12.22'}
    dev: true

  registry.npmmirror.com/@humanwhocodes/retry@0.3.1:
    resolution: {integrity: sha512-JBxkERygn7Bv/GbN5Rv8Ul6LVknS+5Bp6RgDC/O8gEBU/yeH5Ui5C/OlWrTb6qct7LjjfT6Re2NxB0ln0yYybA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@humanwhocodes/retry/-/retry-0.3.1.tgz}
    name: '@humanwhocodes/retry'
    version: 0.3.1
    engines: {node: '>=18.18'}
    dev: true

  registry.npmmirror.com/@humanwhocodes/retry@0.4.3:
    resolution: {integrity: sha512-bV0Tgo9K4hfPCek+aMAn81RppFKv2ySDQeMoSZuvTASywNTnVJCArCZE2FWqpvIatKu7VMRLWlR1EazvVhDyhQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@humanwhocodes/retry/-/retry-0.4.3.tgz}
    name: '@humanwhocodes/retry'
    version: 0.4.3
    engines: {node: '>=18.18'}
    dev: true

  registry.npmmirror.com/@inquirer/checkbox@4.1.8(@types/node@22.10.7):
    resolution: {integrity: sha512-d/QAsnwuHX2OPolxvYcgSj7A9DO9H6gVOy2DvBTx+P2LH2iRTo/RSGV3iwCzW024nP9hw98KIuDmdyhZQj1UQg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@inquirer/checkbox/-/checkbox-4.1.8.tgz}
    id: registry.npmmirror.com/@inquirer/checkbox/4.1.8
    name: '@inquirer/checkbox'
    version: 4.1.8
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true
    dependencies:
      '@inquirer/core': registry.npmmirror.com/@inquirer/core@10.1.13(@types/node@22.10.7)
      '@inquirer/figures': registry.npmmirror.com/@inquirer/figures@1.0.12
      '@inquirer/type': registry.npmmirror.com/@inquirer/type@3.0.7(@types/node@22.10.7)
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      ansi-escapes: registry.npmmirror.com/ansi-escapes@4.3.2
      yoctocolors-cjs: registry.npmmirror.com/yoctocolors-cjs@2.1.2
    dev: true

  registry.npmmirror.com/@inquirer/confirm@5.1.12(@types/node@22.10.7):
    resolution: {integrity: sha512-dpq+ielV9/bqgXRUbNH//KsY6WEw9DrGPmipkpmgC1Y46cwuBTNx7PXFWTjc3MQ+urcc0QxoVHcMI0FW4Ok0hg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@inquirer/confirm/-/confirm-5.1.12.tgz}
    id: registry.npmmirror.com/@inquirer/confirm/5.1.12
    name: '@inquirer/confirm'
    version: 5.1.12
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true
    dependencies:
      '@inquirer/core': registry.npmmirror.com/@inquirer/core@10.1.13(@types/node@22.10.7)
      '@inquirer/type': registry.npmmirror.com/@inquirer/type@3.0.7(@types/node@22.10.7)
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
    dev: true

  registry.npmmirror.com/@inquirer/core@10.1.13(@types/node@22.10.7):
    resolution: {integrity: sha512-1viSxebkYN2nJULlzCxES6G9/stgHSepZ9LqqfdIGPHj5OHhiBUXVS0a6R0bEC2A+VL4D9w6QB66ebCr6HGllA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@inquirer/core/-/core-10.1.13.tgz}
    id: registry.npmmirror.com/@inquirer/core/10.1.13
    name: '@inquirer/core'
    version: 10.1.13
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true
    dependencies:
      '@inquirer/figures': registry.npmmirror.com/@inquirer/figures@1.0.12
      '@inquirer/type': registry.npmmirror.com/@inquirer/type@3.0.7(@types/node@22.10.7)
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      ansi-escapes: registry.npmmirror.com/ansi-escapes@4.3.2
      cli-width: registry.npmmirror.com/cli-width@4.1.0
      mute-stream: registry.npmmirror.com/mute-stream@2.0.0
      signal-exit: registry.npmmirror.com/signal-exit@4.1.0
      wrap-ansi: registry.npmmirror.com/wrap-ansi@6.2.0
      yoctocolors-cjs: registry.npmmirror.com/yoctocolors-cjs@2.1.2
    dev: true

  registry.npmmirror.com/@inquirer/editor@4.2.13(@types/node@22.10.7):
    resolution: {integrity: sha512-WbicD9SUQt/K8O5Vyk9iC2ojq5RHoCLK6itpp2fHsWe44VxxcA9z3GTWlvjSTGmMQpZr+lbVmrxdHcumJoLbMA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@inquirer/editor/-/editor-4.2.13.tgz}
    id: registry.npmmirror.com/@inquirer/editor/4.2.13
    name: '@inquirer/editor'
    version: 4.2.13
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true
    dependencies:
      '@inquirer/core': registry.npmmirror.com/@inquirer/core@10.1.13(@types/node@22.10.7)
      '@inquirer/type': registry.npmmirror.com/@inquirer/type@3.0.7(@types/node@22.10.7)
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      external-editor: registry.npmmirror.com/external-editor@3.1.0
    dev: true

  registry.npmmirror.com/@inquirer/expand@4.0.15(@types/node@22.10.7):
    resolution: {integrity: sha512-4Y+pbr/U9Qcvf+N/goHzPEXiHH8680lM3Dr3Y9h9FFw4gHS+zVpbj8LfbKWIb/jayIB4aSO4pWiBTrBYWkvi5A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@inquirer/expand/-/expand-4.0.15.tgz}
    id: registry.npmmirror.com/@inquirer/expand/4.0.15
    name: '@inquirer/expand'
    version: 4.0.15
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true
    dependencies:
      '@inquirer/core': registry.npmmirror.com/@inquirer/core@10.1.13(@types/node@22.10.7)
      '@inquirer/type': registry.npmmirror.com/@inquirer/type@3.0.7(@types/node@22.10.7)
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      yoctocolors-cjs: registry.npmmirror.com/yoctocolors-cjs@2.1.2
    dev: true

  registry.npmmirror.com/@inquirer/figures@1.0.12:
    resolution: {integrity: sha512-MJttijd8rMFcKJC8NYmprWr6hD3r9Gd9qUC0XwPNwoEPWSMVJwA2MlXxF+nhZZNMY+HXsWa+o7KY2emWYIn0jQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@inquirer/figures/-/figures-1.0.12.tgz}
    name: '@inquirer/figures'
    version: 1.0.12
    engines: {node: '>=18'}
    dev: true

  registry.npmmirror.com/@inquirer/input@4.1.12(@types/node@22.10.7):
    resolution: {integrity: sha512-xJ6PFZpDjC+tC1P8ImGprgcsrzQRsUh9aH3IZixm1lAZFK49UGHxM3ltFfuInN2kPYNfyoPRh+tU4ftsjPLKqQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@inquirer/input/-/input-4.1.12.tgz}
    id: registry.npmmirror.com/@inquirer/input/4.1.12
    name: '@inquirer/input'
    version: 4.1.12
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true
    dependencies:
      '@inquirer/core': registry.npmmirror.com/@inquirer/core@10.1.13(@types/node@22.10.7)
      '@inquirer/type': registry.npmmirror.com/@inquirer/type@3.0.7(@types/node@22.10.7)
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
    dev: true

  registry.npmmirror.com/@inquirer/number@3.0.15(@types/node@22.10.7):
    resolution: {integrity: sha512-xWg+iYfqdhRiM55MvqiTCleHzszpoigUpN5+t1OMcRkJrUrw7va3AzXaxvS+Ak7Gny0j2mFSTv2JJj8sMtbV2g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@inquirer/number/-/number-3.0.15.tgz}
    id: registry.npmmirror.com/@inquirer/number/3.0.15
    name: '@inquirer/number'
    version: 3.0.15
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true
    dependencies:
      '@inquirer/core': registry.npmmirror.com/@inquirer/core@10.1.13(@types/node@22.10.7)
      '@inquirer/type': registry.npmmirror.com/@inquirer/type@3.0.7(@types/node@22.10.7)
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
    dev: true

  registry.npmmirror.com/@inquirer/password@4.0.15(@types/node@22.10.7):
    resolution: {integrity: sha512-75CT2p43DGEnfGTaqFpbDC2p2EEMrq0S+IRrf9iJvYreMy5mAWj087+mdKyLHapUEPLjN10mNvABpGbk8Wdraw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@inquirer/password/-/password-4.0.15.tgz}
    id: registry.npmmirror.com/@inquirer/password/4.0.15
    name: '@inquirer/password'
    version: 4.0.15
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true
    dependencies:
      '@inquirer/core': registry.npmmirror.com/@inquirer/core@10.1.13(@types/node@22.10.7)
      '@inquirer/type': registry.npmmirror.com/@inquirer/type@3.0.7(@types/node@22.10.7)
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      ansi-escapes: registry.npmmirror.com/ansi-escapes@4.3.2
    dev: true

  registry.npmmirror.com/@inquirer/prompts@7.2.1(@types/node@22.10.7):
    resolution: {integrity: sha512-v2JSGri6/HXSfoGIwuKEn8sNCQK6nsB2BNpy2lSX6QH9bsECrMv93QHnj5+f+1ZWpF/VNioIV2B/PDox8EvGuQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@inquirer/prompts/-/prompts-7.2.1.tgz}
    id: registry.npmmirror.com/@inquirer/prompts/7.2.1
    name: '@inquirer/prompts'
    version: 7.2.1
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    dependencies:
      '@inquirer/checkbox': registry.npmmirror.com/@inquirer/checkbox@4.1.8(@types/node@22.10.7)
      '@inquirer/confirm': registry.npmmirror.com/@inquirer/confirm@5.1.12(@types/node@22.10.7)
      '@inquirer/editor': registry.npmmirror.com/@inquirer/editor@4.2.13(@types/node@22.10.7)
      '@inquirer/expand': registry.npmmirror.com/@inquirer/expand@4.0.15(@types/node@22.10.7)
      '@inquirer/input': registry.npmmirror.com/@inquirer/input@4.1.12(@types/node@22.10.7)
      '@inquirer/number': registry.npmmirror.com/@inquirer/number@3.0.15(@types/node@22.10.7)
      '@inquirer/password': registry.npmmirror.com/@inquirer/password@4.0.15(@types/node@22.10.7)
      '@inquirer/rawlist': registry.npmmirror.com/@inquirer/rawlist@4.1.3(@types/node@22.10.7)
      '@inquirer/search': registry.npmmirror.com/@inquirer/search@3.0.15(@types/node@22.10.7)
      '@inquirer/select': registry.npmmirror.com/@inquirer/select@4.2.3(@types/node@22.10.7)
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
    dev: true

  registry.npmmirror.com/@inquirer/prompts@7.2.3(@types/node@22.10.7):
    resolution: {integrity: sha512-hzfnm3uOoDySDXfDNOm9usOuYIaQvTgKp/13l1uJoe6UNY+Zpcn2RYt0jXz3yA+yemGHvDOxVzqWl3S5sQq53Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@inquirer/prompts/-/prompts-7.2.3.tgz}
    id: registry.npmmirror.com/@inquirer/prompts/7.2.3
    name: '@inquirer/prompts'
    version: 7.2.3
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    dependencies:
      '@inquirer/checkbox': registry.npmmirror.com/@inquirer/checkbox@4.1.8(@types/node@22.10.7)
      '@inquirer/confirm': registry.npmmirror.com/@inquirer/confirm@5.1.12(@types/node@22.10.7)
      '@inquirer/editor': registry.npmmirror.com/@inquirer/editor@4.2.13(@types/node@22.10.7)
      '@inquirer/expand': registry.npmmirror.com/@inquirer/expand@4.0.15(@types/node@22.10.7)
      '@inquirer/input': registry.npmmirror.com/@inquirer/input@4.1.12(@types/node@22.10.7)
      '@inquirer/number': registry.npmmirror.com/@inquirer/number@3.0.15(@types/node@22.10.7)
      '@inquirer/password': registry.npmmirror.com/@inquirer/password@4.0.15(@types/node@22.10.7)
      '@inquirer/rawlist': registry.npmmirror.com/@inquirer/rawlist@4.1.3(@types/node@22.10.7)
      '@inquirer/search': registry.npmmirror.com/@inquirer/search@3.0.15(@types/node@22.10.7)
      '@inquirer/select': registry.npmmirror.com/@inquirer/select@4.2.3(@types/node@22.10.7)
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
    dev: true

  registry.npmmirror.com/@inquirer/rawlist@4.1.3(@types/node@22.10.7):
    resolution: {integrity: sha512-7XrV//6kwYumNDSsvJIPeAqa8+p7GJh7H5kRuxirct2cgOcSWwwNGoXDRgpNFbY/MG2vQ4ccIWCi8+IXXyFMZA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@inquirer/rawlist/-/rawlist-4.1.3.tgz}
    id: registry.npmmirror.com/@inquirer/rawlist/4.1.3
    name: '@inquirer/rawlist'
    version: 4.1.3
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true
    dependencies:
      '@inquirer/core': registry.npmmirror.com/@inquirer/core@10.1.13(@types/node@22.10.7)
      '@inquirer/type': registry.npmmirror.com/@inquirer/type@3.0.7(@types/node@22.10.7)
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      yoctocolors-cjs: registry.npmmirror.com/yoctocolors-cjs@2.1.2
    dev: true

  registry.npmmirror.com/@inquirer/search@3.0.15(@types/node@22.10.7):
    resolution: {integrity: sha512-YBMwPxYBrADqyvP4nNItpwkBnGGglAvCLVW8u4pRmmvOsHUtCAUIMbUrLX5B3tFL1/WsLGdQ2HNzkqswMs5Uaw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@inquirer/search/-/search-3.0.15.tgz}
    id: registry.npmmirror.com/@inquirer/search/3.0.15
    name: '@inquirer/search'
    version: 3.0.15
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true
    dependencies:
      '@inquirer/core': registry.npmmirror.com/@inquirer/core@10.1.13(@types/node@22.10.7)
      '@inquirer/figures': registry.npmmirror.com/@inquirer/figures@1.0.12
      '@inquirer/type': registry.npmmirror.com/@inquirer/type@3.0.7(@types/node@22.10.7)
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      yoctocolors-cjs: registry.npmmirror.com/yoctocolors-cjs@2.1.2
    dev: true

  registry.npmmirror.com/@inquirer/select@4.2.3(@types/node@22.10.7):
    resolution: {integrity: sha512-OAGhXU0Cvh0PhLz9xTF/kx6g6x+sP+PcyTiLvCrewI99P3BBeexD+VbuwkNDvqGkk3y2h5ZiWLeRP7BFlhkUDg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@inquirer/select/-/select-4.2.3.tgz}
    id: registry.npmmirror.com/@inquirer/select/4.2.3
    name: '@inquirer/select'
    version: 4.2.3
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true
    dependencies:
      '@inquirer/core': registry.npmmirror.com/@inquirer/core@10.1.13(@types/node@22.10.7)
      '@inquirer/figures': registry.npmmirror.com/@inquirer/figures@1.0.12
      '@inquirer/type': registry.npmmirror.com/@inquirer/type@3.0.7(@types/node@22.10.7)
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      ansi-escapes: registry.npmmirror.com/ansi-escapes@4.3.2
      yoctocolors-cjs: registry.npmmirror.com/yoctocolors-cjs@2.1.2
    dev: true

  registry.npmmirror.com/@inquirer/type@3.0.7(@types/node@22.10.7):
    resolution: {integrity: sha512-PfunHQcjwnju84L+ycmcMKB/pTPIngjUJvfnRhKY6FKPuYXlM4aQCb/nIdTFR6BEhMjFvngzvng/vBAJMZpLSA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@inquirer/type/-/type-3.0.7.tgz}
    id: registry.npmmirror.com/@inquirer/type/3.0.7
    name: '@inquirer/type'
    version: 3.0.7
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true
    dependencies:
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
    dev: true

  registry.npmmirror.com/@isaacs/balanced-match@4.0.1:
    resolution: {integrity: sha512-yzMTt9lEb8Gv7zRioUilSglI0c0smZ9k5D65677DLWLtWJaXIS3CqcGyUFByYKlnUj6TkjLVs54fBl6+TiGQDQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@isaacs/balanced-match/-/balanced-match-4.0.1.tgz}
    name: '@isaacs/balanced-match'
    version: 4.0.1
    engines: {node: 20 || >=22}
    dev: true

  registry.npmmirror.com/@isaacs/brace-expansion@5.0.0:
    resolution: {integrity: sha512-ZT55BDLV0yv0RBm2czMiZ+SqCGO7AvmOM3G/w2xhVPH+te0aKgFjmBvGlL1dH+ql2tgGO3MVrbb3jCKyvpgnxA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@isaacs/brace-expansion/-/brace-expansion-5.0.0.tgz}
    name: '@isaacs/brace-expansion'
    version: 5.0.0
    engines: {node: 20 || >=22}
    dependencies:
      '@isaacs/balanced-match': registry.npmmirror.com/@isaacs/balanced-match@4.0.1
    dev: true

  registry.npmmirror.com/@isaacs/cliui@8.0.2:
    resolution: {integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@isaacs/cliui/-/cliui-8.0.2.tgz}
    name: '@isaacs/cliui'
    version: 8.0.2
    engines: {node: '>=12'}
    dependencies:
      string-width: registry.npmmirror.com/string-width@5.1.2
      string-width-cjs: registry.npmmirror.com/string-width@4.2.3
      strip-ansi: registry.npmmirror.com/strip-ansi@7.1.0
      strip-ansi-cjs: registry.npmmirror.com/strip-ansi@6.0.1
      wrap-ansi: registry.npmmirror.com/wrap-ansi@8.1.0
      wrap-ansi-cjs: registry.npmmirror.com/wrap-ansi@7.0.0

  registry.npmmirror.com/@istanbuljs/load-nyc-config@1.1.0:
    resolution: {integrity: sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz}
    name: '@istanbuljs/load-nyc-config'
    version: 1.1.0
    engines: {node: '>=8'}
    dependencies:
      camelcase: registry.npmmirror.com/camelcase@5.3.1
      find-up: registry.npmmirror.com/find-up@4.1.0
      get-package-type: registry.npmmirror.com/get-package-type@0.1.0
      js-yaml: registry.npmmirror.com/js-yaml@3.14.1
      resolve-from: registry.npmmirror.com/resolve-from@5.0.0
    dev: true

  registry.npmmirror.com/@istanbuljs/schema@0.1.3:
    resolution: {integrity: sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@istanbuljs/schema/-/schema-0.1.3.tgz}
    name: '@istanbuljs/schema'
    version: 0.1.3
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/@jest/console@29.7.0:
    resolution: {integrity: sha512-5Ni4CU7XHQi32IJ398EEP4RrB8eV09sXP2ROqD4bksHrnTree52PsxvX8tpL8LvTZ3pFzXyPbNQReSN41CAhOg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jest/console/-/console-29.7.0.tgz}
    name: '@jest/console'
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      chalk: registry.npmmirror.com/chalk@4.1.2
      jest-message-util: registry.npmmirror.com/jest-message-util@29.7.0
      jest-util: registry.npmmirror.com/jest-util@29.7.0
      slash: registry.npmmirror.com/slash@3.0.0
    dev: true

  registry.npmmirror.com/@jest/core@29.7.0(ts-node@10.9.2):
    resolution: {integrity: sha512-n7aeXWKMnGtDA48y8TLWJPJmLmmZ642Ceo78cYWEpiD7FzDgmNDV/GCVRorPABdXLJZ/9wzzgZAlHjXjxDHGsg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jest/core/-/core-29.7.0.tgz}
    id: registry.npmmirror.com/@jest/core/29.7.0
    name: '@jest/core'
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true
    dependencies:
      '@jest/console': registry.npmmirror.com/@jest/console@29.7.0
      '@jest/reporters': registry.npmmirror.com/@jest/reporters@29.7.0
      '@jest/test-result': registry.npmmirror.com/@jest/test-result@29.7.0
      '@jest/transform': registry.npmmirror.com/@jest/transform@29.7.0
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      ansi-escapes: registry.npmmirror.com/ansi-escapes@4.3.2
      chalk: registry.npmmirror.com/chalk@4.1.2
      ci-info: registry.npmmirror.com/ci-info@3.9.0
      exit: registry.npmmirror.com/exit@0.1.2
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
      jest-changed-files: registry.npmmirror.com/jest-changed-files@29.7.0
      jest-config: registry.npmmirror.com/jest-config@29.7.0(@types/node@22.10.7)(ts-node@10.9.2)
      jest-haste-map: registry.npmmirror.com/jest-haste-map@29.7.0
      jest-message-util: registry.npmmirror.com/jest-message-util@29.7.0
      jest-regex-util: registry.npmmirror.com/jest-regex-util@29.6.3
      jest-resolve: registry.npmmirror.com/jest-resolve@29.7.0
      jest-resolve-dependencies: registry.npmmirror.com/jest-resolve-dependencies@29.7.0
      jest-runner: registry.npmmirror.com/jest-runner@29.7.0
      jest-runtime: registry.npmmirror.com/jest-runtime@29.7.0
      jest-snapshot: registry.npmmirror.com/jest-snapshot@29.7.0
      jest-util: registry.npmmirror.com/jest-util@29.7.0
      jest-validate: registry.npmmirror.com/jest-validate@29.7.0
      jest-watcher: registry.npmmirror.com/jest-watcher@29.7.0
      micromatch: registry.npmmirror.com/micromatch@4.0.8
      pretty-format: registry.npmmirror.com/pretty-format@29.7.0
      slash: registry.npmmirror.com/slash@3.0.0
      strip-ansi: registry.npmmirror.com/strip-ansi@6.0.1
    transitivePeerDependencies:
      - babel-plugin-macros
      - supports-color
      - ts-node
    dev: true

  registry.npmmirror.com/@jest/environment@29.7.0:
    resolution: {integrity: sha512-aQIfHDq33ExsN4jP1NWGXhxgQ/wixs60gDiKO+XVMd8Mn0NWPWgc34ZQDTb2jKaUWQ7MuwoitXAsN2XVXNMpAw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jest/environment/-/environment-29.7.0.tgz}
    name: '@jest/environment'
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/fake-timers': registry.npmmirror.com/@jest/fake-timers@29.7.0
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      jest-mock: registry.npmmirror.com/jest-mock@29.7.0
    dev: true

  registry.npmmirror.com/@jest/expect-utils@29.7.0:
    resolution: {integrity: sha512-GlsNBWiFQFCVi9QVSx7f5AgMeLxe9YCCs5PuP2O2LdjDAA8Jh9eX7lA1Jq/xdXw3Wb3hyvlFNfZIfcRetSzYcA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jest/expect-utils/-/expect-utils-29.7.0.tgz}
    name: '@jest/expect-utils'
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      jest-get-type: registry.npmmirror.com/jest-get-type@29.6.3
    dev: true

  registry.npmmirror.com/@jest/expect@29.7.0:
    resolution: {integrity: sha512-8uMeAMycttpva3P1lBHB8VciS9V0XAr3GymPpipdyQXbBcuhkLQOSe8E/p92RyAdToS6ZD1tFkX+CkhoECE0dQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jest/expect/-/expect-29.7.0.tgz}
    name: '@jest/expect'
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      expect: registry.npmmirror.com/expect@29.7.0
      jest-snapshot: registry.npmmirror.com/jest-snapshot@29.7.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@jest/fake-timers@29.7.0:
    resolution: {integrity: sha512-q4DH1Ha4TTFPdxLsqDXK1d3+ioSL7yL5oCMJZgDYm6i+6CygW5E5xVr/D1HdsGxjt1ZWSfUAs9OxSB/BNelWrQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jest/fake-timers/-/fake-timers-29.7.0.tgz}
    name: '@jest/fake-timers'
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      '@sinonjs/fake-timers': registry.npmmirror.com/@sinonjs/fake-timers@10.3.0
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      jest-message-util: registry.npmmirror.com/jest-message-util@29.7.0
      jest-mock: registry.npmmirror.com/jest-mock@29.7.0
      jest-util: registry.npmmirror.com/jest-util@29.7.0
    dev: true

  registry.npmmirror.com/@jest/globals@29.7.0:
    resolution: {integrity: sha512-mpiz3dutLbkW2MNFubUGUEVLkTGiqW6yLVTA+JbP6fI6J5iL9Y0Nlg8k95pcF8ctKwCS7WVxteBs29hhfAotzQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jest/globals/-/globals-29.7.0.tgz}
    name: '@jest/globals'
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/environment': registry.npmmirror.com/@jest/environment@29.7.0
      '@jest/expect': registry.npmmirror.com/@jest/expect@29.7.0
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      jest-mock: registry.npmmirror.com/jest-mock@29.7.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@jest/reporters@29.7.0:
    resolution: {integrity: sha512-DApq0KJbJOEzAFYjHADNNxAE3KbhxQB1y5Kplb5Waqw6zVbuWatSnMjE5gs8FUgEPmNsnZA3NCWl9NG0ia04Pg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jest/reporters/-/reporters-29.7.0.tgz}
    name: '@jest/reporters'
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true
    dependencies:
      '@bcoe/v8-coverage': registry.npmmirror.com/@bcoe/v8-coverage@0.2.3
      '@jest/console': registry.npmmirror.com/@jest/console@29.7.0
      '@jest/test-result': registry.npmmirror.com/@jest/test-result@29.7.0
      '@jest/transform': registry.npmmirror.com/@jest/transform@29.7.0
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      '@jridgewell/trace-mapping': registry.npmmirror.com/@jridgewell/trace-mapping@0.3.25
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      chalk: registry.npmmirror.com/chalk@4.1.2
      collect-v8-coverage: registry.npmmirror.com/collect-v8-coverage@1.0.2
      exit: registry.npmmirror.com/exit@0.1.2
      glob: registry.npmmirror.com/glob@7.2.3
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
      istanbul-lib-coverage: registry.npmmirror.com/istanbul-lib-coverage@3.2.2
      istanbul-lib-instrument: registry.npmmirror.com/istanbul-lib-instrument@6.0.3
      istanbul-lib-report: registry.npmmirror.com/istanbul-lib-report@3.0.1
      istanbul-lib-source-maps: registry.npmmirror.com/istanbul-lib-source-maps@4.0.1
      istanbul-reports: registry.npmmirror.com/istanbul-reports@3.1.7
      jest-message-util: registry.npmmirror.com/jest-message-util@29.7.0
      jest-util: registry.npmmirror.com/jest-util@29.7.0
      jest-worker: registry.npmmirror.com/jest-worker@29.7.0
      slash: registry.npmmirror.com/slash@3.0.0
      string-length: registry.npmmirror.com/string-length@4.0.2
      strip-ansi: registry.npmmirror.com/strip-ansi@6.0.1
      v8-to-istanbul: registry.npmmirror.com/v8-to-istanbul@9.3.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@jest/schemas@29.6.3:
    resolution: {integrity: sha512-mo5j5X+jIZmJQveBKeS/clAueipV7KgiX1vMgCxam1RNYiqE1w62n0/tJJnHtjW8ZHcQco5gY85jA3mi0L+nSA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jest/schemas/-/schemas-29.6.3.tgz}
    name: '@jest/schemas'
    version: 29.6.3
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@sinclair/typebox': registry.npmmirror.com/@sinclair/typebox@0.27.8
    dev: true

  registry.npmmirror.com/@jest/source-map@29.6.3:
    resolution: {integrity: sha512-MHjT95QuipcPrpLM+8JMSzFx6eHp5Bm+4XeFDJlwsvVBjmKNiIAvasGK2fxz2WbGRlnvqehFbh07MMa7n3YJnw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jest/source-map/-/source-map-29.6.3.tgz}
    name: '@jest/source-map'
    version: 29.6.3
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jridgewell/trace-mapping': registry.npmmirror.com/@jridgewell/trace-mapping@0.3.25
      callsites: registry.npmmirror.com/callsites@3.1.0
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
    dev: true

  registry.npmmirror.com/@jest/test-result@29.7.0:
    resolution: {integrity: sha512-Fdx+tv6x1zlkJPcWXmMDAG2HBnaR9XPSd5aDWQVsfrZmLVT3lU1cwyxLgRmXR9yrq4NBoEm9BMsfgFzTQAbJYA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jest/test-result/-/test-result-29.7.0.tgz}
    name: '@jest/test-result'
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/console': registry.npmmirror.com/@jest/console@29.7.0
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      '@types/istanbul-lib-coverage': registry.npmmirror.com/@types/istanbul-lib-coverage@2.0.6
      collect-v8-coverage: registry.npmmirror.com/collect-v8-coverage@1.0.2
    dev: true

  registry.npmmirror.com/@jest/test-sequencer@29.7.0:
    resolution: {integrity: sha512-GQwJ5WZVrKnOJuiYiAF52UNUJXgTZx1NHjFSEB0qEMmSZKAkdMoIzw/Cj6x6NF4AvV23AUqDpFzQkN/eYCYTxw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jest/test-sequencer/-/test-sequencer-29.7.0.tgz}
    name: '@jest/test-sequencer'
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/test-result': registry.npmmirror.com/@jest/test-result@29.7.0
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
      jest-haste-map: registry.npmmirror.com/jest-haste-map@29.7.0
      slash: registry.npmmirror.com/slash@3.0.0
    dev: true

  registry.npmmirror.com/@jest/transform@29.7.0:
    resolution: {integrity: sha512-ok/BTPFzFKVMwO5eOHRrvnBVHdRy9IrsrW1GpMaQ9MCnilNLXQKmAX8s1YXDFaai9xJpac2ySzV0YeRRECr2Vw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jest/transform/-/transform-29.7.0.tgz}
    name: '@jest/transform'
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      '@jridgewell/trace-mapping': registry.npmmirror.com/@jridgewell/trace-mapping@0.3.25
      babel-plugin-istanbul: registry.npmmirror.com/babel-plugin-istanbul@6.1.1
      chalk: registry.npmmirror.com/chalk@4.1.2
      convert-source-map: registry.npmmirror.com/convert-source-map@2.0.0
      fast-json-stable-stringify: registry.npmmirror.com/fast-json-stable-stringify@2.1.0
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
      jest-haste-map: registry.npmmirror.com/jest-haste-map@29.7.0
      jest-regex-util: registry.npmmirror.com/jest-regex-util@29.6.3
      jest-util: registry.npmmirror.com/jest-util@29.7.0
      micromatch: registry.npmmirror.com/micromatch@4.0.8
      pirates: registry.npmmirror.com/pirates@4.0.7
      slash: registry.npmmirror.com/slash@3.0.0
      write-file-atomic: registry.npmmirror.com/write-file-atomic@4.0.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@jest/types@29.6.3:
    resolution: {integrity: sha512-u3UPsIilWKOM3F9CXtrG8LEJmNxwoCQC/XVj4IKYXvvpx7QIi/Kg1LI5uDmDpKlac62NUtX7eLjRh+jVZcLOzw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jest/types/-/types-29.6.3.tgz}
    name: '@jest/types'
    version: 29.6.3
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/schemas': registry.npmmirror.com/@jest/schemas@29.6.3
      '@types/istanbul-lib-coverage': registry.npmmirror.com/@types/istanbul-lib-coverage@2.0.6
      '@types/istanbul-reports': registry.npmmirror.com/@types/istanbul-reports@3.0.4
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      '@types/yargs': registry.npmmirror.com/@types/yargs@17.0.33
      chalk: registry.npmmirror.com/chalk@4.1.2
    dev: true

  registry.npmmirror.com/@jridgewell/gen-mapping@0.3.8:
    resolution: {integrity: sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz}
    name: '@jridgewell/gen-mapping'
    version: 0.3.8
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': registry.npmmirror.com/@jridgewell/set-array@1.2.1
      '@jridgewell/sourcemap-codec': registry.npmmirror.com/@jridgewell/sourcemap-codec@1.5.0
      '@jridgewell/trace-mapping': registry.npmmirror.com/@jridgewell/trace-mapping@0.3.25
    dev: true

  registry.npmmirror.com/@jridgewell/resolve-uri@3.1.2:
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz}
    name: '@jridgewell/resolve-uri'
    version: 3.1.2
    engines: {node: '>=6.0.0'}

  registry.npmmirror.com/@jridgewell/set-array@1.2.1:
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jridgewell/set-array/-/set-array-1.2.1.tgz}
    name: '@jridgewell/set-array'
    version: 1.2.1
    engines: {node: '>=6.0.0'}
    dev: true

  registry.npmmirror.com/@jridgewell/source-map@0.3.6:
    resolution: {integrity: sha512-1ZJTZebgqllO79ue2bm3rIGud/bOe0pP5BjSRCRxxYkEZS8STV7zN84UBbiYu7jy+eCKSnVIUgoWWE/tt+shMQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jridgewell/source-map/-/source-map-0.3.6.tgz}
    name: '@jridgewell/source-map'
    version: 0.3.6
    dependencies:
      '@jridgewell/gen-mapping': registry.npmmirror.com/@jridgewell/gen-mapping@0.3.8
      '@jridgewell/trace-mapping': registry.npmmirror.com/@jridgewell/trace-mapping@0.3.25
    dev: true

  registry.npmmirror.com/@jridgewell/sourcemap-codec@1.5.0:
    resolution: {integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz}
    name: '@jridgewell/sourcemap-codec'
    version: 1.5.0

  registry.npmmirror.com/@jridgewell/trace-mapping@0.3.25:
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz}
    name: '@jridgewell/trace-mapping'
    version: 0.3.25
    dependencies:
      '@jridgewell/resolve-uri': registry.npmmirror.com/@jridgewell/resolve-uri@3.1.2
      '@jridgewell/sourcemap-codec': registry.npmmirror.com/@jridgewell/sourcemap-codec@1.5.0
    dev: true

  registry.npmmirror.com/@jridgewell/trace-mapping@0.3.9:
    resolution: {integrity: sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.9.tgz}
    name: '@jridgewell/trace-mapping'
    version: 0.3.9
    dependencies:
      '@jridgewell/resolve-uri': registry.npmmirror.com/@jridgewell/resolve-uri@3.1.2
      '@jridgewell/sourcemap-codec': registry.npmmirror.com/@jridgewell/sourcemap-codec@1.5.0

  registry.npmmirror.com/@lukeed/csprng@1.1.0:
    resolution: {integrity: sha512-Z7C/xXCiGWsg0KuKsHTKJxbWhpI3Vs5GwLfOean7MGyVFGqdRgBbAjOCh6u4bbjPc/8MJ2pZmK/0DLdCbivLDA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@lukeed/csprng/-/csprng-1.1.0.tgz}
    name: '@lukeed/csprng'
    version: 1.1.0
    engines: {node: '>=8'}

  registry.npmmirror.com/@microsoft/tsdoc@0.15.1:
    resolution: {integrity: sha512-4aErSrCR/On/e5G2hDP0wjooqDdauzEbIq8hIkIe5pXV0rtWJZvdCEKL0ykZxex+IxIwBp0eGeV48hQN07dXtw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@microsoft/tsdoc/-/tsdoc-0.15.1.tgz}
    name: '@microsoft/tsdoc'
    version: 0.15.1
    dev: false

  registry.npmmirror.com/@napi-rs/nice-android-arm-eabi@1.0.1:
    resolution: {integrity: sha512-5qpvOu5IGwDo7MEKVqqyAxF90I6aLj4n07OzpARdgDRfz8UbBztTByBp0RC59r3J1Ij8uzYi6jI7r5Lws7nn6w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@napi-rs/nice-android-arm-eabi/-/nice-android-arm-eabi-1.0.1.tgz}
    name: '@napi-rs/nice-android-arm-eabi'
    version: 1.0.1
    engines: {node: '>= 10'}
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@napi-rs/nice-android-arm64@1.0.1:
    resolution: {integrity: sha512-GqvXL0P8fZ+mQqG1g0o4AO9hJjQaeYG84FRfZaYjyJtZZZcMjXW5TwkL8Y8UApheJgyE13TQ4YNUssQaTgTyvA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@napi-rs/nice-android-arm64/-/nice-android-arm64-1.0.1.tgz}
    name: '@napi-rs/nice-android-arm64'
    version: 1.0.1
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@napi-rs/nice-darwin-arm64@1.0.1:
    resolution: {integrity: sha512-91k3HEqUl2fsrz/sKkuEkscj6EAj3/eZNCLqzD2AA0TtVbkQi8nqxZCZDMkfklULmxLkMxuUdKe7RvG/T6s2AA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@napi-rs/nice-darwin-arm64/-/nice-darwin-arm64-1.0.1.tgz}
    name: '@napi-rs/nice-darwin-arm64'
    version: 1.0.1
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@napi-rs/nice-darwin-x64@1.0.1:
    resolution: {integrity: sha512-jXnMleYSIR/+TAN/p5u+NkCA7yidgswx5ftqzXdD5wgy/hNR92oerTXHc0jrlBisbd7DpzoaGY4cFD7Sm5GlgQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@napi-rs/nice-darwin-x64/-/nice-darwin-x64-1.0.1.tgz}
    name: '@napi-rs/nice-darwin-x64'
    version: 1.0.1
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@napi-rs/nice-freebsd-x64@1.0.1:
    resolution: {integrity: sha512-j+iJ/ezONXRQsVIB/FJfwjeQXX7A2tf3gEXs4WUGFrJjpe/z2KB7sOv6zpkm08PofF36C9S7wTNuzHZ/Iiccfw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@napi-rs/nice-freebsd-x64/-/nice-freebsd-x64-1.0.1.tgz}
    name: '@napi-rs/nice-freebsd-x64'
    version: 1.0.1
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@napi-rs/nice-linux-arm-gnueabihf@1.0.1:
    resolution: {integrity: sha512-G8RgJ8FYXYkkSGQwywAUh84m946UTn6l03/vmEXBYNJxQJcD+I3B3k5jmjFG/OPiU8DfvxutOP8bi+F89MCV7Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@napi-rs/nice-linux-arm-gnueabihf/-/nice-linux-arm-gnueabihf-1.0.1.tgz}
    name: '@napi-rs/nice-linux-arm-gnueabihf'
    version: 1.0.1
    engines: {node: '>= 10'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@napi-rs/nice-linux-arm64-gnu@1.0.1:
    resolution: {integrity: sha512-IMDak59/W5JSab1oZvmNbrms3mHqcreaCeClUjwlwDr0m3BoR09ZiN8cKFBzuSlXgRdZ4PNqCYNeGQv7YMTjuA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@napi-rs/nice-linux-arm64-gnu/-/nice-linux-arm64-gnu-1.0.1.tgz}
    name: '@napi-rs/nice-linux-arm64-gnu'
    version: 1.0.1
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@napi-rs/nice-linux-arm64-musl@1.0.1:
    resolution: {integrity: sha512-wG8fa2VKuWM4CfjOjjRX9YLIbysSVV1S3Kgm2Fnc67ap/soHBeYZa6AGMeR5BJAylYRjnoVOzV19Cmkco3QEPw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@napi-rs/nice-linux-arm64-musl/-/nice-linux-arm64-musl-1.0.1.tgz}
    name: '@napi-rs/nice-linux-arm64-musl'
    version: 1.0.1
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@napi-rs/nice-linux-ppc64-gnu@1.0.1:
    resolution: {integrity: sha512-lxQ9WrBf0IlNTCA9oS2jg/iAjQyTI6JHzABV664LLrLA/SIdD+I1i3Mjf7TsnoUbgopBcCuDztVLfJ0q9ubf6Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@napi-rs/nice-linux-ppc64-gnu/-/nice-linux-ppc64-gnu-1.0.1.tgz}
    name: '@napi-rs/nice-linux-ppc64-gnu'
    version: 1.0.1
    engines: {node: '>= 10'}
    cpu: [ppc64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@napi-rs/nice-linux-riscv64-gnu@1.0.1:
    resolution: {integrity: sha512-3xs69dO8WSWBb13KBVex+yvxmUeEsdWexxibqskzoKaWx9AIqkMbWmE2npkazJoopPKX2ULKd8Fm9veEn0g4Ig==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@napi-rs/nice-linux-riscv64-gnu/-/nice-linux-riscv64-gnu-1.0.1.tgz}
    name: '@napi-rs/nice-linux-riscv64-gnu'
    version: 1.0.1
    engines: {node: '>= 10'}
    cpu: [riscv64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@napi-rs/nice-linux-s390x-gnu@1.0.1:
    resolution: {integrity: sha512-lMFI3i9rlW7hgToyAzTaEybQYGbQHDrpRkg+1gJWEpH0PLAQoZ8jiY0IzakLfNWnVda1eTYYlxxFYzW8Rqczkg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@napi-rs/nice-linux-s390x-gnu/-/nice-linux-s390x-gnu-1.0.1.tgz}
    name: '@napi-rs/nice-linux-s390x-gnu'
    version: 1.0.1
    engines: {node: '>= 10'}
    cpu: [s390x]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@napi-rs/nice-linux-x64-gnu@1.0.1:
    resolution: {integrity: sha512-XQAJs7DRN2GpLN6Fb+ZdGFeYZDdGl2Fn3TmFlqEL5JorgWKrQGRUrpGKbgZ25UeZPILuTKJ+OowG2avN8mThBA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@napi-rs/nice-linux-x64-gnu/-/nice-linux-x64-gnu-1.0.1.tgz}
    name: '@napi-rs/nice-linux-x64-gnu'
    version: 1.0.1
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@napi-rs/nice-linux-x64-musl@1.0.1:
    resolution: {integrity: sha512-/rodHpRSgiI9o1faq9SZOp/o2QkKQg7T+DK0R5AkbnI/YxvAIEHf2cngjYzLMQSQgUhxym+LFr+UGZx4vK4QdQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@napi-rs/nice-linux-x64-musl/-/nice-linux-x64-musl-1.0.1.tgz}
    name: '@napi-rs/nice-linux-x64-musl'
    version: 1.0.1
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@napi-rs/nice-win32-arm64-msvc@1.0.1:
    resolution: {integrity: sha512-rEcz9vZymaCB3OqEXoHnp9YViLct8ugF+6uO5McifTedjq4QMQs3DHz35xBEGhH3gJWEsXMUbzazkz5KNM5YUg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@napi-rs/nice-win32-arm64-msvc/-/nice-win32-arm64-msvc-1.0.1.tgz}
    name: '@napi-rs/nice-win32-arm64-msvc'
    version: 1.0.1
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@napi-rs/nice-win32-ia32-msvc@1.0.1:
    resolution: {integrity: sha512-t7eBAyPUrWL8su3gDxw9xxxqNwZzAqKo0Szv3IjVQd1GpXXVkb6vBBQUuxfIYaXMzZLwlxRQ7uzM2vdUE9ULGw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@napi-rs/nice-win32-ia32-msvc/-/nice-win32-ia32-msvc-1.0.1.tgz}
    name: '@napi-rs/nice-win32-ia32-msvc'
    version: 1.0.1
    engines: {node: '>= 10'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@napi-rs/nice-win32-x64-msvc@1.0.1:
    resolution: {integrity: sha512-JlF+uDcatt3St2ntBG8H02F1mM45i5SF9W+bIKiReVE6wiy3o16oBP/yxt+RZ+N6LbCImJXJ6bXNO2kn9AXicg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@napi-rs/nice-win32-x64-msvc/-/nice-win32-x64-msvc-1.0.1.tgz}
    name: '@napi-rs/nice-win32-x64-msvc'
    version: 1.0.1
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@napi-rs/nice@1.0.1:
    resolution: {integrity: sha512-zM0mVWSXE0a0h9aKACLwKmD6nHcRiKrPpCfvaKqG1CqDEyjEawId0ocXxVzPMCAm6kkWr2P025msfxXEnt8UGQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@napi-rs/nice/-/nice-1.0.1.tgz}
    name: '@napi-rs/nice'
    version: 1.0.1
    engines: {node: '>= 10'}
    requiresBuild: true
    optionalDependencies:
      '@napi-rs/nice-android-arm-eabi': registry.npmmirror.com/@napi-rs/nice-android-arm-eabi@1.0.1
      '@napi-rs/nice-android-arm64': registry.npmmirror.com/@napi-rs/nice-android-arm64@1.0.1
      '@napi-rs/nice-darwin-arm64': registry.npmmirror.com/@napi-rs/nice-darwin-arm64@1.0.1
      '@napi-rs/nice-darwin-x64': registry.npmmirror.com/@napi-rs/nice-darwin-x64@1.0.1
      '@napi-rs/nice-freebsd-x64': registry.npmmirror.com/@napi-rs/nice-freebsd-x64@1.0.1
      '@napi-rs/nice-linux-arm-gnueabihf': registry.npmmirror.com/@napi-rs/nice-linux-arm-gnueabihf@1.0.1
      '@napi-rs/nice-linux-arm64-gnu': registry.npmmirror.com/@napi-rs/nice-linux-arm64-gnu@1.0.1
      '@napi-rs/nice-linux-arm64-musl': registry.npmmirror.com/@napi-rs/nice-linux-arm64-musl@1.0.1
      '@napi-rs/nice-linux-ppc64-gnu': registry.npmmirror.com/@napi-rs/nice-linux-ppc64-gnu@1.0.1
      '@napi-rs/nice-linux-riscv64-gnu': registry.npmmirror.com/@napi-rs/nice-linux-riscv64-gnu@1.0.1
      '@napi-rs/nice-linux-s390x-gnu': registry.npmmirror.com/@napi-rs/nice-linux-s390x-gnu@1.0.1
      '@napi-rs/nice-linux-x64-gnu': registry.npmmirror.com/@napi-rs/nice-linux-x64-gnu@1.0.1
      '@napi-rs/nice-linux-x64-musl': registry.npmmirror.com/@napi-rs/nice-linux-x64-musl@1.0.1
      '@napi-rs/nice-win32-arm64-msvc': registry.npmmirror.com/@napi-rs/nice-win32-arm64-msvc@1.0.1
      '@napi-rs/nice-win32-ia32-msvc': registry.npmmirror.com/@napi-rs/nice-win32-ia32-msvc@1.0.1
      '@napi-rs/nice-win32-x64-msvc': registry.npmmirror.com/@napi-rs/nice-win32-x64-msvc@1.0.1
    dev: true
    optional: true

  registry.npmmirror.com/@nestjs/cli@11.0.0(@swc/cli@0.6.0)(@swc/core@1.10.7)(@types/node@22.10.7):
    resolution: {integrity: sha512-53Hparhbz4nNQNttf6EFfYNeFxSLdshqnvaiWy3MBnZxgAuqtAZP3xsiNHPId5dYtzGCjdK60FN4Vcspx3Y+aw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@nestjs/cli/-/cli-11.0.0.tgz}
    id: registry.npmmirror.com/@nestjs/cli/11.0.0
    name: '@nestjs/cli'
    version: 11.0.0
    engines: {node: '>= 20.11'}
    hasBin: true
    peerDependencies:
      '@swc/cli': ^0.1.62 || ^0.3.0 || ^0.4.0 || ^0.5.0 || ^0.6.0
      '@swc/core': ^1.3.62
    peerDependenciesMeta:
      '@swc/cli':
        optional: true
      '@swc/core':
        optional: true
    dependencies:
      '@angular-devkit/core': registry.npmmirror.com/@angular-devkit/core@19.1.2(chokidar@4.0.3)
      '@angular-devkit/schematics': registry.npmmirror.com/@angular-devkit/schematics@19.1.2(chokidar@4.0.3)
      '@angular-devkit/schematics-cli': registry.npmmirror.com/@angular-devkit/schematics-cli@19.1.2(@types/node@22.10.7)(chokidar@4.0.3)
      '@inquirer/prompts': registry.npmmirror.com/@inquirer/prompts@7.2.3(@types/node@22.10.7)
      '@nestjs/schematics': registry.npmmirror.com/@nestjs/schematics@11.0.0(chokidar@4.0.3)(typescript@5.7.3)
      '@swc/cli': registry.npmmirror.com/@swc/cli@0.6.0(@swc/core@1.10.7)
      '@swc/core': registry.npmmirror.com/@swc/core@1.10.7
      ansis: registry.npmmirror.com/ansis@3.9.0
      chokidar: registry.npmmirror.com/chokidar@4.0.3
      cli-table3: registry.npmmirror.com/cli-table3@0.6.5
      commander: registry.npmmirror.com/commander@4.1.1
      fork-ts-checker-webpack-plugin: registry.npmmirror.com/fork-ts-checker-webpack-plugin@9.0.2(typescript@5.7.3)(webpack@5.97.1)
      glob: registry.npmmirror.com/glob@11.0.1
      node-emoji: registry.npmmirror.com/node-emoji@1.11.0
      ora: registry.npmmirror.com/ora@5.4.1
      tree-kill: registry.npmmirror.com/tree-kill@1.2.2
      tsconfig-paths: registry.npmmirror.com/tsconfig-paths@4.2.0
      tsconfig-paths-webpack-plugin: registry.npmmirror.com/tsconfig-paths-webpack-plugin@4.2.0
      typescript: registry.npmmirror.com/typescript@5.7.3
      webpack: registry.npmmirror.com/webpack@5.97.1(@swc/core@1.10.7)
      webpack-node-externals: registry.npmmirror.com/webpack-node-externals@3.0.0
    transitivePeerDependencies:
      - '@types/node'
      - esbuild
      - uglify-js
      - webpack-cli
    dev: true

  registry.npmmirror.com/@nestjs/common@11.0.1(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.1):
    resolution: {integrity: sha512-GbPKvDz/sYjhR86ebZu+0F5jAvYDZDXY5WcHek9gkEkMI9eIo3xeH/F/tNIPhdyeWp3eG+kNWqR9VPtC18G+TA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@nestjs/common/-/common-11.0.1.tgz}
    id: registry.npmmirror.com/@nestjs/common/11.0.1
    name: '@nestjs/common'
    version: 11.0.1
    peerDependencies:
      class-transformer: '*'
      class-validator: '*'
      reflect-metadata: ^0.1.12 || ^0.2.0
      rxjs: ^7.1.0
    peerDependenciesMeta:
      class-transformer:
        optional: true
      class-validator:
        optional: true
    dependencies:
      class-transformer: registry.npmmirror.com/class-transformer@0.5.1
      class-validator: registry.npmmirror.com/class-validator@0.14.2
      iterare: registry.npmmirror.com/iterare@1.2.1
      reflect-metadata: registry.npmmirror.com/reflect-metadata@0.2.2
      rxjs: registry.npmmirror.com/rxjs@7.8.1
      tslib: registry.npmmirror.com/tslib@2.8.1
      uid: registry.npmmirror.com/uid@2.0.2

  registry.npmmirror.com/@nestjs/config@4.0.2(@nestjs/common@11.0.1)(rxjs@7.8.1):
    resolution: {integrity: sha512-McMW6EXtpc8+CwTUwFdg6h7dYcBUpH5iUILCclAsa+MbCEvC9ZKu4dCHRlJqALuhjLw97pbQu62l4+wRwGeZqA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@nestjs/config/-/config-4.0.2.tgz}
    id: registry.npmmirror.com/@nestjs/config/4.0.2
    name: '@nestjs/config'
    version: 4.0.2
    peerDependencies:
      '@nestjs/common': ^10.0.0 || ^11.0.0
      rxjs: ^7.1.0
    dependencies:
      '@nestjs/common': registry.npmmirror.com/@nestjs/common@11.0.1(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.1)
      dotenv: registry.npmmirror.com/dotenv@16.4.7
      dotenv-expand: registry.npmmirror.com/dotenv-expand@12.0.1
      lodash: registry.npmmirror.com/lodash@4.17.21
      rxjs: registry.npmmirror.com/rxjs@7.8.1
    dev: false

  registry.npmmirror.com/@nestjs/core@11.0.1(@nestjs/common@11.0.1)(@nestjs/platform-express@11.0.1)(reflect-metadata@0.2.2)(rxjs@7.8.1):
    resolution: {integrity: sha512-Yn7X2aInjmX7yxpH8TjJmgC0JPvs+tcreETkquSRmKbuK5J28dZDi8loiaw3eRTLLvzzUovv5mlqFxmVhDESOw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@nestjs/core/-/core-11.0.1.tgz}
    id: registry.npmmirror.com/@nestjs/core/11.0.1
    name: '@nestjs/core'
    version: 11.0.1
    engines: {node: '>= 20'}
    requiresBuild: true
    peerDependencies:
      '@nestjs/common': ^11.0.0
      '@nestjs/microservices': ^11.0.0
      '@nestjs/platform-express': ^11.0.0
      '@nestjs/websockets': ^11.0.0
      reflect-metadata: ^0.1.12 || ^0.2.0
      rxjs: ^7.1.0
    peerDependenciesMeta:
      '@nestjs/microservices':
        optional: true
      '@nestjs/platform-express':
        optional: true
      '@nestjs/websockets':
        optional: true
    dependencies:
      '@nestjs/common': registry.npmmirror.com/@nestjs/common@11.0.1(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.1)
      '@nestjs/platform-express': registry.npmmirror.com/@nestjs/platform-express@11.0.1(@nestjs/common@11.0.1)(@nestjs/core@11.0.1)
      '@nuxt/opencollective': registry.npmmirror.com/@nuxt/opencollective@0.4.1
      fast-safe-stringify: registry.npmmirror.com/fast-safe-stringify@2.1.1
      iterare: registry.npmmirror.com/iterare@1.2.1
      path-to-regexp: registry.npmmirror.com/path-to-regexp@8.2.0
      reflect-metadata: registry.npmmirror.com/reflect-metadata@0.2.2
      rxjs: registry.npmmirror.com/rxjs@7.8.1
      tslib: registry.npmmirror.com/tslib@2.8.1
      uid: registry.npmmirror.com/uid@2.0.2

  registry.npmmirror.com/@nestjs/jwt@11.0.0(@nestjs/common@11.0.1):
    resolution: {integrity: sha512-v7YRsW3Xi8HNTsO+jeHSEEqelX37TVWgwt+BcxtkG/OfXJEOs6GZdbdza200d6KqId1pJQZ6UPj1F0M6E+mxaA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@nestjs/jwt/-/jwt-11.0.0.tgz}
    id: registry.npmmirror.com/@nestjs/jwt/11.0.0
    name: '@nestjs/jwt'
    version: 11.0.0
    peerDependencies:
      '@nestjs/common': ^8.0.0 || ^9.0.0 || ^10.0.0 || ^11.0.0
    dependencies:
      '@nestjs/common': registry.npmmirror.com/@nestjs/common@11.0.1(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.1)
      '@types/jsonwebtoken': registry.npmmirror.com/@types/jsonwebtoken@9.0.7
      jsonwebtoken: registry.npmmirror.com/jsonwebtoken@9.0.2
    dev: false

  registry.npmmirror.com/@nestjs/mapped-types@2.1.0(@nestjs/common@11.0.1)(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2):
    resolution: {integrity: sha512-W+n+rM69XsFdwORF11UqJahn4J3xi4g/ZEOlJNL6KoW5ygWSmBB2p0S2BZ4FQeS/NDH72e6xIcu35SfJnE8bXw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@nestjs/mapped-types/-/mapped-types-2.1.0.tgz}
    id: registry.npmmirror.com/@nestjs/mapped-types/2.1.0
    name: '@nestjs/mapped-types'
    version: 2.1.0
    peerDependencies:
      '@nestjs/common': ^10.0.0 || ^11.0.0
      class-transformer: ^0.4.0 || ^0.5.0
      class-validator: ^0.13.0 || ^0.14.0
      reflect-metadata: ^0.1.12 || ^0.2.0
    peerDependenciesMeta:
      class-transformer:
        optional: true
      class-validator:
        optional: true
    dependencies:
      '@nestjs/common': registry.npmmirror.com/@nestjs/common@11.0.1(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.1)
      class-transformer: registry.npmmirror.com/class-transformer@0.5.1
      class-validator: registry.npmmirror.com/class-validator@0.14.2
      reflect-metadata: registry.npmmirror.com/reflect-metadata@0.2.2
    dev: false

  registry.npmmirror.com/@nestjs/passport@11.0.5(@nestjs/common@11.0.1)(passport@0.7.0):
    resolution: {integrity: sha512-ulQX6mbjlws92PIM15Naes4F4p2JoxGnIJuUsdXQPT+Oo2sqQmENEZXM7eYuimocfHnKlcfZOuyzbA33LwUlOQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@nestjs/passport/-/passport-11.0.5.tgz}
    id: registry.npmmirror.com/@nestjs/passport/11.0.5
    name: '@nestjs/passport'
    version: 11.0.5
    peerDependencies:
      '@nestjs/common': ^10.0.0 || ^11.0.0
      passport: ^0.5.0 || ^0.6.0 || ^0.7.0
    dependencies:
      '@nestjs/common': registry.npmmirror.com/@nestjs/common@11.0.1(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.1)
      passport: registry.npmmirror.com/passport@0.7.0
    dev: false

  registry.npmmirror.com/@nestjs/platform-express@11.0.1(@nestjs/common@11.0.1)(@nestjs/core@11.0.1):
    resolution: {integrity: sha512-vkAcm4Lm/aAvvpYtRcRKuwzsliy4SqoSp0saHOIx6VdphIb1k7ziRkjDbLFDczDZmkiyX1pJ9kI5SHjoQzVDPw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@nestjs/platform-express/-/platform-express-11.0.1.tgz}
    id: registry.npmmirror.com/@nestjs/platform-express/11.0.1
    name: '@nestjs/platform-express'
    version: 11.0.1
    peerDependencies:
      '@nestjs/common': ^11.0.0
      '@nestjs/core': ^11.0.0
    dependencies:
      '@nestjs/common': registry.npmmirror.com/@nestjs/common@11.0.1(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.1)
      '@nestjs/core': registry.npmmirror.com/@nestjs/core@11.0.1(@nestjs/common@11.0.1)(@nestjs/platform-express@11.0.1)(reflect-metadata@0.2.2)(rxjs@7.8.1)
      body-parser: registry.npmmirror.com/body-parser@1.20.3
      cors: registry.npmmirror.com/cors@2.8.5
      express: registry.npmmirror.com/express@5.0.1
      multer: registry.npmmirror.com/multer@1.4.5-lts.1
      tslib: registry.npmmirror.com/tslib@2.8.1
    transitivePeerDependencies:
      - supports-color

  registry.npmmirror.com/@nestjs/schematics@11.0.0(chokidar@4.0.3)(typescript@5.7.3):
    resolution: {integrity: sha512-wts8lG0GfNWw3Wk9aaG5I/wcMIAdm7HjjeThQfUZhJxeIFT82Z3F5+0cYdHH4ii2pYQGiCSrR1VcuMwPiHoecg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@nestjs/schematics/-/schematics-11.0.0.tgz}
    id: registry.npmmirror.com/@nestjs/schematics/11.0.0
    name: '@nestjs/schematics'
    version: 11.0.0
    peerDependencies:
      typescript: '>=4.8.2'
    dependencies:
      '@angular-devkit/core': registry.npmmirror.com/@angular-devkit/core@19.0.1(chokidar@4.0.3)
      '@angular-devkit/schematics': registry.npmmirror.com/@angular-devkit/schematics@19.0.1(chokidar@4.0.3)
      comment-json: registry.npmmirror.com/comment-json@4.2.5
      jsonc-parser: registry.npmmirror.com/jsonc-parser@3.3.1
      pluralize: registry.npmmirror.com/pluralize@8.0.0
      typescript: registry.npmmirror.com/typescript@5.7.3
    transitivePeerDependencies:
      - chokidar
    dev: true

  registry.npmmirror.com/@nestjs/swagger@11.2.0(@nestjs/common@11.0.1)(@nestjs/core@11.0.1)(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2):
    resolution: {integrity: sha512-5wolt8GmpNcrQv34tIPUtPoV1EeFbCetm40Ij3+M0FNNnf2RJ3FyWfuQvI8SBlcJyfaounYVTKzKHreFXsUyOg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@nestjs/swagger/-/swagger-11.2.0.tgz}
    id: registry.npmmirror.com/@nestjs/swagger/11.2.0
    name: '@nestjs/swagger'
    version: 11.2.0
    peerDependencies:
      '@fastify/static': ^8.0.0
      '@nestjs/common': ^11.0.1
      '@nestjs/core': ^11.0.1
      class-transformer: '*'
      class-validator: '*'
      reflect-metadata: ^0.1.12 || ^0.2.0
    peerDependenciesMeta:
      '@fastify/static':
        optional: true
      class-transformer:
        optional: true
      class-validator:
        optional: true
    dependencies:
      '@microsoft/tsdoc': registry.npmmirror.com/@microsoft/tsdoc@0.15.1
      '@nestjs/common': registry.npmmirror.com/@nestjs/common@11.0.1(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.1)
      '@nestjs/core': registry.npmmirror.com/@nestjs/core@11.0.1(@nestjs/common@11.0.1)(@nestjs/platform-express@11.0.1)(reflect-metadata@0.2.2)(rxjs@7.8.1)
      '@nestjs/mapped-types': registry.npmmirror.com/@nestjs/mapped-types@2.1.0(@nestjs/common@11.0.1)(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)
      class-transformer: registry.npmmirror.com/class-transformer@0.5.1
      class-validator: registry.npmmirror.com/class-validator@0.14.2
      js-yaml: registry.npmmirror.com/js-yaml@4.1.0
      lodash: registry.npmmirror.com/lodash@4.17.21
      path-to-regexp: registry.npmmirror.com/path-to-regexp@8.2.0
      reflect-metadata: registry.npmmirror.com/reflect-metadata@0.2.2
      swagger-ui-dist: registry.npmmirror.com/swagger-ui-dist@5.21.0
    dev: false

  registry.npmmirror.com/@nestjs/testing@11.0.1(@nestjs/common@11.0.1)(@nestjs/core@11.0.1)(@nestjs/platform-express@11.0.1):
    resolution: {integrity: sha512-s1hQAC46W/qp2JJqCeqhG/uT8IBtvgIoAHklUPl7sTYSJCs745q6X2+rrOPtAewVW8CuYNhQS7/0g94QR1IHXw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@nestjs/testing/-/testing-11.0.1.tgz}
    id: registry.npmmirror.com/@nestjs/testing/11.0.1
    name: '@nestjs/testing'
    version: 11.0.1
    peerDependencies:
      '@nestjs/common': ^11.0.0
      '@nestjs/core': ^11.0.0
      '@nestjs/microservices': ^11.0.0
      '@nestjs/platform-express': ^11.0.0
    peerDependenciesMeta:
      '@nestjs/microservices':
        optional: true
      '@nestjs/platform-express':
        optional: true
    dependencies:
      '@nestjs/common': registry.npmmirror.com/@nestjs/common@11.0.1(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.1)
      '@nestjs/core': registry.npmmirror.com/@nestjs/core@11.0.1(@nestjs/common@11.0.1)(@nestjs/platform-express@11.0.1)(reflect-metadata@0.2.2)(rxjs@7.8.1)
      '@nestjs/platform-express': registry.npmmirror.com/@nestjs/platform-express@11.0.1(@nestjs/common@11.0.1)(@nestjs/core@11.0.1)
      tslib: registry.npmmirror.com/tslib@2.8.1
    dev: true

  registry.npmmirror.com/@nestjs/typeorm@11.0.0(@nestjs/common@11.0.1)(@nestjs/core@11.0.1)(reflect-metadata@0.2.2)(rxjs@7.8.1)(typeorm@0.3.25):
    resolution: {integrity: sha512-SOeUQl70Lb2OfhGkvnh4KXWlsd+zA08RuuQgT7kKbzivngxzSo1Oc7Usu5VxCxACQC9wc2l9esOHILSJeK7rJA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@nestjs/typeorm/-/typeorm-11.0.0.tgz}
    id: registry.npmmirror.com/@nestjs/typeorm/11.0.0
    name: '@nestjs/typeorm'
    version: 11.0.0
    peerDependencies:
      '@nestjs/common': ^10.0.0 || ^11.0.0
      '@nestjs/core': ^10.0.0 || ^11.0.0
      reflect-metadata: ^0.1.13 || ^0.2.0
      rxjs: ^7.2.0
      typeorm: ^0.3.0
    dependencies:
      '@nestjs/common': registry.npmmirror.com/@nestjs/common@11.0.1(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.1)
      '@nestjs/core': registry.npmmirror.com/@nestjs/core@11.0.1(@nestjs/common@11.0.1)(@nestjs/platform-express@11.0.1)(reflect-metadata@0.2.2)(rxjs@7.8.1)
      reflect-metadata: registry.npmmirror.com/reflect-metadata@0.2.2
      rxjs: registry.npmmirror.com/rxjs@7.8.1
      typeorm: registry.npmmirror.com/typeorm@0.3.25(mysql2@3.14.1)(reflect-metadata@0.2.2)(ts-node@10.9.2)
    dev: false

  registry.npmmirror.com/@noble/hashes@1.8.0:
    resolution: {integrity: sha512-jCs9ldd7NwzpgXDIf6P3+NrHh9/sD6CQdxHyjQI+h/6rDNo88ypBxxz45UDuZHz9r3tNz7N/VInSVoVdtXEI4A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@noble/hashes/-/hashes-1.8.0.tgz}
    name: '@noble/hashes'
    version: 1.8.0
    engines: {node: ^14.21.3 || >=16}
    dev: true

  registry.npmmirror.com/@nodelib/fs.scandir@2.1.5:
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz}
    name: '@nodelib/fs.scandir'
    version: 2.1.5
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.stat': registry.npmmirror.com/@nodelib/fs.stat@2.0.5
      run-parallel: registry.npmmirror.com/run-parallel@1.2.0
    dev: true

  registry.npmmirror.com/@nodelib/fs.stat@2.0.5:
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz}
    name: '@nodelib/fs.stat'
    version: 2.0.5
    engines: {node: '>= 8'}
    dev: true

  registry.npmmirror.com/@nodelib/fs.walk@1.2.8:
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz}
    name: '@nodelib/fs.walk'
    version: 1.2.8
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.scandir': registry.npmmirror.com/@nodelib/fs.scandir@2.1.5
      fastq: registry.npmmirror.com/fastq@1.19.1
    dev: true

  registry.npmmirror.com/@nuxt/opencollective@0.4.1:
    resolution: {integrity: sha512-GXD3wy50qYbxCJ652bDrDzgMr3NFEkIS374+IgFQKkCvk9yiYcLvX2XDYr7UyQxf4wK0e+yqDYRubZ0DtOxnmQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@nuxt/opencollective/-/opencollective-0.4.1.tgz}
    name: '@nuxt/opencollective'
    version: 0.4.1
    engines: {node: ^14.18.0 || >=16.10.0, npm: '>=5.10.0'}
    hasBin: true
    dependencies:
      consola: registry.npmmirror.com/consola@3.4.2

  registry.npmmirror.com/@paralleldrive/cuid2@2.2.2:
    resolution: {integrity: sha512-ZOBkgDwEdoYVlSeRbYYXs0S9MejQofiVYoTbKzy/6GQa39/q5tQU2IX46+shYnUkpEl3wc+J6wRlar7r2EK2xA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@paralleldrive/cuid2/-/cuid2-2.2.2.tgz}
    name: '@paralleldrive/cuid2'
    version: 2.2.2
    dependencies:
      '@noble/hashes': registry.npmmirror.com/@noble/hashes@1.8.0
    dev: true

  registry.npmmirror.com/@pkgjs/parseargs@0.11.0:
    resolution: {integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@pkgjs/parseargs/-/parseargs-0.11.0.tgz}
    name: '@pkgjs/parseargs'
    version: 0.11.0
    engines: {node: '>=14'}
    requiresBuild: true
    dev: false
    optional: true

  registry.npmmirror.com/@pkgr/core@0.1.2:
    resolution: {integrity: sha512-fdDH1LSGfZdTH2sxdpVMw31BanV28K/Gry0cVFxaNP77neJSkd82mM8ErPNYs9e+0O7SdHBLTDzDgwUuy18RnQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@pkgr/core/-/core-0.1.2.tgz}
    name: '@pkgr/core'
    version: 0.1.2
    engines: {node: ^12.20.0 || ^14.18.0 || >=16.0.0}
    dev: true

  registry.npmmirror.com/@scarf/scarf@1.4.0:
    resolution: {integrity: sha512-xxeapPiUXdZAE3che6f3xogoJPeZgig6omHEy1rIY5WVsB3H2BHNnZH+gHG6x91SCWyQCzWGsuL2Hh3ClO5/qQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@scarf/scarf/-/scarf-1.4.0.tgz}
    name: '@scarf/scarf'
    version: 1.4.0
    requiresBuild: true
    dev: false

  registry.npmmirror.com/@sec-ant/readable-stream@0.4.1:
    resolution: {integrity: sha512-831qok9r2t8AlxLko40y2ebgSDhenenCatLVeW/uBtnHPyhHOvG0C7TvfgecV+wHzIm5KUICgzmVpWS+IMEAeg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@sec-ant/readable-stream/-/readable-stream-0.4.1.tgz}
    name: '@sec-ant/readable-stream'
    version: 0.4.1
    dev: true

  registry.npmmirror.com/@sinclair/typebox@0.27.8:
    resolution: {integrity: sha512-+Fj43pSMwJs4KRrH/938Uf+uAELIgVBmQzg/q1YG10djyfA3TnrU8N8XzqCh/okZdszqBQTZf96idMfE5lnwTA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@sinclair/typebox/-/typebox-0.27.8.tgz}
    name: '@sinclair/typebox'
    version: 0.27.8
    dev: true

  registry.npmmirror.com/@sindresorhus/is@5.6.0:
    resolution: {integrity: sha512-TV7t8GKYaJWsn00tFDqBw8+Uqmr8A0fRU1tvTQhyZzGv0sJCGRQL3JGMI3ucuKo3XIZdUP+Lx7/gh2t3lewy7g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@sindresorhus/is/-/is-5.6.0.tgz}
    name: '@sindresorhus/is'
    version: 5.6.0
    engines: {node: '>=14.16'}
    dev: true

  registry.npmmirror.com/@sinonjs/commons@3.0.1:
    resolution: {integrity: sha512-K3mCHKQ9sVh8o1C9cxkwxaOmXoAMlDxC1mYyHrjqOWEcBjYr76t96zL2zlj5dUGZ3HSw240X1qgH3Mjf1yJWpQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@sinonjs/commons/-/commons-3.0.1.tgz}
    name: '@sinonjs/commons'
    version: 3.0.1
    dependencies:
      type-detect: registry.npmmirror.com/type-detect@4.0.8
    dev: true

  registry.npmmirror.com/@sinonjs/fake-timers@10.3.0:
    resolution: {integrity: sha512-V4BG07kuYSUkTCSBHG8G8TNhM+F19jXFWnQtzj+we8DrkpSBCee9Z3Ms8yiGer/dlmhe35/Xdgyo3/0rQKg7YA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@sinonjs/fake-timers/-/fake-timers-10.3.0.tgz}
    name: '@sinonjs/fake-timers'
    version: 10.3.0
    dependencies:
      '@sinonjs/commons': registry.npmmirror.com/@sinonjs/commons@3.0.1
    dev: true

  registry.npmmirror.com/@sqltools/formatter@1.2.5:
    resolution: {integrity: sha512-Uy0+khmZqUrUGm5dmMqVlnvufZRSK0FbYzVgp0UMstm+F5+W2/jnEEQyc9vo1ZR/E5ZI/B1WjjoTqBqwJL6Krw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@sqltools/formatter/-/formatter-1.2.5.tgz}
    name: '@sqltools/formatter'
    version: 1.2.5
    dev: false

  registry.npmmirror.com/@swc/cli@0.6.0(@swc/core@1.10.7):
    resolution: {integrity: sha512-Q5FsI3Cw0fGMXhmsg7c08i4EmXCrcl+WnAxb6LYOLHw4JFFC3yzmx9LaXZ7QMbA+JZXbigU2TirI7RAfO0Qlnw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@swc/cli/-/cli-0.6.0.tgz}
    id: registry.npmmirror.com/@swc/cli/0.6.0
    name: '@swc/cli'
    version: 0.6.0
    engines: {node: '>= 16.14.0'}
    hasBin: true
    peerDependencies:
      '@swc/core': ^1.2.66
      chokidar: ^4.0.1
    peerDependenciesMeta:
      chokidar:
        optional: true
    dependencies:
      '@swc/core': registry.npmmirror.com/@swc/core@1.10.7
      '@swc/counter': registry.npmmirror.com/@swc/counter@0.1.3
      '@xhmikosr/bin-wrapper': registry.npmmirror.com/@xhmikosr/bin-wrapper@13.0.5
      commander: registry.npmmirror.com/commander@8.3.0
      fast-glob: registry.npmmirror.com/fast-glob@3.3.3
      minimatch: registry.npmmirror.com/minimatch@9.0.5
      piscina: registry.npmmirror.com/piscina@4.9.2
      semver: registry.npmmirror.com/semver@7.7.2
      slash: registry.npmmirror.com/slash@3.0.0
      source-map: registry.npmmirror.com/source-map@0.7.4
    dev: true

  registry.npmmirror.com/@swc/core-darwin-arm64@1.10.7:
    resolution: {integrity: sha512-SI0OFg987P6hcyT0Dbng3YRISPS9uhLX1dzW4qRrfqQdb0i75lPJ2YWe9CN47HBazrIA5COuTzrD2Dc0TcVsSQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@swc/core-darwin-arm64/-/core-darwin-arm64-1.10.7.tgz}
    name: '@swc/core-darwin-arm64'
    version: 1.10.7
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    optional: true

  registry.npmmirror.com/@swc/core-darwin-x64@1.10.7:
    resolution: {integrity: sha512-RFIAmWVicD/l3RzxgHW0R/G1ya/6nyMspE2cAeDcTbjHi0I5qgdhBWd6ieXOaqwEwiCd0Mot1g2VZrLGoBLsjQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@swc/core-darwin-x64/-/core-darwin-x64-1.10.7.tgz}
    name: '@swc/core-darwin-x64'
    version: 1.10.7
    engines: {node: '>=10'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    optional: true

  registry.npmmirror.com/@swc/core-linux-arm-gnueabihf@1.10.7:
    resolution: {integrity: sha512-QP8vz7yELWfop5mM5foN6KkLylVO7ZUgWSF2cA0owwIaziactB2hCPZY5QU690coJouk9KmdFsPWDnaCFUP8tg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@swc/core-linux-arm-gnueabihf/-/core-linux-arm-gnueabihf-1.10.7.tgz}
    name: '@swc/core-linux-arm-gnueabihf'
    version: 1.10.7
    engines: {node: '>=10'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    optional: true

  registry.npmmirror.com/@swc/core-linux-arm64-gnu@1.10.7:
    resolution: {integrity: sha512-NgUDBGQcOeLNR+EOpmUvSDIP/F7i/OVOKxst4wOvT5FTxhnkWrW+StJGKj+DcUVSK5eWOYboSXr1y+Hlywwokw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@swc/core-linux-arm64-gnu/-/core-linux-arm64-gnu-1.10.7.tgz}
    name: '@swc/core-linux-arm64-gnu'
    version: 1.10.7
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    optional: true

  registry.npmmirror.com/@swc/core-linux-arm64-musl@1.10.7:
    resolution: {integrity: sha512-gp5Un3EbeSThBIh6oac5ZArV/CsSmTKj5jNuuUAuEsML3VF9vqPO+25VuxCvsRf/z3py+xOWRaN2HY/rjMeZog==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@swc/core-linux-arm64-musl/-/core-linux-arm64-musl-1.10.7.tgz}
    name: '@swc/core-linux-arm64-musl'
    version: 1.10.7
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    optional: true

  registry.npmmirror.com/@swc/core-linux-x64-gnu@1.10.7:
    resolution: {integrity: sha512-k/OxLLMl/edYqbZyUNg6/bqEHTXJT15l9WGqsl/2QaIGwWGvles8YjruQYQ9d4h/thSXLT9gd8bExU2D0N+bUA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@swc/core-linux-x64-gnu/-/core-linux-x64-gnu-1.10.7.tgz}
    name: '@swc/core-linux-x64-gnu'
    version: 1.10.7
    engines: {node: '>=10'}
    cpu: [x64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    optional: true

  registry.npmmirror.com/@swc/core-linux-x64-musl@1.10.7:
    resolution: {integrity: sha512-XeDoURdWt/ybYmXLCEE8aSiTOzEn0o3Dx5l9hgt0IZEmTts7HgHHVeRgzGXbR4yDo0MfRuX5nE1dYpTmCz0uyA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@swc/core-linux-x64-musl/-/core-linux-x64-musl-1.10.7.tgz}
    name: '@swc/core-linux-x64-musl'
    version: 1.10.7
    engines: {node: '>=10'}
    cpu: [x64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    optional: true

  registry.npmmirror.com/@swc/core-win32-arm64-msvc@1.10.7:
    resolution: {integrity: sha512-nYAbi/uLS+CU0wFtBx8TquJw2uIMKBnl04LBmiVoFrsIhqSl+0MklaA9FVMGA35NcxSJfcm92Prl2W2LfSnTqQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@swc/core-win32-arm64-msvc/-/core-win32-arm64-msvc-1.10.7.tgz}
    name: '@swc/core-win32-arm64-msvc'
    version: 1.10.7
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    optional: true

  registry.npmmirror.com/@swc/core-win32-ia32-msvc@1.10.7:
    resolution: {integrity: sha512-+aGAbsDsIxeLxw0IzyQLtvtAcI1ctlXVvVcXZMNXIXtTURM876yNrufRo4ngoXB3jnb1MLjIIjgXfFs/eZTUSw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@swc/core-win32-ia32-msvc/-/core-win32-ia32-msvc-1.10.7.tgz}
    name: '@swc/core-win32-ia32-msvc'
    version: 1.10.7
    engines: {node: '>=10'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    optional: true

  registry.npmmirror.com/@swc/core-win32-x64-msvc@1.10.7:
    resolution: {integrity: sha512-TBf4clpDBjF/UUnkKrT0/th76/zwvudk5wwobiTFqDywMApHip5O0VpBgZ+4raY2TM8k5+ujoy7bfHb22zu17Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@swc/core-win32-x64-msvc/-/core-win32-x64-msvc-1.10.7.tgz}
    name: '@swc/core-win32-x64-msvc'
    version: 1.10.7
    engines: {node: '>=10'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    optional: true

  registry.npmmirror.com/@swc/core@1.10.7:
    resolution: {integrity: sha512-py91kjI1jV5D5W/Q+PurBdGsdU5TFbrzamP7zSCqLdMcHkKi3rQEM5jkQcZr0MXXSJTaayLxS3MWYTBIkzPDrg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@swc/core/-/core-1.10.7.tgz}
    name: '@swc/core'
    version: 1.10.7
    engines: {node: '>=10'}
    requiresBuild: true
    peerDependencies:
      '@swc/helpers': '*'
    peerDependenciesMeta:
      '@swc/helpers':
        optional: true
    dependencies:
      '@swc/counter': registry.npmmirror.com/@swc/counter@0.1.3
      '@swc/types': registry.npmmirror.com/@swc/types@0.1.23
    optionalDependencies:
      '@swc/core-darwin-arm64': registry.npmmirror.com/@swc/core-darwin-arm64@1.10.7
      '@swc/core-darwin-x64': registry.npmmirror.com/@swc/core-darwin-x64@1.10.7
      '@swc/core-linux-arm-gnueabihf': registry.npmmirror.com/@swc/core-linux-arm-gnueabihf@1.10.7
      '@swc/core-linux-arm64-gnu': registry.npmmirror.com/@swc/core-linux-arm64-gnu@1.10.7
      '@swc/core-linux-arm64-musl': registry.npmmirror.com/@swc/core-linux-arm64-musl@1.10.7
      '@swc/core-linux-x64-gnu': registry.npmmirror.com/@swc/core-linux-x64-gnu@1.10.7
      '@swc/core-linux-x64-musl': registry.npmmirror.com/@swc/core-linux-x64-musl@1.10.7
      '@swc/core-win32-arm64-msvc': registry.npmmirror.com/@swc/core-win32-arm64-msvc@1.10.7
      '@swc/core-win32-ia32-msvc': registry.npmmirror.com/@swc/core-win32-ia32-msvc@1.10.7
      '@swc/core-win32-x64-msvc': registry.npmmirror.com/@swc/core-win32-x64-msvc@1.10.7

  registry.npmmirror.com/@swc/counter@0.1.3:
    resolution: {integrity: sha512-e2BR4lsJkkRlKZ/qCHPw9ZaSxc0MVUd7gtbtaB7aMvHeJVYe8sOB8DBZkP2DtISHGSku9sCK6T6cnY0CtXrOCQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@swc/counter/-/counter-0.1.3.tgz}
    name: '@swc/counter'
    version: 0.1.3

  registry.npmmirror.com/@swc/types@0.1.23:
    resolution: {integrity: sha512-u1iIVZV9Q0jxY+yM2vw/hZGDNudsN85bBpTqzAQ9rzkxW9D+e3aEM4Han+ow518gSewkXgjmEK0BD79ZcNVgPw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@swc/types/-/types-0.1.23.tgz}
    name: '@swc/types'
    version: 0.1.23
    dependencies:
      '@swc/counter': registry.npmmirror.com/@swc/counter@0.1.3

  registry.npmmirror.com/@szmarczak/http-timer@5.0.1:
    resolution: {integrity: sha512-+PmQX0PiAYPMeVYe237LJAYvOMYW1j2rH5YROyS3b4CTVJum34HfRvKvAzozHAQG0TnHNdUfY9nCeUyRAs//cw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@szmarczak/http-timer/-/http-timer-5.0.1.tgz}
    name: '@szmarczak/http-timer'
    version: 5.0.1
    engines: {node: '>=14.16'}
    dependencies:
      defer-to-connect: registry.npmmirror.com/defer-to-connect@2.0.1
    dev: true

  registry.npmmirror.com/@tokenizer/token@0.3.0:
    resolution: {integrity: sha512-OvjF+z51L3ov0OyAU0duzsYuvO01PH7x4t6DJx+guahgTnBHkhJdG7soQeTSFLWN3efnHyibZ4Z8l2EuWwJN3A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@tokenizer/token/-/token-0.3.0.tgz}
    name: '@tokenizer/token'
    version: 0.3.0
    dev: true

  registry.npmmirror.com/@tsconfig/node10@1.0.11:
    resolution: {integrity: sha512-DcRjDCujK/kCk/cUe8Xz8ZSpm8mS3mNNpta+jGCA6USEDfktlNvm1+IuZ9eTcDbNk41BHwpHHeW+N1lKCz4zOw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@tsconfig/node10/-/node10-1.0.11.tgz}
    name: '@tsconfig/node10'
    version: 1.0.11

  registry.npmmirror.com/@tsconfig/node12@1.0.11:
    resolution: {integrity: sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@tsconfig/node12/-/node12-1.0.11.tgz}
    name: '@tsconfig/node12'
    version: 1.0.11

  registry.npmmirror.com/@tsconfig/node14@1.0.3:
    resolution: {integrity: sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@tsconfig/node14/-/node14-1.0.3.tgz}
    name: '@tsconfig/node14'
    version: 1.0.3

  registry.npmmirror.com/@tsconfig/node16@1.0.4:
    resolution: {integrity: sha512-vxhUy4J8lyeyinH7Azl1pdd43GJhZH/tP2weN8TntQblOY+A0XbT8DJk1/oCPuOOyg/Ja757rG0CgHcWC8OfMA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@tsconfig/node16/-/node16-1.0.4.tgz}
    name: '@tsconfig/node16'
    version: 1.0.4

  registry.npmmirror.com/@types/babel__core@7.20.5:
    resolution: {integrity: sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/babel__core/-/babel__core-7.20.5.tgz}
    name: '@types/babel__core'
    version: 7.20.5
    dependencies:
      '@babel/parser': registry.npmmirror.com/@babel/parser@7.27.7
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.7
      '@types/babel__generator': registry.npmmirror.com/@types/babel__generator@7.27.0
      '@types/babel__template': registry.npmmirror.com/@types/babel__template@7.4.4
      '@types/babel__traverse': registry.npmmirror.com/@types/babel__traverse@7.20.7
    dev: true

  registry.npmmirror.com/@types/babel__generator@7.27.0:
    resolution: {integrity: sha512-ufFd2Xi92OAVPYsy+P4n7/U7e68fex0+Ee8gSG9KX7eo084CWiQ4sdxktvdl0bOPupXtVJPY19zk6EwWqUQ8lg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/babel__generator/-/babel__generator-7.27.0.tgz}
    name: '@types/babel__generator'
    version: 7.27.0
    dependencies:
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.7
    dev: true

  registry.npmmirror.com/@types/babel__template@7.4.4:
    resolution: {integrity: sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/babel__template/-/babel__template-7.4.4.tgz}
    name: '@types/babel__template'
    version: 7.4.4
    dependencies:
      '@babel/parser': registry.npmmirror.com/@babel/parser@7.27.7
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.7
    dev: true

  registry.npmmirror.com/@types/babel__traverse@7.20.7:
    resolution: {integrity: sha512-dkO5fhS7+/oos4ciWxyEyjWe48zmG6wbCheo/G2ZnHx4fs3EU6YC6UM8rk56gAjNJ9P3MTH2jo5jb92/K6wbng==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/babel__traverse/-/babel__traverse-7.20.7.tgz}
    name: '@types/babel__traverse'
    version: 7.20.7
    dependencies:
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.7
    dev: true

  registry.npmmirror.com/@types/bcryptjs@3.0.0:
    resolution: {integrity: sha512-WRZOuCuaz8UcZZE4R5HXTco2goQSI2XxjGY3hbM/xDvwmqFWd4ivooImsMx65OKM6CtNKbnZ5YL+YwAwK7c1dg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/bcryptjs/-/bcryptjs-3.0.0.tgz}
    name: '@types/bcryptjs'
    version: 3.0.0
    deprecated: This is a stub types definition. bcryptjs provides its own type definitions, so you do not need this installed.
    dependencies:
      bcryptjs: registry.npmmirror.com/bcryptjs@3.0.2
    dev: true

  registry.npmmirror.com/@types/body-parser@1.19.6:
    resolution: {integrity: sha512-HLFeCYgz89uk22N5Qg3dvGvsv46B8GLvKKo1zKG4NybA8U2DiEO3w9lqGg29t/tfLRJpJ6iQxnVw4OnB7MoM9g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/body-parser/-/body-parser-1.19.6.tgz}
    name: '@types/body-parser'
    version: 1.19.6
    dependencies:
      '@types/connect': registry.npmmirror.com/@types/connect@3.4.38
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
    dev: true

  registry.npmmirror.com/@types/connect@3.4.38:
    resolution: {integrity: sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/connect/-/connect-3.4.38.tgz}
    name: '@types/connect'
    version: 3.4.38
    dependencies:
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
    dev: true

  registry.npmmirror.com/@types/cookiejar@2.1.5:
    resolution: {integrity: sha512-he+DHOWReW0nghN24E1WUqM0efK4kI9oTqDm6XmK8ZPe2djZ90BSNdGnIyCLzCPw7/pogPlGbzI2wHGGmi4O/Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/cookiejar/-/cookiejar-2.1.5.tgz}
    name: '@types/cookiejar'
    version: 2.1.5
    dev: true

  registry.npmmirror.com/@types/eslint-scope@3.7.7:
    resolution: {integrity: sha512-MzMFlSLBqNF2gcHWO0G1vP/YQyfvrxZ0bF+u7mzUdZ1/xK4A4sru+nraZz5i3iEIk1l1uyicaDVTB4QbbEkAYg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/eslint-scope/-/eslint-scope-3.7.7.tgz}
    name: '@types/eslint-scope'
    version: 3.7.7
    dependencies:
      '@types/eslint': registry.npmmirror.com/@types/eslint@9.6.1
      '@types/estree': registry.npmmirror.com/@types/estree@1.0.8
    dev: true

  registry.npmmirror.com/@types/eslint@9.6.1:
    resolution: {integrity: sha512-FXx2pKgId/WyYo2jXw63kk7/+TY7u7AziEJxJAnSFzHlqTAS3Ync6SvgYAN/k4/PQpnnVuzoMuVnByKK2qp0ag==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/eslint/-/eslint-9.6.1.tgz}
    name: '@types/eslint'
    version: 9.6.1
    dependencies:
      '@types/estree': registry.npmmirror.com/@types/estree@1.0.8
      '@types/json-schema': registry.npmmirror.com/@types/json-schema@7.0.15
    dev: true

  registry.npmmirror.com/@types/estree@1.0.8:
    resolution: {integrity: sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/estree/-/estree-1.0.8.tgz}
    name: '@types/estree'
    version: 1.0.8
    dev: true

  registry.npmmirror.com/@types/express-serve-static-core@5.0.6:
    resolution: {integrity: sha512-3xhRnjJPkULekpSzgtoNYYcTWgEZkp4myc+Saevii5JPnHNvHMRlBSHDbs7Bh1iPPoVTERHEZXyhyLbMEsExsA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/express-serve-static-core/-/express-serve-static-core-5.0.6.tgz}
    name: '@types/express-serve-static-core'
    version: 5.0.6
    dependencies:
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      '@types/qs': registry.npmmirror.com/@types/qs@6.14.0
      '@types/range-parser': registry.npmmirror.com/@types/range-parser@1.2.7
      '@types/send': registry.npmmirror.com/@types/send@0.17.5
    dev: true

  registry.npmmirror.com/@types/express@5.0.0:
    resolution: {integrity: sha512-DvZriSMehGHL1ZNLzi6MidnsDhUZM/x2pRdDIKdwbUNqqwHxMlRdkxtn6/EPKyqKpHqTl/4nRZsRNLpZxZRpPQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/express/-/express-5.0.0.tgz}
    name: '@types/express'
    version: 5.0.0
    dependencies:
      '@types/body-parser': registry.npmmirror.com/@types/body-parser@1.19.6
      '@types/express-serve-static-core': registry.npmmirror.com/@types/express-serve-static-core@5.0.6
      '@types/qs': registry.npmmirror.com/@types/qs@6.14.0
      '@types/serve-static': registry.npmmirror.com/@types/serve-static@1.15.8
    dev: true

  registry.npmmirror.com/@types/graceful-fs@4.1.9:
    resolution: {integrity: sha512-olP3sd1qOEe5dXTSaFvQG+02VdRXcdytWLAZsAq1PecU8uqQAhkrnbli7DagjtXKW/Bl7YJbUsa8MPcuc8LHEQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/graceful-fs/-/graceful-fs-4.1.9.tgz}
    name: '@types/graceful-fs'
    version: 4.1.9
    dependencies:
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
    dev: true

  registry.npmmirror.com/@types/http-cache-semantics@4.0.4:
    resolution: {integrity: sha512-1m0bIFVc7eJWyve9S0RnuRgcQqF/Xd5QsUZAZeQFr1Q3/p9JWoQQEqmVy+DPTNpGXwhgIetAoYF8JSc33q29QA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/http-cache-semantics/-/http-cache-semantics-4.0.4.tgz}
    name: '@types/http-cache-semantics'
    version: 4.0.4
    dev: true

  registry.npmmirror.com/@types/http-errors@2.0.5:
    resolution: {integrity: sha512-r8Tayk8HJnX0FztbZN7oVqGccWgw98T/0neJphO91KkmOzug1KkofZURD4UaD5uH8AqcFLfdPErnBod0u71/qg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/http-errors/-/http-errors-2.0.5.tgz}
    name: '@types/http-errors'
    version: 2.0.5
    dev: true

  registry.npmmirror.com/@types/istanbul-lib-coverage@2.0.6:
    resolution: {integrity: sha512-2QF/t/auWm0lsy8XtKVPG19v3sSOQlJe/YHZgfjb/KBBHOGSV+J2q/S671rcq9uTBrLAXmZpqJiaQbMT+zNU1w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz}
    name: '@types/istanbul-lib-coverage'
    version: 2.0.6
    dev: true

  registry.npmmirror.com/@types/istanbul-lib-report@3.0.3:
    resolution: {integrity: sha512-NQn7AHQnk/RSLOxrBbGyJM/aVQ+pjj5HCgasFxc0K/KhoATfQ/47AyUl15I2yBUpihjmas+a+VJBOqecrFH+uA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz}
    name: '@types/istanbul-lib-report'
    version: 3.0.3
    dependencies:
      '@types/istanbul-lib-coverage': registry.npmmirror.com/@types/istanbul-lib-coverage@2.0.6
    dev: true

  registry.npmmirror.com/@types/istanbul-reports@3.0.4:
    resolution: {integrity: sha512-pk2B1NWalF9toCRu6gjBzR69syFjP4Od8WRAX+0mmf9lAjCRicLOWc+ZrxZHx/0XRjotgkF9t6iaMJ+aXcOdZQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz}
    name: '@types/istanbul-reports'
    version: 3.0.4
    dependencies:
      '@types/istanbul-lib-report': registry.npmmirror.com/@types/istanbul-lib-report@3.0.3
    dev: true

  registry.npmmirror.com/@types/jest@29.5.14:
    resolution: {integrity: sha512-ZN+4sdnLUbo8EVvVc2ao0GFW6oVrQRPn4K2lglySj7APvSrgzxHiNNK99us4WDMi57xxA2yggblIAMNhXOotLQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/jest/-/jest-29.5.14.tgz}
    name: '@types/jest'
    version: 29.5.14
    dependencies:
      expect: registry.npmmirror.com/expect@29.7.0
      pretty-format: registry.npmmirror.com/pretty-format@29.7.0
    dev: true

  registry.npmmirror.com/@types/json-schema@7.0.15:
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/json-schema/-/json-schema-7.0.15.tgz}
    name: '@types/json-schema'
    version: 7.0.15
    dev: true

  registry.npmmirror.com/@types/jsonwebtoken@9.0.7:
    resolution: {integrity: sha512-ugo316mmTYBl2g81zDFnZ7cfxlut3o+/EQdaP7J8QN2kY6lJ22hmQYCK5EHcJHbrW+dkCGSCPgbG8JtYj6qSrg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/jsonwebtoken/-/jsonwebtoken-9.0.7.tgz}
    name: '@types/jsonwebtoken'
    version: 9.0.7
    dependencies:
      '@types/node': registry.npmmirror.com/@types/node@22.10.7

  registry.npmmirror.com/@types/methods@1.1.4:
    resolution: {integrity: sha512-ymXWVrDiCxTBE3+RIrrP533E70eA+9qu7zdWoHuOmGujkYtzf4HQF96b8nwHLqhuf4ykX61IGRIB38CC6/sImQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/methods/-/methods-1.1.4.tgz}
    name: '@types/methods'
    version: 1.1.4
    dev: true

  registry.npmmirror.com/@types/mime@1.3.5:
    resolution: {integrity: sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/mime/-/mime-1.3.5.tgz}
    name: '@types/mime'
    version: 1.3.5
    dev: true

  registry.npmmirror.com/@types/node@22.10.7:
    resolution: {integrity: sha512-V09KvXxFiutGp6B7XkpaDXlNadZxrzajcY50EuoLIpQ6WWYCSvf19lVIazzfIzQvhUN2HjX12spLojTnhuKlGg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/node/-/node-22.10.7.tgz}
    name: '@types/node'
    version: 22.10.7
    dependencies:
      undici-types: registry.npmmirror.com/undici-types@6.20.0

  registry.npmmirror.com/@types/passport-jwt@4.0.1:
    resolution: {integrity: sha512-Y0Ykz6nWP4jpxgEUYq8NoVZeCQPo1ZndJLfapI249g1jHChvRfZRO/LS3tqu26YgAS/laI1qx98sYGz0IalRXQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/passport-jwt/-/passport-jwt-4.0.1.tgz}
    name: '@types/passport-jwt'
    version: 4.0.1
    dependencies:
      '@types/jsonwebtoken': registry.npmmirror.com/@types/jsonwebtoken@9.0.7
      '@types/passport-strategy': registry.npmmirror.com/@types/passport-strategy@0.2.38
    dev: true

  registry.npmmirror.com/@types/passport-strategy@0.2.38:
    resolution: {integrity: sha512-GC6eMqqojOooq993Tmnmp7AUTbbQSgilyvpCYQjT+H6JfG/g6RGc7nXEniZlp0zyKJ0WUdOiZWLBZft9Yug1uA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/passport-strategy/-/passport-strategy-0.2.38.tgz}
    name: '@types/passport-strategy'
    version: 0.2.38
    dependencies:
      '@types/express': registry.npmmirror.com/@types/express@5.0.0
      '@types/passport': registry.npmmirror.com/@types/passport@1.0.17
    dev: true

  registry.npmmirror.com/@types/passport@1.0.17:
    resolution: {integrity: sha512-aciLyx+wDwT2t2/kJGJR2AEeBz0nJU4WuRX04Wu9Dqc5lSUtwu0WERPHYsLhF9PtseiAMPBGNUOtFjxZ56prsg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/passport/-/passport-1.0.17.tgz}
    name: '@types/passport'
    version: 1.0.17
    dependencies:
      '@types/express': registry.npmmirror.com/@types/express@5.0.0
    dev: true

  registry.npmmirror.com/@types/qs@6.14.0:
    resolution: {integrity: sha512-eOunJqu0K1923aExK6y8p6fsihYEn/BYuQ4g0CxAAgFc4b/ZLN4CrsRZ55srTdqoiLzU2B2evC+apEIxprEzkQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/qs/-/qs-6.14.0.tgz}
    name: '@types/qs'
    version: 6.14.0
    dev: true

  registry.npmmirror.com/@types/range-parser@1.2.7:
    resolution: {integrity: sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/range-parser/-/range-parser-1.2.7.tgz}
    name: '@types/range-parser'
    version: 1.2.7
    dev: true

  registry.npmmirror.com/@types/send@0.17.5:
    resolution: {integrity: sha512-z6F2D3cOStZvuk2SaP6YrwkNO65iTZcwA2ZkSABegdkAh/lf+Aa/YQndZVfmEXT5vgAp6zv06VQ3ejSVjAny4w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/send/-/send-0.17.5.tgz}
    name: '@types/send'
    version: 0.17.5
    dependencies:
      '@types/mime': registry.npmmirror.com/@types/mime@1.3.5
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
    dev: true

  registry.npmmirror.com/@types/serve-static@1.15.8:
    resolution: {integrity: sha512-roei0UY3LhpOJvjbIP6ZZFngyLKl5dskOtDhxY5THRSpO+ZI+nzJ+m5yUMzGrp89YRa7lvknKkMYjqQFGwA7Sg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/serve-static/-/serve-static-1.15.8.tgz}
    name: '@types/serve-static'
    version: 1.15.8
    dependencies:
      '@types/http-errors': registry.npmmirror.com/@types/http-errors@2.0.5
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      '@types/send': registry.npmmirror.com/@types/send@0.17.5
    dev: true

  registry.npmmirror.com/@types/stack-utils@2.0.3:
    resolution: {integrity: sha512-9aEbYZ3TbYMznPdcdr3SmIrLXwC/AKZXQeCf9Pgao5CKb8CyHuEX5jzWPTkvregvhRJHcpRO6BFoGW9ycaOkYw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/stack-utils/-/stack-utils-2.0.3.tgz}
    name: '@types/stack-utils'
    version: 2.0.3
    dev: true

  registry.npmmirror.com/@types/superagent@8.1.9:
    resolution: {integrity: sha512-pTVjI73witn+9ILmoJdajHGW2jkSaOzhiFYF1Rd3EQ94kymLqB9PjD9ISg7WaALC7+dCHT0FGe9T2LktLq/3GQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/superagent/-/superagent-8.1.9.tgz}
    name: '@types/superagent'
    version: 8.1.9
    dependencies:
      '@types/cookiejar': registry.npmmirror.com/@types/cookiejar@2.1.5
      '@types/methods': registry.npmmirror.com/@types/methods@1.1.4
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      form-data: registry.npmmirror.com/form-data@4.0.3
    dev: true

  registry.npmmirror.com/@types/supertest@6.0.2:
    resolution: {integrity: sha512-137ypx2lk/wTQbW6An6safu9hXmajAifU/s7szAHLN/FeIm5w7yR0Wkl9fdJMRSHwOn4HLAI0DaB2TOORuhPDg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/supertest/-/supertest-6.0.2.tgz}
    name: '@types/supertest'
    version: 6.0.2
    dependencies:
      '@types/methods': registry.npmmirror.com/@types/methods@1.1.4
      '@types/superagent': registry.npmmirror.com/@types/superagent@8.1.9
    dev: true

  registry.npmmirror.com/@types/validator@13.15.2:
    resolution: {integrity: sha512-y7pa/oEJJ4iGYBxOpfAKn5b9+xuihvzDVnC/OSvlVnGxVg0pOqmjiMafiJ1KVNQEaPZf9HsEp5icEwGg8uIe5Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/validator/-/validator-13.15.2.tgz}
    name: '@types/validator'
    version: 13.15.2

  registry.npmmirror.com/@types/yargs-parser@21.0.3:
    resolution: {integrity: sha512-I4q9QU9MQv4oEOz4tAHJtNz1cwuLxn2F3xcc2iV5WdqLPpUnj30aUuxt1mAxYTG+oe8CZMV/+6rU4S4gRDzqtQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/yargs-parser/-/yargs-parser-21.0.3.tgz}
    name: '@types/yargs-parser'
    version: 21.0.3
    dev: true

  registry.npmmirror.com/@types/yargs@17.0.33:
    resolution: {integrity: sha512-WpxBCKWPLr4xSsHgz511rFJAM+wS28w2zEO1QDNY5zM/S8ok70NNfztH0xwhqKyaK0OHCbN98LDAZuy1ctxDkA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/yargs/-/yargs-17.0.33.tgz}
    name: '@types/yargs'
    version: 17.0.33
    dependencies:
      '@types/yargs-parser': registry.npmmirror.com/@types/yargs-parser@21.0.3
    dev: true

  registry.npmmirror.com/@typescript-eslint/eslint-plugin@8.20.0(@typescript-eslint/parser@8.20.0)(eslint@9.18.0)(typescript@5.7.3):
    resolution: {integrity: sha512-naduuphVw5StFfqp4Gq4WhIBE2gN1GEmMUExpJYknZJdRnc+2gDzB8Z3+5+/Kv33hPQRDGzQO/0opHE72lZZ6A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@typescript-eslint/eslint-plugin/-/eslint-plugin-8.20.0.tgz}
    id: registry.npmmirror.com/@typescript-eslint/eslint-plugin/8.20.0
    name: '@typescript-eslint/eslint-plugin'
    version: 8.20.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      '@typescript-eslint/parser': ^8.0.0 || ^8.0.0-alpha.0
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.8.0'
    dependencies:
      '@eslint-community/regexpp': registry.npmmirror.com/@eslint-community/regexpp@4.12.1
      '@typescript-eslint/parser': registry.npmmirror.com/@typescript-eslint/parser@8.20.0(eslint@9.18.0)(typescript@5.7.3)
      '@typescript-eslint/scope-manager': registry.npmmirror.com/@typescript-eslint/scope-manager@8.20.0
      '@typescript-eslint/type-utils': registry.npmmirror.com/@typescript-eslint/type-utils@8.20.0(eslint@9.18.0)(typescript@5.7.3)
      '@typescript-eslint/utils': registry.npmmirror.com/@typescript-eslint/utils@8.20.0(eslint@9.18.0)(typescript@5.7.3)
      '@typescript-eslint/visitor-keys': registry.npmmirror.com/@typescript-eslint/visitor-keys@8.20.0
      eslint: registry.npmmirror.com/eslint@9.18.0
      graphemer: registry.npmmirror.com/graphemer@1.4.0
      ignore: registry.npmmirror.com/ignore@5.3.2
      natural-compare: registry.npmmirror.com/natural-compare@1.4.0
      ts-api-utils: registry.npmmirror.com/ts-api-utils@2.1.0(typescript@5.7.3)
      typescript: registry.npmmirror.com/typescript@5.7.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@typescript-eslint/parser@8.20.0(eslint@9.18.0)(typescript@5.7.3):
    resolution: {integrity: sha512-gKXG7A5HMyjDIedBi6bUrDcun8GIjnI8qOwVLiY3rx6T/sHP/19XLJOnIq/FgQvWLHja5JN/LSE7eklNBr612g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@typescript-eslint/parser/-/parser-8.20.0.tgz}
    id: registry.npmmirror.com/@typescript-eslint/parser/8.20.0
    name: '@typescript-eslint/parser'
    version: 8.20.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.8.0'
    dependencies:
      '@typescript-eslint/scope-manager': registry.npmmirror.com/@typescript-eslint/scope-manager@8.20.0
      '@typescript-eslint/types': registry.npmmirror.com/@typescript-eslint/types@8.20.0
      '@typescript-eslint/typescript-estree': registry.npmmirror.com/@typescript-eslint/typescript-estree@8.20.0(typescript@5.7.3)
      '@typescript-eslint/visitor-keys': registry.npmmirror.com/@typescript-eslint/visitor-keys@8.20.0
      debug: registry.npmmirror.com/debug@4.4.1
      eslint: registry.npmmirror.com/eslint@9.18.0
      typescript: registry.npmmirror.com/typescript@5.7.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@typescript-eslint/scope-manager@8.20.0:
    resolution: {integrity: sha512-J7+VkpeGzhOt3FeG1+SzhiMj9NzGD/M6KoGn9f4dbz3YzK9hvbhVTmLj/HiTp9DazIzJ8B4XcM80LrR9Dm1rJw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@typescript-eslint/scope-manager/-/scope-manager-8.20.0.tgz}
    name: '@typescript-eslint/scope-manager'
    version: 8.20.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      '@typescript-eslint/types': registry.npmmirror.com/@typescript-eslint/types@8.20.0
      '@typescript-eslint/visitor-keys': registry.npmmirror.com/@typescript-eslint/visitor-keys@8.20.0
    dev: true

  registry.npmmirror.com/@typescript-eslint/type-utils@8.20.0(eslint@9.18.0)(typescript@5.7.3):
    resolution: {integrity: sha512-bPC+j71GGvA7rVNAHAtOjbVXbLN5PkwqMvy1cwGeaxUoRQXVuKCebRoLzm+IPW/NtFFpstn1ummSIasD5t60GA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@typescript-eslint/type-utils/-/type-utils-8.20.0.tgz}
    id: registry.npmmirror.com/@typescript-eslint/type-utils/8.20.0
    name: '@typescript-eslint/type-utils'
    version: 8.20.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.8.0'
    dependencies:
      '@typescript-eslint/typescript-estree': registry.npmmirror.com/@typescript-eslint/typescript-estree@8.20.0(typescript@5.7.3)
      '@typescript-eslint/utils': registry.npmmirror.com/@typescript-eslint/utils@8.20.0(eslint@9.18.0)(typescript@5.7.3)
      debug: registry.npmmirror.com/debug@4.4.1
      eslint: registry.npmmirror.com/eslint@9.18.0
      ts-api-utils: registry.npmmirror.com/ts-api-utils@2.1.0(typescript@5.7.3)
      typescript: registry.npmmirror.com/typescript@5.7.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@typescript-eslint/types@8.20.0:
    resolution: {integrity: sha512-cqaMiY72CkP+2xZRrFt3ExRBu0WmVitN/rYPZErA80mHjHx/Svgp8yfbzkJmDoQ/whcytOPO9/IZXnOc+wigRA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@typescript-eslint/types/-/types-8.20.0.tgz}
    name: '@typescript-eslint/types'
    version: 8.20.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dev: true

  registry.npmmirror.com/@typescript-eslint/typescript-estree@8.20.0(typescript@5.7.3):
    resolution: {integrity: sha512-Y7ncuy78bJqHI35NwzWol8E0X7XkRVS4K4P4TCyzWkOJih5NDvtoRDW4Ba9YJJoB2igm9yXDdYI/+fkiiAxPzA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@typescript-eslint/typescript-estree/-/typescript-estree-8.20.0.tgz}
    id: registry.npmmirror.com/@typescript-eslint/typescript-estree/8.20.0
    name: '@typescript-eslint/typescript-estree'
    version: 8.20.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <5.8.0'
    dependencies:
      '@typescript-eslint/types': registry.npmmirror.com/@typescript-eslint/types@8.20.0
      '@typescript-eslint/visitor-keys': registry.npmmirror.com/@typescript-eslint/visitor-keys@8.20.0
      debug: registry.npmmirror.com/debug@4.4.1
      fast-glob: registry.npmmirror.com/fast-glob@3.3.3
      is-glob: registry.npmmirror.com/is-glob@4.0.3
      minimatch: registry.npmmirror.com/minimatch@9.0.5
      semver: registry.npmmirror.com/semver@7.7.2
      ts-api-utils: registry.npmmirror.com/ts-api-utils@2.1.0(typescript@5.7.3)
      typescript: registry.npmmirror.com/typescript@5.7.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@typescript-eslint/utils@8.20.0(eslint@9.18.0)(typescript@5.7.3):
    resolution: {integrity: sha512-dq70RUw6UK9ei7vxc4KQtBRk7qkHZv447OUZ6RPQMQl71I3NZxQJX/f32Smr+iqWrB02pHKn2yAdHBb0KNrRMA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@typescript-eslint/utils/-/utils-8.20.0.tgz}
    id: registry.npmmirror.com/@typescript-eslint/utils/8.20.0
    name: '@typescript-eslint/utils'
    version: 8.20.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.8.0'
    dependencies:
      '@eslint-community/eslint-utils': registry.npmmirror.com/@eslint-community/eslint-utils@4.7.0(eslint@9.18.0)
      '@typescript-eslint/scope-manager': registry.npmmirror.com/@typescript-eslint/scope-manager@8.20.0
      '@typescript-eslint/types': registry.npmmirror.com/@typescript-eslint/types@8.20.0
      '@typescript-eslint/typescript-estree': registry.npmmirror.com/@typescript-eslint/typescript-estree@8.20.0(typescript@5.7.3)
      eslint: registry.npmmirror.com/eslint@9.18.0
      typescript: registry.npmmirror.com/typescript@5.7.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@typescript-eslint/visitor-keys@8.20.0:
    resolution: {integrity: sha512-v/BpkeeYAsPkKCkR8BDwcno0llhzWVqPOamQrAEMdpZav2Y9OVjd9dwJyBLJWwf335B5DmlifECIkZRJCaGaHA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@typescript-eslint/visitor-keys/-/visitor-keys-8.20.0.tgz}
    name: '@typescript-eslint/visitor-keys'
    version: 8.20.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      '@typescript-eslint/types': registry.npmmirror.com/@typescript-eslint/types@8.20.0
      eslint-visitor-keys: registry.npmmirror.com/eslint-visitor-keys@4.2.1
    dev: true

  registry.npmmirror.com/@webassemblyjs/ast@1.14.1:
    resolution: {integrity: sha512-nuBEDgQfm1ccRp/8bCQrx1frohyufl4JlbMMZ4P1wpeOfDhF6FQkxZJ1b/e+PLwr6X1Nhw6OLme5usuBWYBvuQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@webassemblyjs/ast/-/ast-1.14.1.tgz}
    name: '@webassemblyjs/ast'
    version: 1.14.1
    dependencies:
      '@webassemblyjs/helper-numbers': registry.npmmirror.com/@webassemblyjs/helper-numbers@1.13.2
      '@webassemblyjs/helper-wasm-bytecode': registry.npmmirror.com/@webassemblyjs/helper-wasm-bytecode@1.13.2
    dev: true

  registry.npmmirror.com/@webassemblyjs/floating-point-hex-parser@1.13.2:
    resolution: {integrity: sha512-6oXyTOzbKxGH4steLbLNOu71Oj+C8Lg34n6CqRvqfS2O71BxY6ByfMDRhBytzknj9yGUPVJ1qIKhRlAwO1AovA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.13.2.tgz}
    name: '@webassemblyjs/floating-point-hex-parser'
    version: 1.13.2
    dev: true

  registry.npmmirror.com/@webassemblyjs/helper-api-error@1.13.2:
    resolution: {integrity: sha512-U56GMYxy4ZQCbDZd6JuvvNV/WFildOjsaWD3Tzzvmw/mas3cXzRJPMjP83JqEsgSbyrmaGjBfDtV7KDXV9UzFQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@webassemblyjs/helper-api-error/-/helper-api-error-1.13.2.tgz}
    name: '@webassemblyjs/helper-api-error'
    version: 1.13.2
    dev: true

  registry.npmmirror.com/@webassemblyjs/helper-buffer@1.14.1:
    resolution: {integrity: sha512-jyH7wtcHiKssDtFPRB+iQdxlDf96m0E39yb0k5uJVhFGleZFoNw1c4aeIcVUPPbXUVJ94wwnMOAqUHyzoEPVMA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@webassemblyjs/helper-buffer/-/helper-buffer-1.14.1.tgz}
    name: '@webassemblyjs/helper-buffer'
    version: 1.14.1
    dev: true

  registry.npmmirror.com/@webassemblyjs/helper-numbers@1.13.2:
    resolution: {integrity: sha512-FE8aCmS5Q6eQYcV3gI35O4J789wlQA+7JrqTTpJqn5emA4U2hvwJmvFRC0HODS+3Ye6WioDklgd6scJ3+PLnEA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@webassemblyjs/helper-numbers/-/helper-numbers-1.13.2.tgz}
    name: '@webassemblyjs/helper-numbers'
    version: 1.13.2
    dependencies:
      '@webassemblyjs/floating-point-hex-parser': registry.npmmirror.com/@webassemblyjs/floating-point-hex-parser@1.13.2
      '@webassemblyjs/helper-api-error': registry.npmmirror.com/@webassemblyjs/helper-api-error@1.13.2
      '@xtuc/long': registry.npmmirror.com/@xtuc/long@4.2.2
    dev: true

  registry.npmmirror.com/@webassemblyjs/helper-wasm-bytecode@1.13.2:
    resolution: {integrity: sha512-3QbLKy93F0EAIXLh0ogEVR6rOubA9AoZ+WRYhNbFyuB70j3dRdwH9g+qXhLAO0kiYGlg3TxDV+I4rQTr/YNXkA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.13.2.tgz}
    name: '@webassemblyjs/helper-wasm-bytecode'
    version: 1.13.2
    dev: true

  registry.npmmirror.com/@webassemblyjs/helper-wasm-section@1.14.1:
    resolution: {integrity: sha512-ds5mXEqTJ6oxRoqjhWDU83OgzAYjwsCV8Lo/N+oRsNDmx/ZDpqalmrtgOMkHwxsG0iI//3BwWAErYRHtgn0dZw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.14.1.tgz}
    name: '@webassemblyjs/helper-wasm-section'
    version: 1.14.1
    dependencies:
      '@webassemblyjs/ast': registry.npmmirror.com/@webassemblyjs/ast@1.14.1
      '@webassemblyjs/helper-buffer': registry.npmmirror.com/@webassemblyjs/helper-buffer@1.14.1
      '@webassemblyjs/helper-wasm-bytecode': registry.npmmirror.com/@webassemblyjs/helper-wasm-bytecode@1.13.2
      '@webassemblyjs/wasm-gen': registry.npmmirror.com/@webassemblyjs/wasm-gen@1.14.1
    dev: true

  registry.npmmirror.com/@webassemblyjs/ieee754@1.13.2:
    resolution: {integrity: sha512-4LtOzh58S/5lX4ITKxnAK2USuNEvpdVV9AlgGQb8rJDHaLeHciwG4zlGr0j/SNWlr7x3vO1lDEsuePvtcDNCkw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@webassemblyjs/ieee754/-/ieee754-1.13.2.tgz}
    name: '@webassemblyjs/ieee754'
    version: 1.13.2
    dependencies:
      '@xtuc/ieee754': registry.npmmirror.com/@xtuc/ieee754@1.2.0
    dev: true

  registry.npmmirror.com/@webassemblyjs/leb128@1.13.2:
    resolution: {integrity: sha512-Lde1oNoIdzVzdkNEAWZ1dZ5orIbff80YPdHx20mrHwHrVNNTjNr8E3xz9BdpcGqRQbAEa+fkrCb+fRFTl/6sQw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@webassemblyjs/leb128/-/leb128-1.13.2.tgz}
    name: '@webassemblyjs/leb128'
    version: 1.13.2
    dependencies:
      '@xtuc/long': registry.npmmirror.com/@xtuc/long@4.2.2
    dev: true

  registry.npmmirror.com/@webassemblyjs/utf8@1.13.2:
    resolution: {integrity: sha512-3NQWGjKTASY1xV5m7Hr0iPeXD9+RDobLll3T9d2AO+g3my8xy5peVyjSag4I50mR1bBSN/Ct12lo+R9tJk0NZQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@webassemblyjs/utf8/-/utf8-1.13.2.tgz}
    name: '@webassemblyjs/utf8'
    version: 1.13.2
    dev: true

  registry.npmmirror.com/@webassemblyjs/wasm-edit@1.14.1:
    resolution: {integrity: sha512-RNJUIQH/J8iA/1NzlE4N7KtyZNHi3w7at7hDjvRNm5rcUXa00z1vRz3glZoULfJ5mpvYhLybmVcwcjGrC1pRrQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@webassemblyjs/wasm-edit/-/wasm-edit-1.14.1.tgz}
    name: '@webassemblyjs/wasm-edit'
    version: 1.14.1
    dependencies:
      '@webassemblyjs/ast': registry.npmmirror.com/@webassemblyjs/ast@1.14.1
      '@webassemblyjs/helper-buffer': registry.npmmirror.com/@webassemblyjs/helper-buffer@1.14.1
      '@webassemblyjs/helper-wasm-bytecode': registry.npmmirror.com/@webassemblyjs/helper-wasm-bytecode@1.13.2
      '@webassemblyjs/helper-wasm-section': registry.npmmirror.com/@webassemblyjs/helper-wasm-section@1.14.1
      '@webassemblyjs/wasm-gen': registry.npmmirror.com/@webassemblyjs/wasm-gen@1.14.1
      '@webassemblyjs/wasm-opt': registry.npmmirror.com/@webassemblyjs/wasm-opt@1.14.1
      '@webassemblyjs/wasm-parser': registry.npmmirror.com/@webassemblyjs/wasm-parser@1.14.1
      '@webassemblyjs/wast-printer': registry.npmmirror.com/@webassemblyjs/wast-printer@1.14.1
    dev: true

  registry.npmmirror.com/@webassemblyjs/wasm-gen@1.14.1:
    resolution: {integrity: sha512-AmomSIjP8ZbfGQhumkNvgC33AY7qtMCXnN6bL2u2Js4gVCg8fp735aEiMSBbDR7UQIj90n4wKAFUSEd0QN2Ukg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@webassemblyjs/wasm-gen/-/wasm-gen-1.14.1.tgz}
    name: '@webassemblyjs/wasm-gen'
    version: 1.14.1
    dependencies:
      '@webassemblyjs/ast': registry.npmmirror.com/@webassemblyjs/ast@1.14.1
      '@webassemblyjs/helper-wasm-bytecode': registry.npmmirror.com/@webassemblyjs/helper-wasm-bytecode@1.13.2
      '@webassemblyjs/ieee754': registry.npmmirror.com/@webassemblyjs/ieee754@1.13.2
      '@webassemblyjs/leb128': registry.npmmirror.com/@webassemblyjs/leb128@1.13.2
      '@webassemblyjs/utf8': registry.npmmirror.com/@webassemblyjs/utf8@1.13.2
    dev: true

  registry.npmmirror.com/@webassemblyjs/wasm-opt@1.14.1:
    resolution: {integrity: sha512-PTcKLUNvBqnY2U6E5bdOQcSM+oVP/PmrDY9NzowJjislEjwP/C4an2303MCVS2Mg9d3AJpIGdUFIQQWbPds0Sw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@webassemblyjs/wasm-opt/-/wasm-opt-1.14.1.tgz}
    name: '@webassemblyjs/wasm-opt'
    version: 1.14.1
    dependencies:
      '@webassemblyjs/ast': registry.npmmirror.com/@webassemblyjs/ast@1.14.1
      '@webassemblyjs/helper-buffer': registry.npmmirror.com/@webassemblyjs/helper-buffer@1.14.1
      '@webassemblyjs/wasm-gen': registry.npmmirror.com/@webassemblyjs/wasm-gen@1.14.1
      '@webassemblyjs/wasm-parser': registry.npmmirror.com/@webassemblyjs/wasm-parser@1.14.1
    dev: true

  registry.npmmirror.com/@webassemblyjs/wasm-parser@1.14.1:
    resolution: {integrity: sha512-JLBl+KZ0R5qB7mCnud/yyX08jWFw5MsoalJ1pQ4EdFlgj9VdXKGuENGsiCIjegI1W7p91rUlcB/LB5yRJKNTcQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@webassemblyjs/wasm-parser/-/wasm-parser-1.14.1.tgz}
    name: '@webassemblyjs/wasm-parser'
    version: 1.14.1
    dependencies:
      '@webassemblyjs/ast': registry.npmmirror.com/@webassemblyjs/ast@1.14.1
      '@webassemblyjs/helper-api-error': registry.npmmirror.com/@webassemblyjs/helper-api-error@1.13.2
      '@webassemblyjs/helper-wasm-bytecode': registry.npmmirror.com/@webassemblyjs/helper-wasm-bytecode@1.13.2
      '@webassemblyjs/ieee754': registry.npmmirror.com/@webassemblyjs/ieee754@1.13.2
      '@webassemblyjs/leb128': registry.npmmirror.com/@webassemblyjs/leb128@1.13.2
      '@webassemblyjs/utf8': registry.npmmirror.com/@webassemblyjs/utf8@1.13.2
    dev: true

  registry.npmmirror.com/@webassemblyjs/wast-printer@1.14.1:
    resolution: {integrity: sha512-kPSSXE6De1XOR820C90RIo2ogvZG+c3KiHzqUoO/F34Y2shGzesfqv7o57xrxovZJH/MetF5UjroJ/R/3isoiw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@webassemblyjs/wast-printer/-/wast-printer-1.14.1.tgz}
    name: '@webassemblyjs/wast-printer'
    version: 1.14.1
    dependencies:
      '@webassemblyjs/ast': registry.npmmirror.com/@webassemblyjs/ast@1.14.1
      '@xtuc/long': registry.npmmirror.com/@xtuc/long@4.2.2
    dev: true

  registry.npmmirror.com/@xhmikosr/archive-type@7.0.0:
    resolution: {integrity: sha512-sIm84ZneCOJuiy3PpWR5bxkx3HaNt1pqaN+vncUBZIlPZCq8ASZH+hBVdu5H8znR7qYC6sKwx+ie2Q7qztJTxA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@xhmikosr/archive-type/-/archive-type-7.0.0.tgz}
    name: '@xhmikosr/archive-type'
    version: 7.0.0
    engines: {node: ^14.14.0 || >=16.0.0}
    dependencies:
      file-type: registry.npmmirror.com/file-type@19.6.0
    dev: true

  registry.npmmirror.com/@xhmikosr/bin-check@7.0.3:
    resolution: {integrity: sha512-4UnCLCs8DB+itHJVkqFp9Zjg+w/205/J2j2wNBsCEAm/BuBmtua2hhUOdAMQE47b1c7P9Xmddj0p+X1XVsfHsA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@xhmikosr/bin-check/-/bin-check-7.0.3.tgz}
    name: '@xhmikosr/bin-check'
    version: 7.0.3
    engines: {node: '>=18'}
    dependencies:
      execa: registry.npmmirror.com/execa@5.1.1
      isexe: registry.npmmirror.com/isexe@2.0.0
    dev: true

  registry.npmmirror.com/@xhmikosr/bin-wrapper@13.0.5:
    resolution: {integrity: sha512-DT2SAuHDeOw0G5bs7wZbQTbf4hd8pJ14tO0i4cWhRkIJfgRdKmMfkDilpaJ8uZyPA0NVRwasCNAmMJcWA67osw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@xhmikosr/bin-wrapper/-/bin-wrapper-13.0.5.tgz}
    name: '@xhmikosr/bin-wrapper'
    version: 13.0.5
    engines: {node: '>=18'}
    dependencies:
      '@xhmikosr/bin-check': registry.npmmirror.com/@xhmikosr/bin-check@7.0.3
      '@xhmikosr/downloader': registry.npmmirror.com/@xhmikosr/downloader@15.0.1
      '@xhmikosr/os-filter-obj': registry.npmmirror.com/@xhmikosr/os-filter-obj@3.0.0
      bin-version-check: registry.npmmirror.com/bin-version-check@5.1.0
    dev: true

  registry.npmmirror.com/@xhmikosr/decompress-tar@8.0.1:
    resolution: {integrity: sha512-dpEgs0cQKJ2xpIaGSO0hrzz3Kt8TQHYdizHsgDtLorWajuHJqxzot9Hbi0huRxJuAGG2qiHSQkwyvHHQtlE+fg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@xhmikosr/decompress-tar/-/decompress-tar-8.0.1.tgz}
    name: '@xhmikosr/decompress-tar'
    version: 8.0.1
    engines: {node: '>=18'}
    dependencies:
      file-type: registry.npmmirror.com/file-type@19.6.0
      is-stream: registry.npmmirror.com/is-stream@2.0.1
      tar-stream: registry.npmmirror.com/tar-stream@3.1.7
    dev: true

  registry.npmmirror.com/@xhmikosr/decompress-tarbz2@8.0.2:
    resolution: {integrity: sha512-p5A2r/AVynTQSsF34Pig6olt9CvRj6J5ikIhzUd3b57pUXyFDGtmBstcw+xXza0QFUh93zJsmY3zGeNDlR2AQQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@xhmikosr/decompress-tarbz2/-/decompress-tarbz2-8.0.2.tgz}
    name: '@xhmikosr/decompress-tarbz2'
    version: 8.0.2
    engines: {node: '>=18'}
    dependencies:
      '@xhmikosr/decompress-tar': registry.npmmirror.com/@xhmikosr/decompress-tar@8.0.1
      file-type: registry.npmmirror.com/file-type@19.6.0
      is-stream: registry.npmmirror.com/is-stream@2.0.1
      seek-bzip: registry.npmmirror.com/seek-bzip@2.0.0
      unbzip2-stream: registry.npmmirror.com/unbzip2-stream@1.4.3
    dev: true

  registry.npmmirror.com/@xhmikosr/decompress-targz@8.0.1:
    resolution: {integrity: sha512-mvy5AIDIZjQ2IagMI/wvauEiSNHhu/g65qpdM4EVoYHUJBAmkQWqcPJa8Xzi1aKVTmOA5xLJeDk7dqSjlHq8Mg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@xhmikosr/decompress-targz/-/decompress-targz-8.0.1.tgz}
    name: '@xhmikosr/decompress-targz'
    version: 8.0.1
    engines: {node: '>=18'}
    dependencies:
      '@xhmikosr/decompress-tar': registry.npmmirror.com/@xhmikosr/decompress-tar@8.0.1
      file-type: registry.npmmirror.com/file-type@19.6.0
      is-stream: registry.npmmirror.com/is-stream@2.0.1
    dev: true

  registry.npmmirror.com/@xhmikosr/decompress-unzip@7.0.0:
    resolution: {integrity: sha512-GQMpzIpWTsNr6UZbISawsGI0hJ4KA/mz5nFq+cEoPs12UybAqZWKbyIaZZyLbJebKl5FkLpsGBkrplJdjvUoSQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@xhmikosr/decompress-unzip/-/decompress-unzip-7.0.0.tgz}
    name: '@xhmikosr/decompress-unzip'
    version: 7.0.0
    engines: {node: '>=18'}
    dependencies:
      file-type: registry.npmmirror.com/file-type@19.6.0
      get-stream: registry.npmmirror.com/get-stream@6.0.1
      yauzl: registry.npmmirror.com/yauzl@3.2.0
    dev: true

  registry.npmmirror.com/@xhmikosr/decompress@10.0.1:
    resolution: {integrity: sha512-6uHnEEt5jv9ro0CDzqWlFgPycdE+H+kbJnwyxgZregIMLQ7unQSCNVsYG255FoqU8cP46DyggI7F7LohzEl8Ag==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@xhmikosr/decompress/-/decompress-10.0.1.tgz}
    name: '@xhmikosr/decompress'
    version: 10.0.1
    engines: {node: '>=18'}
    dependencies:
      '@xhmikosr/decompress-tar': registry.npmmirror.com/@xhmikosr/decompress-tar@8.0.1
      '@xhmikosr/decompress-tarbz2': registry.npmmirror.com/@xhmikosr/decompress-tarbz2@8.0.2
      '@xhmikosr/decompress-targz': registry.npmmirror.com/@xhmikosr/decompress-targz@8.0.1
      '@xhmikosr/decompress-unzip': registry.npmmirror.com/@xhmikosr/decompress-unzip@7.0.0
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
      make-dir: registry.npmmirror.com/make-dir@4.0.0
      strip-dirs: registry.npmmirror.com/strip-dirs@3.0.0
    dev: true

  registry.npmmirror.com/@xhmikosr/downloader@15.0.1:
    resolution: {integrity: sha512-fiuFHf3Dt6pkX8HQrVBsK0uXtkgkVlhrZEh8b7VgoDqFf+zrgFBPyrwCqE/3nDwn3hLeNz+BsrS7q3mu13Lp1g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@xhmikosr/downloader/-/downloader-15.0.1.tgz}
    name: '@xhmikosr/downloader'
    version: 15.0.1
    engines: {node: '>=18'}
    dependencies:
      '@xhmikosr/archive-type': registry.npmmirror.com/@xhmikosr/archive-type@7.0.0
      '@xhmikosr/decompress': registry.npmmirror.com/@xhmikosr/decompress@10.0.1
      content-disposition: registry.npmmirror.com/content-disposition@0.5.4
      defaults: registry.npmmirror.com/defaults@3.0.0
      ext-name: registry.npmmirror.com/ext-name@5.0.0
      file-type: registry.npmmirror.com/file-type@19.6.0
      filenamify: registry.npmmirror.com/filenamify@6.0.0
      get-stream: registry.npmmirror.com/get-stream@6.0.1
      got: registry.npmmirror.com/got@13.0.0
    dev: true

  registry.npmmirror.com/@xhmikosr/os-filter-obj@3.0.0:
    resolution: {integrity: sha512-siPY6BD5dQ2SZPl3I0OZBHL27ZqZvLEosObsZRQ1NUB8qcxegwt0T9eKtV96JMFQpIz1elhkzqOg4c/Ri6Dp9A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@xhmikosr/os-filter-obj/-/os-filter-obj-3.0.0.tgz}
    name: '@xhmikosr/os-filter-obj'
    version: 3.0.0
    engines: {node: ^14.14.0 || >=16.0.0}
    dependencies:
      arch: registry.npmmirror.com/arch@3.0.0
    dev: true

  registry.npmmirror.com/@xtuc/ieee754@1.2.0:
    resolution: {integrity: sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@xtuc/ieee754/-/ieee754-1.2.0.tgz}
    name: '@xtuc/ieee754'
    version: 1.2.0
    dev: true

  registry.npmmirror.com/@xtuc/long@4.2.2:
    resolution: {integrity: sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@xtuc/long/-/long-4.2.2.tgz}
    name: '@xtuc/long'
    version: 4.2.2
    dev: true

  registry.npmmirror.com/accepts@2.0.0:
    resolution: {integrity: sha512-5cvg6CtKwfgdmVqY1WIiXKc3Q1bkRqGLi+2W/6ao+6Y7gu/RCwRuAhGEzh5B4KlszSuTLgZYuqFqo5bImjNKng==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/accepts/-/accepts-2.0.0.tgz}
    name: accepts
    version: 2.0.0
    engines: {node: '>= 0.6'}
    dependencies:
      mime-types: registry.npmmirror.com/mime-types@3.0.1
      negotiator: registry.npmmirror.com/negotiator@1.0.0

  registry.npmmirror.com/acorn-jsx@5.3.2(acorn@8.15.0):
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/acorn-jsx/-/acorn-jsx-5.3.2.tgz}
    id: registry.npmmirror.com/acorn-jsx/5.3.2
    name: acorn-jsx
    version: 5.3.2
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
    dependencies:
      acorn: registry.npmmirror.com/acorn@8.15.0
    dev: true

  registry.npmmirror.com/acorn-walk@8.3.4:
    resolution: {integrity: sha512-ueEepnujpqee2o5aIYnvHU6C0A42MNdsIDeqy5BydrkuC5R1ZuUFnm27EeFJGoEHJQgn3uleRvmTXaJgfXbt4g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/acorn-walk/-/acorn-walk-8.3.4.tgz}
    name: acorn-walk
    version: 8.3.4
    engines: {node: '>=0.4.0'}
    dependencies:
      acorn: registry.npmmirror.com/acorn@8.15.0

  registry.npmmirror.com/acorn@8.15.0:
    resolution: {integrity: sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/acorn/-/acorn-8.15.0.tgz}
    name: acorn
    version: 8.15.0
    engines: {node: '>=0.4.0'}
    hasBin: true

  registry.npmmirror.com/ajv-formats@2.1.1(ajv@8.17.1):
    resolution: {integrity: sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ajv-formats/-/ajv-formats-2.1.1.tgz}
    id: registry.npmmirror.com/ajv-formats/2.1.1
    name: ajv-formats
    version: 2.1.1
    peerDependencies:
      ajv: ^8.0.0
    peerDependenciesMeta:
      ajv:
        optional: true
    dependencies:
      ajv: registry.npmmirror.com/ajv@8.17.1
    dev: true

  registry.npmmirror.com/ajv-formats@3.0.1(ajv@8.17.1):
    resolution: {integrity: sha512-8iUql50EUR+uUcdRQ3HDqa6EVyo3docL8g5WJ3FNcWmu62IbkGUue/pEyLBW8VGKKucTPgqeks4fIU1DA4yowQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ajv-formats/-/ajv-formats-3.0.1.tgz}
    id: registry.npmmirror.com/ajv-formats/3.0.1
    name: ajv-formats
    version: 3.0.1
    peerDependencies:
      ajv: ^8.0.0
    peerDependenciesMeta:
      ajv:
        optional: true
    dependencies:
      ajv: registry.npmmirror.com/ajv@8.17.1
    dev: true

  registry.npmmirror.com/ajv-keywords@3.5.2(ajv@6.12.6):
    resolution: {integrity: sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ajv-keywords/-/ajv-keywords-3.5.2.tgz}
    id: registry.npmmirror.com/ajv-keywords/3.5.2
    name: ajv-keywords
    version: 3.5.2
    peerDependencies:
      ajv: ^6.9.1
    dependencies:
      ajv: registry.npmmirror.com/ajv@6.12.6
    dev: true

  registry.npmmirror.com/ajv-keywords@5.1.0(ajv@8.17.1):
    resolution: {integrity: sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ajv-keywords/-/ajv-keywords-5.1.0.tgz}
    id: registry.npmmirror.com/ajv-keywords/5.1.0
    name: ajv-keywords
    version: 5.1.0
    peerDependencies:
      ajv: ^8.8.2
    dependencies:
      ajv: registry.npmmirror.com/ajv@8.17.1
      fast-deep-equal: registry.npmmirror.com/fast-deep-equal@3.1.3
    dev: true

  registry.npmmirror.com/ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz}
    name: ajv
    version: 6.12.6
    dependencies:
      fast-deep-equal: registry.npmmirror.com/fast-deep-equal@3.1.3
      fast-json-stable-stringify: registry.npmmirror.com/fast-json-stable-stringify@2.1.0
      json-schema-traverse: registry.npmmirror.com/json-schema-traverse@0.4.1
      uri-js: registry.npmmirror.com/uri-js@4.4.1
    dev: true

  registry.npmmirror.com/ajv@8.17.1:
    resolution: {integrity: sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ajv/-/ajv-8.17.1.tgz}
    name: ajv
    version: 8.17.1
    dependencies:
      fast-deep-equal: registry.npmmirror.com/fast-deep-equal@3.1.3
      fast-uri: registry.npmmirror.com/fast-uri@3.0.6
      json-schema-traverse: registry.npmmirror.com/json-schema-traverse@1.0.0
      require-from-string: registry.npmmirror.com/require-from-string@2.0.2
    dev: true

  registry.npmmirror.com/alipay-sdk@4.14.0:
    resolution: {integrity: sha512-oiD/VP5Ei0RRacHHmE+N0uqgOj2xzce7c0fHrtyyh1P04O+o9I1r65LdGPzU8960J56xOxS/d3c+R/9lsPUH7g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/alipay-sdk/-/alipay-sdk-4.14.0.tgz}
    name: alipay-sdk
    version: 4.14.0
    engines: {node: '>=18.0.0'}
    dependencies:
      '@fidm/x509': registry.npmmirror.com/@fidm/x509@1.2.1
      bignumber.js: registry.npmmirror.com/bignumber.js@9.3.0
      camelcase-keys: registry.npmmirror.com/camelcase-keys@7.0.2
      crypto-js: registry.npmmirror.com/crypto-js@4.2.0
      formstream: registry.npmmirror.com/formstream@1.5.1
      snakecase-keys: registry.npmmirror.com/snakecase-keys@8.0.1
      sse-decoder: registry.npmmirror.com/sse-decoder@1.0.0
      urllib: registry.npmmirror.com/urllib@4.6.12
      utility: registry.npmmirror.com/utility@2.5.0
    dev: false

  registry.npmmirror.com/ansi-colors@4.1.3:
    resolution: {integrity: sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ansi-colors/-/ansi-colors-4.1.3.tgz}
    name: ansi-colors
    version: 4.1.3
    engines: {node: '>=6'}
    dev: true

  registry.npmmirror.com/ansi-escapes@4.3.2:
    resolution: {integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ansi-escapes/-/ansi-escapes-4.3.2.tgz}
    name: ansi-escapes
    version: 4.3.2
    engines: {node: '>=8'}
    dependencies:
      type-fest: registry.npmmirror.com/type-fest@0.21.3
    dev: true

  registry.npmmirror.com/ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz}
    name: ansi-regex
    version: 5.0.1
    engines: {node: '>=8'}

  registry.npmmirror.com/ansi-regex@6.1.0:
    resolution: {integrity: sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ansi-regex/-/ansi-regex-6.1.0.tgz}
    name: ansi-regex
    version: 6.1.0
    engines: {node: '>=12'}

  registry.npmmirror.com/ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz}
    name: ansi-styles
    version: 4.3.0
    engines: {node: '>=8'}
    dependencies:
      color-convert: registry.npmmirror.com/color-convert@2.0.1

  registry.npmmirror.com/ansi-styles@5.2.0:
    resolution: {integrity: sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ansi-styles/-/ansi-styles-5.2.0.tgz}
    name: ansi-styles
    version: 5.2.0
    engines: {node: '>=10'}
    dev: true

  registry.npmmirror.com/ansi-styles@6.2.1:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ansi-styles/-/ansi-styles-6.2.1.tgz}
    name: ansi-styles
    version: 6.2.1
    engines: {node: '>=12'}

  registry.npmmirror.com/ansis@3.17.0:
    resolution: {integrity: sha512-0qWUglt9JEqLFr3w1I1pbrChn1grhaiAR2ocX1PP/flRmxgtwTzPFFFnfIlD6aMOLQZgSuCRlidD70lvx8yhzg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ansis/-/ansis-3.17.0.tgz}
    name: ansis
    version: 3.17.0
    engines: {node: '>=14'}
    dev: false

  registry.npmmirror.com/ansis@3.9.0:
    resolution: {integrity: sha512-PcDrVe15ldexeZMsVLBAzBwF2KhZgaU0R+CHxH+x5kqn/pO+UWVBZJ+NEXMPpEOLUFeNsnNdoWYc2gwO+MVkDg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ansis/-/ansis-3.9.0.tgz}
    name: ansis
    version: 3.9.0
    engines: {node: '>=16'}
    dev: true

  registry.npmmirror.com/anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/anymatch/-/anymatch-3.1.3.tgz}
    name: anymatch
    version: 3.1.3
    engines: {node: '>= 8'}
    dependencies:
      normalize-path: registry.npmmirror.com/normalize-path@3.0.0
      picomatch: registry.npmmirror.com/picomatch@2.3.1
    dev: true

  registry.npmmirror.com/app-root-path@3.1.0:
    resolution: {integrity: sha512-biN3PwB2gUtjaYy/isrU3aNWI5w+fAfvHkSvCKeQGxhmYpwKFUxudR3Yya+KqVRHBmEDYh+/lTozYCFbmzX4nA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/app-root-path/-/app-root-path-3.1.0.tgz}
    name: app-root-path
    version: 3.1.0
    engines: {node: '>= 6.0.0'}
    dev: false

  registry.npmmirror.com/append-field@1.0.0:
    resolution: {integrity: sha512-klpgFSWLW1ZEs8svjfb7g4qWY0YS5imI82dTg+QahUvJ8YqAY0P10Uk8tTyh9ZGuYEZEMaeJYCF5BFuX552hsw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/append-field/-/append-field-1.0.0.tgz}
    name: append-field
    version: 1.0.0

  registry.npmmirror.com/arch@3.0.0:
    resolution: {integrity: sha512-AmIAC+Wtm2AU8lGfTtHsw0Y9Qtftx2YXEEtiBP10xFUtMOA+sHHx6OAddyL52mUKh1vsXQ6/w1mVDptZCyUt4Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/arch/-/arch-3.0.0.tgz}
    name: arch
    version: 3.0.0
    dev: true

  registry.npmmirror.com/arg@4.1.3:
    resolution: {integrity: sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/arg/-/arg-4.1.3.tgz}
    name: arg
    version: 4.1.3

  registry.npmmirror.com/argparse@1.0.10:
    resolution: {integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/argparse/-/argparse-1.0.10.tgz}
    name: argparse
    version: 1.0.10
    dependencies:
      sprintf-js: registry.npmmirror.com/sprintf-js@1.0.3
    dev: true

  registry.npmmirror.com/argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/argparse/-/argparse-2.0.1.tgz}
    name: argparse
    version: 2.0.1

  registry.npmmirror.com/array-timsort@1.0.3:
    resolution: {integrity: sha512-/+3GRL7dDAGEfM6TseQk/U+mi18TU2Ms9I3UlLdUMhz2hbvGNTKdj9xniwXfUqgYhHxRx0+8UnKkvlNwVU+cWQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/array-timsort/-/array-timsort-1.0.3.tgz}
    name: array-timsort
    version: 1.0.3
    dev: true

  registry.npmmirror.com/asap@2.0.6:
    resolution: {integrity: sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/asap/-/asap-2.0.6.tgz}
    name: asap
    version: 2.0.6
    dev: true

  registry.npmmirror.com/async@3.2.6:
    resolution: {integrity: sha512-htCUDlxyyCLMgaM3xXg0C0LW2xqfuQ6p05pCEIsXuyQ+a1koYKTuBMzRNwmybfLgvJDMd0r1LTn4+E0Ti6C2AA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/async/-/async-3.2.6.tgz}
    name: async
    version: 3.2.6
    dev: true

  registry.npmmirror.com/asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/asynckit/-/asynckit-0.4.0.tgz}
    name: asynckit
    version: 0.4.0

  registry.npmmirror.com/aws-ssl-profiles@1.1.2:
    resolution: {integrity: sha512-NZKeq9AfyQvEeNlN0zSYAaWrmBffJh3IELMZfRpJVWgrpEbtEpnjvzqBPf+mxoI287JohRDoa+/nsfqqiZmF6g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/aws-ssl-profiles/-/aws-ssl-profiles-1.1.2.tgz}
    name: aws-ssl-profiles
    version: 1.1.2
    engines: {node: '>= 6.0.0'}
    dev: false

  registry.npmmirror.com/b4a@1.6.7:
    resolution: {integrity: sha512-OnAYlL5b7LEkALw87fUVafQw5rVR9RjwGd4KUwNQ6DrrNmaVaUCgLipfVlzrPQ4tWOR9P0IXGNOx50jYCCdSJg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/b4a/-/b4a-1.6.7.tgz}
    name: b4a
    version: 1.6.7
    dev: true

  registry.npmmirror.com/babel-jest@29.7.0(@babel/core@7.27.7):
    resolution: {integrity: sha512-BrvGY3xZSwEcCzKvKsCi2GgHqDqsYkOP4/by5xCgIwGXQxIEh+8ew3gmrE1y7XRR6LHZIj6yLYnUi/mm2KXKBg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/babel-jest/-/babel-jest-29.7.0.tgz}
    id: registry.npmmirror.com/babel-jest/29.7.0
    name: babel-jest
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      '@babel/core': ^7.8.0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@jest/transform': registry.npmmirror.com/@jest/transform@29.7.0
      '@types/babel__core': registry.npmmirror.com/@types/babel__core@7.20.5
      babel-plugin-istanbul: registry.npmmirror.com/babel-plugin-istanbul@6.1.1
      babel-preset-jest: registry.npmmirror.com/babel-preset-jest@29.6.3(@babel/core@7.27.7)
      chalk: registry.npmmirror.com/chalk@4.1.2
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
      slash: registry.npmmirror.com/slash@3.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/babel-plugin-istanbul@6.1.1:
    resolution: {integrity: sha512-Y1IQok9821cC9onCx5otgFfRm7Lm+I+wwxOx738M/WLPZ9Q42m4IG5W0FNX8WLL2gYMZo3JkuXIH2DOpWM+qwA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz}
    name: babel-plugin-istanbul
    version: 6.1.1
    engines: {node: '>=8'}
    dependencies:
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
      '@istanbuljs/load-nyc-config': registry.npmmirror.com/@istanbuljs/load-nyc-config@1.1.0
      '@istanbuljs/schema': registry.npmmirror.com/@istanbuljs/schema@0.1.3
      istanbul-lib-instrument: registry.npmmirror.com/istanbul-lib-instrument@5.2.1
      test-exclude: registry.npmmirror.com/test-exclude@6.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/babel-plugin-jest-hoist@29.6.3:
    resolution: {integrity: sha512-ESAc/RJvGTFEzRwOTT4+lNDk/GNHMkKbNzsvT0qKRfDyyYTskxB5rnU2njIDYVxXCBHHEI1c0YwHob3WaYujOg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.6.3.tgz}
    name: babel-plugin-jest-hoist
    version: 29.6.3
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@babel/template': registry.npmmirror.com/@babel/template@7.27.2
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.7
      '@types/babel__core': registry.npmmirror.com/@types/babel__core@7.20.5
      '@types/babel__traverse': registry.npmmirror.com/@types/babel__traverse@7.20.7
    dev: true

  registry.npmmirror.com/babel-preset-current-node-syntax@1.1.0(@babel/core@7.27.7):
    resolution: {integrity: sha512-ldYss8SbBlWva1bs28q78Ju5Zq1F+8BrqBZZ0VFhLBvhh6lCpC2o3gDJi/5DRLs9FgYZCnmPYIVFU4lRXCkyUw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.1.0.tgz}
    id: registry.npmmirror.com/babel-preset-current-node-syntax/1.1.0
    name: babel-preset-current-node-syntax
    version: 1.1.0
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@babel/plugin-syntax-async-generators': registry.npmmirror.com/@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.27.7)
      '@babel/plugin-syntax-bigint': registry.npmmirror.com/@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.27.7)
      '@babel/plugin-syntax-class-properties': registry.npmmirror.com/@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.27.7)
      '@babel/plugin-syntax-class-static-block': registry.npmmirror.com/@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.27.7)
      '@babel/plugin-syntax-import-attributes': registry.npmmirror.com/@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.27.7)
      '@babel/plugin-syntax-import-meta': registry.npmmirror.com/@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.27.7)
      '@babel/plugin-syntax-json-strings': registry.npmmirror.com/@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.27.7)
      '@babel/plugin-syntax-logical-assignment-operators': registry.npmmirror.com/@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.27.7)
      '@babel/plugin-syntax-nullish-coalescing-operator': registry.npmmirror.com/@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.27.7)
      '@babel/plugin-syntax-numeric-separator': registry.npmmirror.com/@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.27.7)
      '@babel/plugin-syntax-object-rest-spread': registry.npmmirror.com/@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.27.7)
      '@babel/plugin-syntax-optional-catch-binding': registry.npmmirror.com/@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.27.7)
      '@babel/plugin-syntax-optional-chaining': registry.npmmirror.com/@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.27.7)
      '@babel/plugin-syntax-private-property-in-object': registry.npmmirror.com/@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.27.7)
      '@babel/plugin-syntax-top-level-await': registry.npmmirror.com/@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.27.7)
    dev: true

  registry.npmmirror.com/babel-preset-jest@29.6.3(@babel/core@7.27.7):
    resolution: {integrity: sha512-0B3bhxR6snWXJZtR/RliHTDPRgn1sNHOR0yVtq/IiQFyuOVjFS+wuio/R4gSNkyYmKmJB4wGZv2NZanmKmTnNA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/babel-preset-jest/-/babel-preset-jest-29.6.3.tgz}
    id: registry.npmmirror.com/babel-preset-jest/29.6.3
    name: babel-preset-jest
    version: 29.6.3
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      babel-plugin-jest-hoist: registry.npmmirror.com/babel-plugin-jest-hoist@29.6.3
      babel-preset-current-node-syntax: registry.npmmirror.com/babel-preset-current-node-syntax@1.1.0(@babel/core@7.27.7)
    dev: true

  registry.npmmirror.com/balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/balanced-match/-/balanced-match-1.0.2.tgz}
    name: balanced-match
    version: 1.0.2

  registry.npmmirror.com/bare-events@2.5.4:
    resolution: {integrity: sha512-+gFfDkR8pj4/TrWCGUGWmJIkBwuxPS5F+a5yWjOHQt2hHvNZd5YLzadjmDUtFmMM4y429bnKLa8bYBMHcYdnQA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/bare-events/-/bare-events-2.5.4.tgz}
    name: bare-events
    version: 2.5.4
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/base64-js@1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/base64-js/-/base64-js-1.5.1.tgz}
    name: base64-js
    version: 1.5.1

  registry.npmmirror.com/bcryptjs@3.0.2:
    resolution: {integrity: sha512-k38b3XOZKv60C4E2hVsXTolJWfkGRMbILBIe2IBITXciy5bOsTKot5kDrf3ZfufQtQOUN5mXceUEpU1rTl9Uog==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/bcryptjs/-/bcryptjs-3.0.2.tgz}
    name: bcryptjs
    version: 3.0.2
    hasBin: true

  registry.npmmirror.com/bignumber.js@9.3.0:
    resolution: {integrity: sha512-EM7aMFTXbptt/wZdMlBv2t8IViwQL+h6SLHosp8Yf0dqJMTnY6iL32opnAB6kAdL0SZPuvcAzFr31o0c/R3/RA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/bignumber.js/-/bignumber.js-9.3.0.tgz}
    name: bignumber.js
    version: 9.3.0
    dev: false

  registry.npmmirror.com/bin-version-check@5.1.0:
    resolution: {integrity: sha512-bYsvMqJ8yNGILLz1KP9zKLzQ6YpljV3ln1gqhuLkUtyfGi3qXKGuK2p+U4NAvjVFzDFiBBtOpCOSFNuYYEGZ5g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/bin-version-check/-/bin-version-check-5.1.0.tgz}
    name: bin-version-check
    version: 5.1.0
    engines: {node: '>=12'}
    dependencies:
      bin-version: registry.npmmirror.com/bin-version@6.0.0
      semver: registry.npmmirror.com/semver@7.7.2
      semver-truncate: registry.npmmirror.com/semver-truncate@3.0.0
    dev: true

  registry.npmmirror.com/bin-version@6.0.0:
    resolution: {integrity: sha512-nk5wEsP4RiKjG+vF+uG8lFsEn4d7Y6FVDamzzftSunXOoOcOOkzcWdKVlGgFFwlUQCj63SgnUkLLGF8v7lufhw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/bin-version/-/bin-version-6.0.0.tgz}
    name: bin-version
    version: 6.0.0
    engines: {node: '>=12'}
    dependencies:
      execa: registry.npmmirror.com/execa@5.1.1
      find-versions: registry.npmmirror.com/find-versions@5.1.0
    dev: true

  registry.npmmirror.com/binary-extensions@2.3.0:
    resolution: {integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/binary-extensions/-/binary-extensions-2.3.0.tgz}
    name: binary-extensions
    version: 2.3.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/bl@4.1.0:
    resolution: {integrity: sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/bl/-/bl-4.1.0.tgz}
    name: bl
    version: 4.1.0
    dependencies:
      buffer: registry.npmmirror.com/buffer@5.7.1
      inherits: registry.npmmirror.com/inherits@2.0.4
      readable-stream: registry.npmmirror.com/readable-stream@3.6.2
    dev: true

  registry.npmmirror.com/body-parser@1.20.3:
    resolution: {integrity: sha512-7rAxByjUMqQ3/bHJy7D6OGXvx/MMc4IqBn/X0fcM1QUcAItpZrBEYhWGem+tzXH90c+G01ypMcYJBO9Y30203g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/body-parser/-/body-parser-1.20.3.tgz}
    name: body-parser
    version: 1.20.3
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}
    dependencies:
      bytes: registry.npmmirror.com/bytes@3.1.2
      content-type: registry.npmmirror.com/content-type@1.0.5
      debug: registry.npmmirror.com/debug@2.6.9
      depd: registry.npmmirror.com/depd@2.0.0
      destroy: registry.npmmirror.com/destroy@1.2.0
      http-errors: registry.npmmirror.com/http-errors@2.0.0
      iconv-lite: registry.npmmirror.com/iconv-lite@0.4.24
      on-finished: registry.npmmirror.com/on-finished@2.4.1
      qs: registry.npmmirror.com/qs@6.13.0
      raw-body: registry.npmmirror.com/raw-body@2.5.2
      type-is: registry.npmmirror.com/type-is@1.6.18
      unpipe: registry.npmmirror.com/unpipe@1.0.0
    transitivePeerDependencies:
      - supports-color

  registry.npmmirror.com/body-parser@2.2.0:
    resolution: {integrity: sha512-02qvAaxv8tp7fBa/mw1ga98OGm+eCbqzJOKoRt70sLmfEEi+jyBYVTDGfCL/k06/4EMk/z01gCe7HoCH/f2LTg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/body-parser/-/body-parser-2.2.0.tgz}
    name: body-parser
    version: 2.2.0
    engines: {node: '>=18'}
    dependencies:
      bytes: registry.npmmirror.com/bytes@3.1.2
      content-type: registry.npmmirror.com/content-type@1.0.5
      debug: registry.npmmirror.com/debug@4.4.1
      http-errors: registry.npmmirror.com/http-errors@2.0.0
      iconv-lite: registry.npmmirror.com/iconv-lite@0.6.3
      on-finished: registry.npmmirror.com/on-finished@2.4.1
      qs: registry.npmmirror.com/qs@6.14.0
      raw-body: registry.npmmirror.com/raw-body@3.0.0
      type-is: registry.npmmirror.com/type-is@2.0.1
    transitivePeerDependencies:
      - supports-color

  registry.npmmirror.com/brace-expansion@1.1.12:
    resolution: {integrity: sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.12.tgz}
    name: brace-expansion
    version: 1.1.12
    dependencies:
      balanced-match: registry.npmmirror.com/balanced-match@1.0.2
      concat-map: registry.npmmirror.com/concat-map@0.0.1
    dev: true

  registry.npmmirror.com/brace-expansion@2.0.2:
    resolution: {integrity: sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/brace-expansion/-/brace-expansion-2.0.2.tgz}
    name: brace-expansion
    version: 2.0.2
    dependencies:
      balanced-match: registry.npmmirror.com/balanced-match@1.0.2

  registry.npmmirror.com/braces@3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/braces/-/braces-3.0.3.tgz}
    name: braces
    version: 3.0.3
    engines: {node: '>=8'}
    dependencies:
      fill-range: registry.npmmirror.com/fill-range@7.1.1
    dev: true

  registry.npmmirror.com/browserslist@4.25.1:
    resolution: {integrity: sha512-KGj0KoOMXLpSNkkEI6Z6mShmQy0bc1I+T7K9N81k4WWMrfz+6fQ6es80B/YLAeRoKvjYE1YSHHOW1qe9xIVzHw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/browserslist/-/browserslist-4.25.1.tgz}
    name: browserslist
    version: 4.25.1
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      caniuse-lite: registry.npmmirror.com/caniuse-lite@1.0.30001726
      electron-to-chromium: registry.npmmirror.com/electron-to-chromium@1.5.177
      node-releases: registry.npmmirror.com/node-releases@2.0.19
      update-browserslist-db: registry.npmmirror.com/update-browserslist-db@1.1.3(browserslist@4.25.1)
    dev: true

  registry.npmmirror.com/bs-logger@0.2.6:
    resolution: {integrity: sha512-pd8DCoxmbgc7hyPKOvxtqNcjYoOsABPQdcCUjGp3d42VR2CX1ORhk2A87oqqu5R1kk+76nsxZupkmyd+MVtCog==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/bs-logger/-/bs-logger-0.2.6.tgz}
    name: bs-logger
    version: 0.2.6
    engines: {node: '>= 6'}
    dependencies:
      fast-json-stable-stringify: registry.npmmirror.com/fast-json-stable-stringify@2.1.0
    dev: true

  registry.npmmirror.com/bser@2.1.1:
    resolution: {integrity: sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/bser/-/bser-2.1.1.tgz}
    name: bser
    version: 2.1.1
    dependencies:
      node-int64: registry.npmmirror.com/node-int64@0.4.0
    dev: true

  registry.npmmirror.com/buffer-crc32@0.2.13:
    resolution: {integrity: sha512-VO9Ht/+p3SN7SKWqcrgEzjGbRSJYTx+Q1pTQC0wrWqHx0vpJraQ6GtHx8tvcg1rlK1byhU5gccxgOgj7B0TDkQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/buffer-crc32/-/buffer-crc32-0.2.13.tgz}
    name: buffer-crc32
    version: 0.2.13
    dev: true

  registry.npmmirror.com/buffer-equal-constant-time@1.0.1:
    resolution: {integrity: sha512-zRpUiDwd/xk6ADqPMATG8vc9VPrkck7T07OIx0gnjmJAnHnTVXNQG3vfvWNuiZIkwu9KrKdA1iJKfsfTVxE6NA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz}
    name: buffer-equal-constant-time
    version: 1.0.1
    dev: false

  registry.npmmirror.com/buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/buffer-from/-/buffer-from-1.1.2.tgz}
    name: buffer-from
    version: 1.1.2

  registry.npmmirror.com/buffer@5.7.1:
    resolution: {integrity: sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/buffer/-/buffer-5.7.1.tgz}
    name: buffer
    version: 5.7.1
    dependencies:
      base64-js: registry.npmmirror.com/base64-js@1.5.1
      ieee754: registry.npmmirror.com/ieee754@1.2.1
    dev: true

  registry.npmmirror.com/buffer@6.0.3:
    resolution: {integrity: sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/buffer/-/buffer-6.0.3.tgz}
    name: buffer
    version: 6.0.3
    dependencies:
      base64-js: registry.npmmirror.com/base64-js@1.5.1
      ieee754: registry.npmmirror.com/ieee754@1.2.1
    dev: false

  registry.npmmirror.com/busboy@1.6.0:
    resolution: {integrity: sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/busboy/-/busboy-1.6.0.tgz}
    name: busboy
    version: 1.6.0
    engines: {node: '>=10.16.0'}
    dependencies:
      streamsearch: registry.npmmirror.com/streamsearch@1.1.0

  registry.npmmirror.com/bytes@3.1.2:
    resolution: {integrity: sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/bytes/-/bytes-3.1.2.tgz}
    name: bytes
    version: 3.1.2
    engines: {node: '>= 0.8'}

  registry.npmmirror.com/cacheable-lookup@7.0.0:
    resolution: {integrity: sha512-+qJyx4xiKra8mZrcwhjMRMUhD5NR1R8esPkzIYxX96JiecFoxAXFuz/GpR3+ev4PE1WamHip78wV0vcmPQtp8w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/cacheable-lookup/-/cacheable-lookup-7.0.0.tgz}
    name: cacheable-lookup
    version: 7.0.0
    engines: {node: '>=14.16'}
    dev: true

  registry.npmmirror.com/cacheable-request@10.2.14:
    resolution: {integrity: sha512-zkDT5WAF4hSSoUgyfg5tFIxz8XQK+25W/TLVojJTMKBaxevLBBtLxgqguAuVQB8PVW79FVjHcU+GJ9tVbDZ9mQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/cacheable-request/-/cacheable-request-10.2.14.tgz}
    name: cacheable-request
    version: 10.2.14
    engines: {node: '>=14.16'}
    dependencies:
      '@types/http-cache-semantics': registry.npmmirror.com/@types/http-cache-semantics@4.0.4
      get-stream: registry.npmmirror.com/get-stream@6.0.1
      http-cache-semantics: registry.npmmirror.com/http-cache-semantics@4.2.0
      keyv: registry.npmmirror.com/keyv@4.5.4
      mimic-response: registry.npmmirror.com/mimic-response@4.0.0
      normalize-url: registry.npmmirror.com/normalize-url@8.0.2
      responselike: registry.npmmirror.com/responselike@3.0.0
    dev: true

  registry.npmmirror.com/call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz}
    name: call-bind-apply-helpers
    version: 1.0.2
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: registry.npmmirror.com/es-errors@1.3.0
      function-bind: registry.npmmirror.com/function-bind@1.1.2

  registry.npmmirror.com/call-bound@1.0.4:
    resolution: {integrity: sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/call-bound/-/call-bound-1.0.4.tgz}
    name: call-bound
    version: 1.0.4
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: registry.npmmirror.com/call-bind-apply-helpers@1.0.2
      get-intrinsic: registry.npmmirror.com/get-intrinsic@1.3.0

  registry.npmmirror.com/callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/callsites/-/callsites-3.1.0.tgz}
    name: callsites
    version: 3.1.0
    engines: {node: '>=6'}
    dev: true

  registry.npmmirror.com/camelcase-keys@7.0.2:
    resolution: {integrity: sha512-Rjs1H+A9R+Ig+4E/9oyB66UC5Mj9Xq3N//vcLf2WzgdTi/3gUu3Z9KoqmlrEG4VuuLK8wJHofxzdQXz/knhiYg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/camelcase-keys/-/camelcase-keys-7.0.2.tgz}
    name: camelcase-keys
    version: 7.0.2
    engines: {node: '>=12'}
    dependencies:
      camelcase: registry.npmmirror.com/camelcase@6.3.0
      map-obj: registry.npmmirror.com/map-obj@4.3.0
      quick-lru: registry.npmmirror.com/quick-lru@5.1.1
      type-fest: registry.npmmirror.com/type-fest@1.4.0
    dev: false

  registry.npmmirror.com/camelcase@5.3.1:
    resolution: {integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/camelcase/-/camelcase-5.3.1.tgz}
    name: camelcase
    version: 5.3.1
    engines: {node: '>=6'}
    dev: true

  registry.npmmirror.com/camelcase@6.3.0:
    resolution: {integrity: sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/camelcase/-/camelcase-6.3.0.tgz}
    name: camelcase
    version: 6.3.0
    engines: {node: '>=10'}

  registry.npmmirror.com/caniuse-lite@1.0.30001726:
    resolution: {integrity: sha512-VQAUIUzBiZ/UnlM28fSp2CRF3ivUn1BWEvxMcVTNwpw91Py1pGbPIyIKtd+tzct9C3ouceCVdGAXxZOpZAsgdw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/caniuse-lite/-/caniuse-lite-1.0.30001726.tgz}
    name: caniuse-lite
    version: 1.0.30001726
    dev: true

  registry.npmmirror.com/chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz}
    name: chalk
    version: 4.1.2
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: registry.npmmirror.com/ansi-styles@4.3.0
      supports-color: registry.npmmirror.com/supports-color@7.2.0
    dev: true

  registry.npmmirror.com/char-regex@1.0.2:
    resolution: {integrity: sha512-kWWXztvZ5SBQV+eRgKFeh8q5sLuZY2+8WUIzlxWVTg+oGwY14qylx1KbKzHd8P6ZYkAg0xyIDU9JMHhyJMZ1jw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/char-regex/-/char-regex-1.0.2.tgz}
    name: char-regex
    version: 1.0.2
    engines: {node: '>=10'}
    dev: true

  registry.npmmirror.com/chardet@0.7.0:
    resolution: {integrity: sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/chardet/-/chardet-0.7.0.tgz}
    name: chardet
    version: 0.7.0
    dev: true

  registry.npmmirror.com/chokidar@3.6.0:
    resolution: {integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/chokidar/-/chokidar-3.6.0.tgz}
    name: chokidar
    version: 3.6.0
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: registry.npmmirror.com/anymatch@3.1.3
      braces: registry.npmmirror.com/braces@3.0.3
      glob-parent: registry.npmmirror.com/glob-parent@5.1.2
      is-binary-path: registry.npmmirror.com/is-binary-path@2.1.0
      is-glob: registry.npmmirror.com/is-glob@4.0.3
      normalize-path: registry.npmmirror.com/normalize-path@3.0.0
      readdirp: registry.npmmirror.com/readdirp@3.6.0
    optionalDependencies:
      fsevents: registry.npmmirror.com/fsevents@2.3.3
    dev: true

  registry.npmmirror.com/chokidar@4.0.3:
    resolution: {integrity: sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/chokidar/-/chokidar-4.0.3.tgz}
    name: chokidar
    version: 4.0.3
    engines: {node: '>= 14.16.0'}
    dependencies:
      readdirp: registry.npmmirror.com/readdirp@4.1.2
    dev: true

  registry.npmmirror.com/chrome-trace-event@1.0.4:
    resolution: {integrity: sha512-rNjApaLzuwaOTjCiT8lSDdGN1APCiqkChLMJxJPWLunPAt5fy8xgU9/jNOchV84wfIxrA0lRQB7oCT8jrn/wrQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/chrome-trace-event/-/chrome-trace-event-1.0.4.tgz}
    name: chrome-trace-event
    version: 1.0.4
    engines: {node: '>=6.0'}
    dev: true

  registry.npmmirror.com/ci-info@3.9.0:
    resolution: {integrity: sha512-NIxF55hv4nSqQswkAeiOi1r83xy8JldOFDTWiug55KBu9Jnblncd2U6ViHmYgHf01TPZS77NJBhBMKdWj9HQMQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ci-info/-/ci-info-3.9.0.tgz}
    name: ci-info
    version: 3.9.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/cjs-module-lexer@1.4.3:
    resolution: {integrity: sha512-9z8TZaGM1pfswYeXrUpzPrkx8UnWYdhJclsiYMm6x/w5+nN+8Tf/LnAgfLGQCm59qAOxU8WwHEq2vNwF6i4j+Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/cjs-module-lexer/-/cjs-module-lexer-1.4.3.tgz}
    name: cjs-module-lexer
    version: 1.4.3
    dev: true

  registry.npmmirror.com/class-transformer@0.5.1:
    resolution: {integrity: sha512-SQa1Ws6hUbfC98vKGxZH3KFY0Y1lm5Zm0SY8XX9zbK7FJCyVEac3ATW0RIpwzW+oOfmHE5PMPufDG9hCfoEOMw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/class-transformer/-/class-transformer-0.5.1.tgz}
    name: class-transformer
    version: 0.5.1

  registry.npmmirror.com/class-validator@0.14.2:
    resolution: {integrity: sha512-3kMVRF2io8N8pY1IFIXlho9r8IPUUIfHe2hYVtiebvAzU2XeQFXTv+XI4WX+TnXmtwXMDcjngcpkiPM0O9PvLw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/class-validator/-/class-validator-0.14.2.tgz}
    name: class-validator
    version: 0.14.2
    dependencies:
      '@types/validator': registry.npmmirror.com/@types/validator@13.15.2
      libphonenumber-js: registry.npmmirror.com/libphonenumber-js@1.12.9
      validator: registry.npmmirror.com/validator@13.15.15

  registry.npmmirror.com/cli-cursor@3.1.0:
    resolution: {integrity: sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/cli-cursor/-/cli-cursor-3.1.0.tgz}
    name: cli-cursor
    version: 3.1.0
    engines: {node: '>=8'}
    dependencies:
      restore-cursor: registry.npmmirror.com/restore-cursor@3.1.0
    dev: true

  registry.npmmirror.com/cli-spinners@2.9.2:
    resolution: {integrity: sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/cli-spinners/-/cli-spinners-2.9.2.tgz}
    name: cli-spinners
    version: 2.9.2
    engines: {node: '>=6'}
    dev: true

  registry.npmmirror.com/cli-table3@0.6.5:
    resolution: {integrity: sha512-+W/5efTR7y5HRD7gACw9yQjqMVvEMLBHmboM/kPWam+H+Hmyrgjh6YncVKK122YZkXrLudzTuAukUw9FnMf7IQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/cli-table3/-/cli-table3-0.6.5.tgz}
    name: cli-table3
    version: 0.6.5
    engines: {node: 10.* || >= 12.*}
    dependencies:
      string-width: registry.npmmirror.com/string-width@4.2.3
    optionalDependencies:
      '@colors/colors': registry.npmmirror.com/@colors/colors@1.5.0
    dev: true

  registry.npmmirror.com/cli-width@4.1.0:
    resolution: {integrity: sha512-ouuZd4/dm2Sw5Gmqy6bGyNNNe1qt9RpmxveLSO7KcgsTnU7RXfsw+/bukWGo1abgBiMAic068rclZsO4IWmmxQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/cli-width/-/cli-width-4.1.0.tgz}
    name: cli-width
    version: 4.1.0
    engines: {node: '>= 12'}
    dev: true

  registry.npmmirror.com/cliui@8.0.1:
    resolution: {integrity: sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/cliui/-/cliui-8.0.1.tgz}
    name: cliui
    version: 8.0.1
    engines: {node: '>=12'}
    dependencies:
      string-width: registry.npmmirror.com/string-width@4.2.3
      strip-ansi: registry.npmmirror.com/strip-ansi@6.0.1
      wrap-ansi: registry.npmmirror.com/wrap-ansi@7.0.0

  registry.npmmirror.com/clone@1.0.4:
    resolution: {integrity: sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/clone/-/clone-1.0.4.tgz}
    name: clone
    version: 1.0.4
    engines: {node: '>=0.8'}
    dev: true

  registry.npmmirror.com/co@4.6.0:
    resolution: {integrity: sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/co/-/co-4.6.0.tgz}
    name: co
    version: 4.6.0
    engines: {iojs: '>= 1.0.0', node: '>= 0.12.0'}
    dev: true

  registry.npmmirror.com/collect-v8-coverage@1.0.2:
    resolution: {integrity: sha512-lHl4d5/ONEbLlJvaJNtsF/Lz+WvB07u2ycqTYbdrq7UypDXailES4valYb2eWiJFxZlVmpGekfqoxQhzyFdT4Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz}
    name: collect-v8-coverage
    version: 1.0.2
    dev: true

  registry.npmmirror.com/color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz}
    name: color-convert
    version: 2.0.1
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: registry.npmmirror.com/color-name@1.1.4

  registry.npmmirror.com/color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz}
    name: color-name
    version: 1.1.4

  registry.npmmirror.com/combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.8.tgz}
    name: combined-stream
    version: 1.0.8
    engines: {node: '>= 0.8'}
    dependencies:
      delayed-stream: registry.npmmirror.com/delayed-stream@1.0.0

  registry.npmmirror.com/commander@2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/commander/-/commander-2.20.3.tgz}
    name: commander
    version: 2.20.3
    dev: true

  registry.npmmirror.com/commander@4.1.1:
    resolution: {integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/commander/-/commander-4.1.1.tgz}
    name: commander
    version: 4.1.1
    engines: {node: '>= 6'}
    dev: true

  registry.npmmirror.com/commander@6.2.1:
    resolution: {integrity: sha512-U7VdrJFnJgo4xjrHpTzu0yrHPGImdsmD95ZlgYSEajAn2JKzDhDTPG9kBTefmObL2w/ngeZnilk+OV9CG3d7UA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/commander/-/commander-6.2.1.tgz}
    name: commander
    version: 6.2.1
    engines: {node: '>= 6'}
    dev: true

  registry.npmmirror.com/commander@8.3.0:
    resolution: {integrity: sha512-OkTL9umf+He2DZkUq8f8J9of7yL6RJKI24dVITBmNfZBmri9zYZQrKkuXiKhyfPSu8tUhnVBB1iKXevvnlR4Ww==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/commander/-/commander-8.3.0.tgz}
    name: commander
    version: 8.3.0
    engines: {node: '>= 12'}
    dev: true

  registry.npmmirror.com/comment-json@4.2.5:
    resolution: {integrity: sha512-bKw/r35jR3HGt5PEPm1ljsQQGyCrR8sFGNiN5L+ykDHdpO8Smxkrkla9Yi6NkQyUrb8V54PGhfMs6NrIwtxtdw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/comment-json/-/comment-json-4.2.5.tgz}
    name: comment-json
    version: 4.2.5
    engines: {node: '>= 6'}
    dependencies:
      array-timsort: registry.npmmirror.com/array-timsort@1.0.3
      core-util-is: registry.npmmirror.com/core-util-is@1.0.3
      esprima: registry.npmmirror.com/esprima@4.0.1
      has-own-prop: registry.npmmirror.com/has-own-prop@2.0.0
      repeat-string: registry.npmmirror.com/repeat-string@1.6.1
    dev: true

  registry.npmmirror.com/component-emitter@1.3.1:
    resolution: {integrity: sha512-T0+barUSQRTUQASh8bx02dl+DhF54GtIDY13Y3m9oWTklKbb3Wv974meRpeZ3lp1JpLVECWWNHC4vaG2XHXouQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/component-emitter/-/component-emitter-1.3.1.tgz}
    name: component-emitter
    version: 1.3.1
    dev: true

  registry.npmmirror.com/concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/concat-map/-/concat-map-0.0.1.tgz}
    name: concat-map
    version: 0.0.1
    dev: true

  registry.npmmirror.com/concat-stream@1.6.2:
    resolution: {integrity: sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/concat-stream/-/concat-stream-1.6.2.tgz}
    name: concat-stream
    version: 1.6.2
    engines: {'0': node >= 0.8}
    dependencies:
      buffer-from: registry.npmmirror.com/buffer-from@1.1.2
      inherits: registry.npmmirror.com/inherits@2.0.4
      readable-stream: registry.npmmirror.com/readable-stream@2.3.8
      typedarray: registry.npmmirror.com/typedarray@0.0.6

  registry.npmmirror.com/consola@3.4.2:
    resolution: {integrity: sha512-5IKcdX0nnYavi6G7TtOhwkYzyjfJlatbjMjuLSfE2kYT5pMDOilZ4OvMhi637CcDICTmz3wARPoyhqyX1Y+XvA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/consola/-/consola-3.4.2.tgz}
    name: consola
    version: 3.4.2
    engines: {node: ^14.18.0 || >=16.10.0}

  registry.npmmirror.com/content-disposition@0.5.4:
    resolution: {integrity: sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/content-disposition/-/content-disposition-0.5.4.tgz}
    name: content-disposition
    version: 0.5.4
    engines: {node: '>= 0.6'}
    dependencies:
      safe-buffer: registry.npmmirror.com/safe-buffer@5.2.1
    dev: true

  registry.npmmirror.com/content-disposition@1.0.0:
    resolution: {integrity: sha512-Au9nRL8VNUut/XSzbQA38+M78dzP4D+eqg3gfJHMIHHYa3bg067xj1KxMUWj+VULbiZMowKngFFbKczUrNJ1mg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/content-disposition/-/content-disposition-1.0.0.tgz}
    name: content-disposition
    version: 1.0.0
    engines: {node: '>= 0.6'}
    dependencies:
      safe-buffer: registry.npmmirror.com/safe-buffer@5.2.1

  registry.npmmirror.com/content-type@1.0.5:
    resolution: {integrity: sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/content-type/-/content-type-1.0.5.tgz}
    name: content-type
    version: 1.0.5
    engines: {node: '>= 0.6'}

  registry.npmmirror.com/convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/convert-source-map/-/convert-source-map-2.0.0.tgz}
    name: convert-source-map
    version: 2.0.0
    dev: true

  registry.npmmirror.com/cookie-signature@1.2.2:
    resolution: {integrity: sha512-D76uU73ulSXrD1UXF4KE2TMxVVwhsnCgfAyTg9k8P6KGZjlXKrOLe4dJQKI3Bxi5wjesZoFXJWElNWBjPZMbhg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/cookie-signature/-/cookie-signature-1.2.2.tgz}
    name: cookie-signature
    version: 1.2.2
    engines: {node: '>=6.6.0'}

  registry.npmmirror.com/cookie@0.7.1:
    resolution: {integrity: sha512-6DnInpx7SJ2AK3+CTUE/ZM0vWTUboZCegxhC2xiIydHR9jNuTAASBrfEpHhiGOZw/nX51bHt6YQl8jsGo4y/0w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/cookie/-/cookie-0.7.1.tgz}
    name: cookie
    version: 0.7.1
    engines: {node: '>= 0.6'}

  registry.npmmirror.com/cookiejar@2.1.4:
    resolution: {integrity: sha512-LDx6oHrK+PhzLKJU9j5S7/Y3jM/mUHvD/DeI1WQmJn652iPC5Y4TBzC9l+5OMOXlyTTA+SmVUPm0HQUwpD5Jqw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/cookiejar/-/cookiejar-2.1.4.tgz}
    name: cookiejar
    version: 2.1.4
    dev: true

  registry.npmmirror.com/core-util-is@1.0.3:
    resolution: {integrity: sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/core-util-is/-/core-util-is-1.0.3.tgz}
    name: core-util-is
    version: 1.0.3

  registry.npmmirror.com/cors@2.8.5:
    resolution: {integrity: sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/cors/-/cors-2.8.5.tgz}
    name: cors
    version: 2.8.5
    engines: {node: '>= 0.10'}
    dependencies:
      object-assign: registry.npmmirror.com/object-assign@4.1.1
      vary: registry.npmmirror.com/vary@1.1.2

  registry.npmmirror.com/cosmiconfig@8.3.6(typescript@5.7.3):
    resolution: {integrity: sha512-kcZ6+W5QzcJ3P1Mt+83OUv/oHFqZHIx8DuxG6eZ5RGMERoLqp4BuGjhHLYGK+Kf5XVkQvqBSmAy/nGWN3qDgEA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/cosmiconfig/-/cosmiconfig-8.3.6.tgz}
    id: registry.npmmirror.com/cosmiconfig/8.3.6
    name: cosmiconfig
    version: 8.3.6
    engines: {node: '>=14'}
    peerDependencies:
      typescript: '>=4.9.5'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      import-fresh: registry.npmmirror.com/import-fresh@3.3.1
      js-yaml: registry.npmmirror.com/js-yaml@4.1.0
      parse-json: registry.npmmirror.com/parse-json@5.2.0
      path-type: registry.npmmirror.com/path-type@4.0.0
      typescript: registry.npmmirror.com/typescript@5.7.3
    dev: true

  registry.npmmirror.com/create-jest@29.7.0(@types/node@22.10.7)(ts-node@10.9.2):
    resolution: {integrity: sha512-Adz2bdH0Vq3F53KEMJOoftQFutWCukm6J24wbPWRO4k1kMY7gS7ds/uoJkNuV8wDCtWWnuwGcJwpWcih+zEW1Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/create-jest/-/create-jest-29.7.0.tgz}
    id: registry.npmmirror.com/create-jest/29.7.0
    name: create-jest
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    hasBin: true
    dependencies:
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      chalk: registry.npmmirror.com/chalk@4.1.2
      exit: registry.npmmirror.com/exit@0.1.2
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
      jest-config: registry.npmmirror.com/jest-config@29.7.0(@types/node@22.10.7)(ts-node@10.9.2)
      jest-util: registry.npmmirror.com/jest-util@29.7.0
      prompts: registry.npmmirror.com/prompts@2.4.2
    transitivePeerDependencies:
      - '@types/node'
      - babel-plugin-macros
      - supports-color
      - ts-node
    dev: true

  registry.npmmirror.com/create-require@1.1.1:
    resolution: {integrity: sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/create-require/-/create-require-1.1.1.tgz}
    name: create-require
    version: 1.1.1

  registry.npmmirror.com/cross-spawn@7.0.6:
    resolution: {integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.6.tgz}
    name: cross-spawn
    version: 7.0.6
    engines: {node: '>= 8'}
    dependencies:
      path-key: registry.npmmirror.com/path-key@3.1.1
      shebang-command: registry.npmmirror.com/shebang-command@2.0.0
      which: registry.npmmirror.com/which@2.0.2

  registry.npmmirror.com/crypto-js@4.2.0:
    resolution: {integrity: sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/crypto-js/-/crypto-js-4.2.0.tgz}
    name: crypto-js
    version: 4.2.0
    dev: false

  registry.npmmirror.com/dayjs@1.11.13:
    resolution: {integrity: sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/dayjs/-/dayjs-1.11.13.tgz}
    name: dayjs
    version: 1.11.13
    dev: false

  registry.npmmirror.com/debug@2.6.9:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/debug/-/debug-2.6.9.tgz}
    name: debug
    version: 2.6.9
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: registry.npmmirror.com/ms@2.0.0

  registry.npmmirror.com/debug@4.3.6:
    resolution: {integrity: sha512-O/09Bd4Z1fBrU4VzkhFqVgpPzaGbw6Sm9FEkBT1A/YBXQFGuuSxa1dN2nxgxS34JmKXqYx8CZAwEVoJFImUXIg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/debug/-/debug-4.3.6.tgz}
    name: debug
    version: 4.3.6
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: registry.npmmirror.com/ms@2.1.2

  registry.npmmirror.com/debug@4.4.1:
    resolution: {integrity: sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/debug/-/debug-4.4.1.tgz}
    name: debug
    version: 4.4.1
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: registry.npmmirror.com/ms@2.1.3

  registry.npmmirror.com/decompress-response@6.0.0:
    resolution: {integrity: sha512-aW35yZM6Bb/4oJlZncMH2LCoZtJXTRxES17vE3hoRiowU2kWHaJKFkSBDnDR+cm9J+9QhXmREyIfv0pji9ejCQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/decompress-response/-/decompress-response-6.0.0.tgz}
    name: decompress-response
    version: 6.0.0
    engines: {node: '>=10'}
    dependencies:
      mimic-response: registry.npmmirror.com/mimic-response@3.1.0
    dev: true

  registry.npmmirror.com/dedent@1.6.0:
    resolution: {integrity: sha512-F1Z+5UCFpmQUzJa11agbyPVMbpgT/qA3/SKyJ1jyBgm7dUcUEa8v9JwDkerSQXfakBwFljIxhOJqGkjUwZ9FSA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/dedent/-/dedent-1.6.0.tgz}
    name: dedent
    version: 1.6.0
    peerDependencies:
      babel-plugin-macros: ^3.1.0
    peerDependenciesMeta:
      babel-plugin-macros:
        optional: true

  registry.npmmirror.com/deep-is@0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/deep-is/-/deep-is-0.1.4.tgz}
    name: deep-is
    version: 0.1.4
    dev: true

  registry.npmmirror.com/deepmerge@4.3.1:
    resolution: {integrity: sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/deepmerge/-/deepmerge-4.3.1.tgz}
    name: deepmerge
    version: 4.3.1
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/defaults@1.0.4:
    resolution: {integrity: sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/defaults/-/defaults-1.0.4.tgz}
    name: defaults
    version: 1.0.4
    dependencies:
      clone: registry.npmmirror.com/clone@1.0.4
    dev: true

  registry.npmmirror.com/defaults@3.0.0:
    resolution: {integrity: sha512-RsqXDEAALjfRTro+IFNKpcPCt0/Cy2FqHSIlnomiJp9YGadpQnrtbRpSgN2+np21qHcIKiva4fiOQGjS9/qR/A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/defaults/-/defaults-3.0.0.tgz}
    name: defaults
    version: 3.0.0
    engines: {node: '>=18'}
    dev: true

  registry.npmmirror.com/defer-to-connect@2.0.1:
    resolution: {integrity: sha512-4tvttepXG1VaYGrRibk5EwJd1t4udunSOVMdLSAL6mId1ix438oPwPZMALY41FCijukO1L0twNcGsdzS7dHgDg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/defer-to-connect/-/defer-to-connect-2.0.1.tgz}
    name: defer-to-connect
    version: 2.0.1
    engines: {node: '>=10'}
    dev: true

  registry.npmmirror.com/delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/delayed-stream/-/delayed-stream-1.0.0.tgz}
    name: delayed-stream
    version: 1.0.0
    engines: {node: '>=0.4.0'}

  registry.npmmirror.com/denque@2.1.0:
    resolution: {integrity: sha512-HVQE3AAb/pxF8fQAoiqpvg9i3evqug3hoiwakOyZAwJm+6vZehbkYXZ0l4JxS+I3QxM97v5aaRNhj8v5oBhekw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/denque/-/denque-2.1.0.tgz}
    name: denque
    version: 2.1.0
    engines: {node: '>=0.10'}
    dev: false

  registry.npmmirror.com/depd@2.0.0:
    resolution: {integrity: sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/depd/-/depd-2.0.0.tgz}
    name: depd
    version: 2.0.0
    engines: {node: '>= 0.8'}

  registry.npmmirror.com/destroy@1.2.0:
    resolution: {integrity: sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/destroy/-/destroy-1.2.0.tgz}
    name: destroy
    version: 1.2.0
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  registry.npmmirror.com/detect-newline@3.1.0:
    resolution: {integrity: sha512-TLz+x/vEXm/Y7P7wn1EJFNLxYpUD4TgMosxY6fAVJUnJMbupHBOncxyWUG9OpTaH9EBD7uFI5LfEgmMOc54DsA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/detect-newline/-/detect-newline-3.1.0.tgz}
    name: detect-newline
    version: 3.1.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/dezalgo@1.0.4:
    resolution: {integrity: sha512-rXSP0bf+5n0Qonsb+SVVfNfIsimO4HEtmnIpPHY8Q1UCzKlQrDMfdobr8nJOOsRgWCyMRqeSBQzmWUMq7zvVig==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/dezalgo/-/dezalgo-1.0.4.tgz}
    name: dezalgo
    version: 1.0.4
    dependencies:
      asap: registry.npmmirror.com/asap@2.0.6
      wrappy: registry.npmmirror.com/wrappy@1.0.2
    dev: true

  registry.npmmirror.com/diff-sequences@29.6.3:
    resolution: {integrity: sha512-EjePK1srD3P08o2j4f0ExnylqRs5B9tJjcp9t1krH2qRi8CCdsYfwe9JgSLurFBWwq4uOlipzfk5fHNvwFKr8Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/diff-sequences/-/diff-sequences-29.6.3.tgz}
    name: diff-sequences
    version: 29.6.3
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dev: true

  registry.npmmirror.com/diff@4.0.2:
    resolution: {integrity: sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/diff/-/diff-4.0.2.tgz}
    name: diff
    version: 4.0.2
    engines: {node: '>=0.3.1'}

  registry.npmmirror.com/dot-case@3.0.4:
    resolution: {integrity: sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/dot-case/-/dot-case-3.0.4.tgz}
    name: dot-case
    version: 3.0.4
    dependencies:
      no-case: registry.npmmirror.com/no-case@3.0.4
      tslib: registry.npmmirror.com/tslib@2.8.1
    dev: false

  registry.npmmirror.com/dotenv-expand@12.0.1:
    resolution: {integrity: sha512-LaKRbou8gt0RNID/9RoI+J2rvXsBRPMV7p+ElHlPhcSARbCPDYcYG2s1TIzAfWv4YSgyY5taidWzzs31lNV3yQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/dotenv-expand/-/dotenv-expand-12.0.1.tgz}
    name: dotenv-expand
    version: 12.0.1
    engines: {node: '>=12'}
    dependencies:
      dotenv: registry.npmmirror.com/dotenv@16.4.7
    dev: false

  registry.npmmirror.com/dotenv@16.4.7:
    resolution: {integrity: sha512-47qPchRCykZC03FhkYAhrvwU4xDBFIj1QPqaarj6mdM/hgUzfPHcpkHJOn3mJAufFeeAxAzeGsr5X0M4k6fLZQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/dotenv/-/dotenv-16.4.7.tgz}
    name: dotenv
    version: 16.4.7
    engines: {node: '>=12'}
    dev: false

  registry.npmmirror.com/dotenv@16.6.1:
    resolution: {integrity: sha512-uBq4egWHTcTt33a72vpSG0z3HnPuIl6NqYcTrKEg2azoEyl2hpW0zqlxysq2pK9HlDIHyHyakeYaYnSAwd8bow==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/dotenv/-/dotenv-16.6.1.tgz}
    name: dotenv
    version: 16.6.1
    engines: {node: '>=12'}
    dev: false

  registry.npmmirror.com/dunder-proto@1.0.1:
    resolution: {integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/dunder-proto/-/dunder-proto-1.0.1.tgz}
    name: dunder-proto
    version: 1.0.1
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: registry.npmmirror.com/call-bind-apply-helpers@1.0.2
      es-errors: registry.npmmirror.com/es-errors@1.3.0
      gopd: registry.npmmirror.com/gopd@1.2.0

  registry.npmmirror.com/eastasianwidth@0.2.0:
    resolution: {integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/eastasianwidth/-/eastasianwidth-0.2.0.tgz}
    name: eastasianwidth
    version: 0.2.0

  registry.npmmirror.com/ecdsa-sig-formatter@1.0.11:
    resolution: {integrity: sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz}
    name: ecdsa-sig-formatter
    version: 1.0.11
    dependencies:
      safe-buffer: registry.npmmirror.com/safe-buffer@5.2.1
    dev: false

  registry.npmmirror.com/ee-first@1.1.1:
    resolution: {integrity: sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ee-first/-/ee-first-1.1.1.tgz}
    name: ee-first
    version: 1.1.1

  registry.npmmirror.com/ejs@3.1.10:
    resolution: {integrity: sha512-UeJmFfOrAQS8OJWPZ4qtgHyWExa088/MtK5UEyoJGFH67cDEXkZSviOiKRCZ4Xij0zxI3JECgYs3oKx+AizQBA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ejs/-/ejs-3.1.10.tgz}
    name: ejs
    version: 3.1.10
    engines: {node: '>=0.10.0'}
    hasBin: true
    dependencies:
      jake: registry.npmmirror.com/jake@10.9.2
    dev: true

  registry.npmmirror.com/electron-to-chromium@1.5.177:
    resolution: {integrity: sha512-7EH2G59nLsEMj97fpDuvVcYi6lwTcM1xuWw3PssD8xzboAW7zj7iB3COEEEATUfjLHrs5uKBLQT03V/8URx06g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/electron-to-chromium/-/electron-to-chromium-1.5.177.tgz}
    name: electron-to-chromium
    version: 1.5.177
    dev: true

  registry.npmmirror.com/emittery@0.13.1:
    resolution: {integrity: sha512-DeWwawk6r5yR9jFgnDKYt4sLS0LmHJJi3ZOnb5/JdbYwj3nW+FxQnHIjhBKz8YLC7oRNPVM9NQ47I3CVx34eqQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/emittery/-/emittery-0.13.1.tgz}
    name: emittery
    version: 0.13.1
    engines: {node: '>=12'}
    dev: true

  registry.npmmirror.com/emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/emoji-regex/-/emoji-regex-8.0.0.tgz}
    name: emoji-regex
    version: 8.0.0

  registry.npmmirror.com/emoji-regex@9.2.2:
    resolution: {integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/emoji-regex/-/emoji-regex-9.2.2.tgz}
    name: emoji-regex
    version: 9.2.2

  registry.npmmirror.com/encodeurl@2.0.0:
    resolution: {integrity: sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/encodeurl/-/encodeurl-2.0.0.tgz}
    name: encodeurl
    version: 2.0.0
    engines: {node: '>= 0.8'}

  registry.npmmirror.com/enhanced-resolve@5.18.2:
    resolution: {integrity: sha512-6Jw4sE1maoRJo3q8MsSIn2onJFbLTOjY9hlx4DZXmOKvLRd1Ok2kXmAGXaafL2+ijsJZ1ClYbl/pmqr9+k4iUQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/enhanced-resolve/-/enhanced-resolve-5.18.2.tgz}
    name: enhanced-resolve
    version: 5.18.2
    engines: {node: '>=10.13.0'}
    dependencies:
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
      tapable: registry.npmmirror.com/tapable@2.2.2
    dev: true

  registry.npmmirror.com/error-ex@1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/error-ex/-/error-ex-1.3.2.tgz}
    name: error-ex
    version: 1.3.2
    dependencies:
      is-arrayish: registry.npmmirror.com/is-arrayish@0.2.1
    dev: true

  registry.npmmirror.com/es-define-property@1.0.1:
    resolution: {integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/es-define-property/-/es-define-property-1.0.1.tgz}
    name: es-define-property
    version: 1.0.1
    engines: {node: '>= 0.4'}

  registry.npmmirror.com/es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/es-errors/-/es-errors-1.3.0.tgz}
    name: es-errors
    version: 1.3.0
    engines: {node: '>= 0.4'}

  registry.npmmirror.com/es-module-lexer@1.7.0:
    resolution: {integrity: sha512-jEQoCwk8hyb2AZziIOLhDqpm5+2ww5uIE6lkO/6jcOCusfk6LhMHpXXfBLXTZ7Ydyt0j4VoUQv6uGNYbdW+kBA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/es-module-lexer/-/es-module-lexer-1.7.0.tgz}
    name: es-module-lexer
    version: 1.7.0
    dev: true

  registry.npmmirror.com/es-object-atoms@1.1.1:
    resolution: {integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/es-object-atoms/-/es-object-atoms-1.1.1.tgz}
    name: es-object-atoms
    version: 1.1.1
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: registry.npmmirror.com/es-errors@1.3.0

  registry.npmmirror.com/es-set-tostringtag@2.1.0:
    resolution: {integrity: sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz}
    name: es-set-tostringtag
    version: 2.1.0
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: registry.npmmirror.com/es-errors@1.3.0
      get-intrinsic: registry.npmmirror.com/get-intrinsic@1.3.0
      has-tostringtag: registry.npmmirror.com/has-tostringtag@1.0.2
      hasown: registry.npmmirror.com/hasown@2.0.2

  registry.npmmirror.com/escalade@3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/escalade/-/escalade-3.2.0.tgz}
    name: escalade
    version: 3.2.0
    engines: {node: '>=6'}

  registry.npmmirror.com/escape-html@1.0.3:
    resolution: {integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/escape-html/-/escape-html-1.0.3.tgz}
    name: escape-html
    version: 1.0.3

  registry.npmmirror.com/escape-string-regexp@2.0.0:
    resolution: {integrity: sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz}
    name: escape-string-regexp
    version: 2.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/escape-string-regexp@4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz}
    name: escape-string-regexp
    version: 4.0.0
    engines: {node: '>=10'}
    dev: true

  registry.npmmirror.com/eslint-config-prettier@10.0.1(eslint@9.18.0):
    resolution: {integrity: sha512-lZBts941cyJyeaooiKxAtzoPHTN+GbQTJFAIdQbRhA4/8whaAraEh47Whw/ZFfrjNSnlAxqfm9i0XVAEkULjCw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/eslint-config-prettier/-/eslint-config-prettier-10.0.1.tgz}
    id: registry.npmmirror.com/eslint-config-prettier/10.0.1
    name: eslint-config-prettier
    version: 10.0.1
    hasBin: true
    peerDependencies:
      eslint: '>=7.0.0'
    dependencies:
      eslint: registry.npmmirror.com/eslint@9.18.0
    dev: true

  registry.npmmirror.com/eslint-plugin-prettier@5.2.2(eslint-config-prettier@10.0.1)(eslint@9.18.0)(prettier@3.4.2):
    resolution: {integrity: sha512-1yI3/hf35wmlq66C8yOyrujQnel+v5l1Vop5Cl2I6ylyNTT1JbuUUnV3/41PzwTzcyDp/oF0jWE3HXvcH5AQOQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.2.2.tgz}
    id: registry.npmmirror.com/eslint-plugin-prettier/5.2.2
    name: eslint-plugin-prettier
    version: 5.2.2
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      '@types/eslint': '>=8.0.0'
      eslint: '>=8.0.0'
      eslint-config-prettier: '*'
      prettier: '>=3.0.0'
    peerDependenciesMeta:
      '@types/eslint':
        optional: true
      eslint-config-prettier:
        optional: true
    dependencies:
      eslint: registry.npmmirror.com/eslint@9.18.0
      eslint-config-prettier: registry.npmmirror.com/eslint-config-prettier@10.0.1(eslint@9.18.0)
      prettier: registry.npmmirror.com/prettier@3.4.2
      prettier-linter-helpers: registry.npmmirror.com/prettier-linter-helpers@1.0.0
      synckit: registry.npmmirror.com/synckit@0.9.3
    dev: true

  registry.npmmirror.com/eslint-scope@5.1.1:
    resolution: {integrity: sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/eslint-scope/-/eslint-scope-5.1.1.tgz}
    name: eslint-scope
    version: 5.1.1
    engines: {node: '>=8.0.0'}
    dependencies:
      esrecurse: registry.npmmirror.com/esrecurse@4.3.0
      estraverse: registry.npmmirror.com/estraverse@4.3.0
    dev: true

  registry.npmmirror.com/eslint-scope@8.4.0:
    resolution: {integrity: sha512-sNXOfKCn74rt8RICKMvJS7XKV/Xk9kA7DyJr8mJik3S7Cwgy3qlkkmyS2uQB3jiJg6VNdZd/pDBJu0nvG2NlTg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/eslint-scope/-/eslint-scope-8.4.0.tgz}
    name: eslint-scope
    version: 8.4.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      esrecurse: registry.npmmirror.com/esrecurse@4.3.0
      estraverse: registry.npmmirror.com/estraverse@5.3.0
    dev: true

  registry.npmmirror.com/eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz}
    name: eslint-visitor-keys
    version: 3.4.3
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dev: true

  registry.npmmirror.com/eslint-visitor-keys@4.2.1:
    resolution: {integrity: sha512-Uhdk5sfqcee/9H/rCOJikYz67o0a2Tw2hGRPOG2Y1R2dg7brRe1uG0yaNQDHu+TO/uQPF/5eCapvYSmHUjt7JQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-4.2.1.tgz}
    name: eslint-visitor-keys
    version: 4.2.1
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dev: true

  registry.npmmirror.com/eslint@9.18.0:
    resolution: {integrity: sha512-+waTfRWQlSbpt3KWE+CjrPPYnbq9kfZIYUqapc0uBXyjTp8aYXZDsUH16m39Ryq3NjAVP4tjuF7KaukeqoCoaA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/eslint/-/eslint-9.18.0.tgz}
    name: eslint
    version: 9.18.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    hasBin: true
    peerDependencies:
      jiti: '*'
    peerDependenciesMeta:
      jiti:
        optional: true
    dependencies:
      '@eslint-community/eslint-utils': registry.npmmirror.com/@eslint-community/eslint-utils@4.7.0(eslint@9.18.0)
      '@eslint-community/regexpp': registry.npmmirror.com/@eslint-community/regexpp@4.12.1
      '@eslint/config-array': registry.npmmirror.com/@eslint/config-array@0.19.2
      '@eslint/core': registry.npmmirror.com/@eslint/core@0.10.0
      '@eslint/eslintrc': registry.npmmirror.com/@eslint/eslintrc@3.2.0
      '@eslint/js': registry.npmmirror.com/@eslint/js@9.18.0
      '@eslint/plugin-kit': registry.npmmirror.com/@eslint/plugin-kit@0.2.8
      '@humanfs/node': registry.npmmirror.com/@humanfs/node@0.16.6
      '@humanwhocodes/module-importer': registry.npmmirror.com/@humanwhocodes/module-importer@1.0.1
      '@humanwhocodes/retry': registry.npmmirror.com/@humanwhocodes/retry@0.4.3
      '@types/estree': registry.npmmirror.com/@types/estree@1.0.8
      '@types/json-schema': registry.npmmirror.com/@types/json-schema@7.0.15
      ajv: registry.npmmirror.com/ajv@6.12.6
      chalk: registry.npmmirror.com/chalk@4.1.2
      cross-spawn: registry.npmmirror.com/cross-spawn@7.0.6
      debug: registry.npmmirror.com/debug@4.4.1
      escape-string-regexp: registry.npmmirror.com/escape-string-regexp@4.0.0
      eslint-scope: registry.npmmirror.com/eslint-scope@8.4.0
      eslint-visitor-keys: registry.npmmirror.com/eslint-visitor-keys@4.2.1
      espree: registry.npmmirror.com/espree@10.4.0
      esquery: registry.npmmirror.com/esquery@1.6.0
      esutils: registry.npmmirror.com/esutils@2.0.3
      fast-deep-equal: registry.npmmirror.com/fast-deep-equal@3.1.3
      file-entry-cache: registry.npmmirror.com/file-entry-cache@8.0.0
      find-up: registry.npmmirror.com/find-up@5.0.0
      glob-parent: registry.npmmirror.com/glob-parent@6.0.2
      ignore: registry.npmmirror.com/ignore@5.3.2
      imurmurhash: registry.npmmirror.com/imurmurhash@0.1.4
      is-glob: registry.npmmirror.com/is-glob@4.0.3
      json-stable-stringify-without-jsonify: registry.npmmirror.com/json-stable-stringify-without-jsonify@1.0.1
      lodash.merge: registry.npmmirror.com/lodash.merge@4.6.2
      minimatch: registry.npmmirror.com/minimatch@3.1.2
      natural-compare: registry.npmmirror.com/natural-compare@1.4.0
      optionator: registry.npmmirror.com/optionator@0.9.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/espree@10.4.0:
    resolution: {integrity: sha512-j6PAQ2uUr79PZhBjP5C5fhl8e39FmRnOjsD5lGnWrFU8i2G776tBK7+nP8KuQUTTyAZUwfQqXAgrVH5MbH9CYQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/espree/-/espree-10.4.0.tgz}
    name: espree
    version: 10.4.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      acorn: registry.npmmirror.com/acorn@8.15.0
      acorn-jsx: registry.npmmirror.com/acorn-jsx@5.3.2(acorn@8.15.0)
      eslint-visitor-keys: registry.npmmirror.com/eslint-visitor-keys@4.2.1
    dev: true

  registry.npmmirror.com/esprima@4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esprima/-/esprima-4.0.1.tgz}
    name: esprima
    version: 4.0.1
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  registry.npmmirror.com/esquery@1.6.0:
    resolution: {integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esquery/-/esquery-1.6.0.tgz}
    name: esquery
    version: 1.6.0
    engines: {node: '>=0.10'}
    dependencies:
      estraverse: registry.npmmirror.com/estraverse@5.3.0
    dev: true

  registry.npmmirror.com/esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esrecurse/-/esrecurse-4.3.0.tgz}
    name: esrecurse
    version: 4.3.0
    engines: {node: '>=4.0'}
    dependencies:
      estraverse: registry.npmmirror.com/estraverse@5.3.0
    dev: true

  registry.npmmirror.com/estraverse@4.3.0:
    resolution: {integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/estraverse/-/estraverse-4.3.0.tgz}
    name: estraverse
    version: 4.3.0
    engines: {node: '>=4.0'}
    dev: true

  registry.npmmirror.com/estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/estraverse/-/estraverse-5.3.0.tgz}
    name: estraverse
    version: 5.3.0
    engines: {node: '>=4.0'}
    dev: true

  registry.npmmirror.com/esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esutils/-/esutils-2.0.3.tgz}
    name: esutils
    version: 2.0.3
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/etag@1.8.1:
    resolution: {integrity: sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/etag/-/etag-1.8.1.tgz}
    name: etag
    version: 1.8.1
    engines: {node: '>= 0.6'}

  registry.npmmirror.com/events@3.3.0:
    resolution: {integrity: sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/events/-/events-3.3.0.tgz}
    name: events
    version: 3.3.0
    engines: {node: '>=0.8.x'}
    dev: true

  registry.npmmirror.com/execa@5.1.1:
    resolution: {integrity: sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/execa/-/execa-5.1.1.tgz}
    name: execa
    version: 5.1.1
    engines: {node: '>=10'}
    dependencies:
      cross-spawn: registry.npmmirror.com/cross-spawn@7.0.6
      get-stream: registry.npmmirror.com/get-stream@6.0.1
      human-signals: registry.npmmirror.com/human-signals@2.1.0
      is-stream: registry.npmmirror.com/is-stream@2.0.1
      merge-stream: registry.npmmirror.com/merge-stream@2.0.0
      npm-run-path: registry.npmmirror.com/npm-run-path@4.0.1
      onetime: registry.npmmirror.com/onetime@5.1.2
      signal-exit: registry.npmmirror.com/signal-exit@3.0.7
      strip-final-newline: registry.npmmirror.com/strip-final-newline@2.0.0
    dev: true

  registry.npmmirror.com/exit@0.1.2:
    resolution: {integrity: sha512-Zk/eNKV2zbjpKzrsQ+n1G6poVbErQxJ0LBOJXaKZ1EViLzH+hrLu9cdXI4zw9dBQJslwBEpbQ2P1oS7nDxs6jQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/exit/-/exit-0.1.2.tgz}
    name: exit
    version: 0.1.2
    engines: {node: '>= 0.8.0'}
    dev: true

  registry.npmmirror.com/expect@29.7.0:
    resolution: {integrity: sha512-2Zks0hf1VLFYI1kbh0I5jP3KHHyCHpkfyHBzsSXRFgl/Bg9mWYfMW8oD+PdMPlEwy5HNsR9JutYy6pMeOh61nw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/expect/-/expect-29.7.0.tgz}
    name: expect
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/expect-utils': registry.npmmirror.com/@jest/expect-utils@29.7.0
      jest-get-type: registry.npmmirror.com/jest-get-type@29.6.3
      jest-matcher-utils: registry.npmmirror.com/jest-matcher-utils@29.7.0
      jest-message-util: registry.npmmirror.com/jest-message-util@29.7.0
      jest-util: registry.npmmirror.com/jest-util@29.7.0
    dev: true

  registry.npmmirror.com/express@5.0.1:
    resolution: {integrity: sha512-ORF7g6qGnD+YtUG9yx4DFoqCShNMmUKiXuT5oWMHiOvt/4WFbHC6yCwQMTSBMno7AqntNCAzzcnnjowRkTL9eQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/express/-/express-5.0.1.tgz}
    name: express
    version: 5.0.1
    engines: {node: '>= 18'}
    dependencies:
      accepts: registry.npmmirror.com/accepts@2.0.0
      body-parser: registry.npmmirror.com/body-parser@2.2.0
      content-disposition: registry.npmmirror.com/content-disposition@1.0.0
      content-type: registry.npmmirror.com/content-type@1.0.5
      cookie: registry.npmmirror.com/cookie@0.7.1
      cookie-signature: registry.npmmirror.com/cookie-signature@1.2.2
      debug: registry.npmmirror.com/debug@4.3.6
      depd: registry.npmmirror.com/depd@2.0.0
      encodeurl: registry.npmmirror.com/encodeurl@2.0.0
      escape-html: registry.npmmirror.com/escape-html@1.0.3
      etag: registry.npmmirror.com/etag@1.8.1
      finalhandler: registry.npmmirror.com/finalhandler@2.1.0
      fresh: registry.npmmirror.com/fresh@2.0.0
      http-errors: registry.npmmirror.com/http-errors@2.0.0
      merge-descriptors: registry.npmmirror.com/merge-descriptors@2.0.0
      methods: registry.npmmirror.com/methods@1.1.2
      mime-types: registry.npmmirror.com/mime-types@3.0.1
      on-finished: registry.npmmirror.com/on-finished@2.4.1
      once: registry.npmmirror.com/once@1.4.0
      parseurl: registry.npmmirror.com/parseurl@1.3.3
      proxy-addr: registry.npmmirror.com/proxy-addr@2.0.7
      qs: registry.npmmirror.com/qs@6.13.0
      range-parser: registry.npmmirror.com/range-parser@1.2.1
      router: registry.npmmirror.com/router@2.2.0
      safe-buffer: registry.npmmirror.com/safe-buffer@5.2.1
      send: registry.npmmirror.com/send@1.2.0
      serve-static: registry.npmmirror.com/serve-static@2.2.0
      setprototypeof: registry.npmmirror.com/setprototypeof@1.2.0
      statuses: registry.npmmirror.com/statuses@2.0.1
      type-is: registry.npmmirror.com/type-is@2.0.1
      utils-merge: registry.npmmirror.com/utils-merge@1.0.1
      vary: registry.npmmirror.com/vary@1.1.2
    transitivePeerDependencies:
      - supports-color

  registry.npmmirror.com/ext-list@2.2.2:
    resolution: {integrity: sha512-u+SQgsubraE6zItfVA0tBuCBhfU9ogSRnsvygI7wht9TS510oLkBRXBsqopeUG/GBOIQyKZO9wjTqIu/sf5zFA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ext-list/-/ext-list-2.2.2.tgz}
    name: ext-list
    version: 2.2.2
    engines: {node: '>=0.10.0'}
    dependencies:
      mime-db: registry.npmmirror.com/mime-db@1.54.0
    dev: true

  registry.npmmirror.com/ext-name@5.0.0:
    resolution: {integrity: sha512-yblEwXAbGv1VQDmow7s38W77hzAgJAO50ztBLMcUyUBfxv1HC+LGwtiEN+Co6LtlqT/5uwVOxsD4TNIilWhwdQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ext-name/-/ext-name-5.0.0.tgz}
    name: ext-name
    version: 5.0.0
    engines: {node: '>=4'}
    dependencies:
      ext-list: registry.npmmirror.com/ext-list@2.2.2
      sort-keys-length: registry.npmmirror.com/sort-keys-length@1.0.1
    dev: true

  registry.npmmirror.com/extend-shallow@2.0.1:
    resolution: {integrity: sha512-zCnTtlxNoAiDc3gqY2aYAWFx7XWWiasuF2K8Me5WbN8otHKTUKBwjPtNpRs/rbUZm7KxWAaNj7P1a/p52GbVug==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/extend-shallow/-/extend-shallow-2.0.1.tgz}
    name: extend-shallow
    version: 2.0.1
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extendable: registry.npmmirror.com/is-extendable@0.1.1
    dev: false

  registry.npmmirror.com/external-editor@3.1.0:
    resolution: {integrity: sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/external-editor/-/external-editor-3.1.0.tgz}
    name: external-editor
    version: 3.1.0
    engines: {node: '>=4'}
    dependencies:
      chardet: registry.npmmirror.com/chardet@0.7.0
      iconv-lite: registry.npmmirror.com/iconv-lite@0.4.24
      tmp: registry.npmmirror.com/tmp@0.0.33
    dev: true

  registry.npmmirror.com/fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz}
    name: fast-deep-equal
    version: 3.1.3
    dev: true

  registry.npmmirror.com/fast-diff@1.3.0:
    resolution: {integrity: sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fast-diff/-/fast-diff-1.3.0.tgz}
    name: fast-diff
    version: 1.3.0
    dev: true

  registry.npmmirror.com/fast-fifo@1.3.2:
    resolution: {integrity: sha512-/d9sfos4yxzpwkDkuN7k2SqFKtYNmCTzgfEpz82x34IM9/zc8KGxQoXg1liNC/izpRM/MBdt44Nmx41ZWqk+FQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fast-fifo/-/fast-fifo-1.3.2.tgz}
    name: fast-fifo
    version: 1.3.2
    dev: true

  registry.npmmirror.com/fast-glob@3.3.3:
    resolution: {integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fast-glob/-/fast-glob-3.3.3.tgz}
    name: fast-glob
    version: 3.3.3
    engines: {node: '>=8.6.0'}
    dependencies:
      '@nodelib/fs.stat': registry.npmmirror.com/@nodelib/fs.stat@2.0.5
      '@nodelib/fs.walk': registry.npmmirror.com/@nodelib/fs.walk@1.2.8
      glob-parent: registry.npmmirror.com/glob-parent@5.1.2
      merge2: registry.npmmirror.com/merge2@1.4.1
      micromatch: registry.npmmirror.com/micromatch@4.0.8
    dev: true

  registry.npmmirror.com/fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz}
    name: fast-json-stable-stringify
    version: 2.1.0
    dev: true

  registry.npmmirror.com/fast-levenshtein@2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz}
    name: fast-levenshtein
    version: 2.0.6
    dev: true

  registry.npmmirror.com/fast-safe-stringify@2.1.1:
    resolution: {integrity: sha512-W+KJc2dmILlPplD/H4K9l9LcAHAfPtP6BY84uVLXQ6Evcz9Lcg33Y2z1IVblT6xdY54PXYVHEv+0Wpq8Io6zkA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fast-safe-stringify/-/fast-safe-stringify-2.1.1.tgz}
    name: fast-safe-stringify
    version: 2.1.1

  registry.npmmirror.com/fast-uri@3.0.6:
    resolution: {integrity: sha512-Atfo14OibSv5wAp4VWNsFYE1AchQRTv9cBGWET4pZWHzYshFSS9NQI6I57rdKn9croWVMbYFbLhJ+yJvmZIIHw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fast-uri/-/fast-uri-3.0.6.tgz}
    name: fast-uri
    version: 3.0.6
    dev: true

  registry.npmmirror.com/fastq@1.19.1:
    resolution: {integrity: sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fastq/-/fastq-1.19.1.tgz}
    name: fastq
    version: 1.19.1
    dependencies:
      reusify: registry.npmmirror.com/reusify@1.1.0
    dev: true

  registry.npmmirror.com/fb-watchman@2.0.2:
    resolution: {integrity: sha512-p5161BqbuCaSnB8jIbzQHOlpgsPmK5rJVDfDKO91Axs5NC1uu3HRQm6wt9cd9/+GtQQIO53JdGXXoyDpTAsgYA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fb-watchman/-/fb-watchman-2.0.2.tgz}
    name: fb-watchman
    version: 2.0.2
    dependencies:
      bser: registry.npmmirror.com/bser@2.1.1
    dev: true

  registry.npmmirror.com/file-entry-cache@8.0.0:
    resolution: {integrity: sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/file-entry-cache/-/file-entry-cache-8.0.0.tgz}
    name: file-entry-cache
    version: 8.0.0
    engines: {node: '>=16.0.0'}
    dependencies:
      flat-cache: registry.npmmirror.com/flat-cache@4.0.1
    dev: true

  registry.npmmirror.com/file-type@19.6.0:
    resolution: {integrity: sha512-VZR5I7k5wkD0HgFnMsq5hOsSc710MJMu5Nc5QYsbe38NN5iPV/XTObYLc/cpttRTf6lX538+5uO1ZQRhYibiZQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/file-type/-/file-type-19.6.0.tgz}
    name: file-type
    version: 19.6.0
    engines: {node: '>=18'}
    dependencies:
      get-stream: registry.npmmirror.com/get-stream@9.0.1
      strtok3: registry.npmmirror.com/strtok3@9.1.1
      token-types: registry.npmmirror.com/token-types@6.0.3
      uint8array-extras: registry.npmmirror.com/uint8array-extras@1.4.0
    dev: true

  registry.npmmirror.com/filelist@1.0.4:
    resolution: {integrity: sha512-w1cEuf3S+DrLCQL7ET6kz+gmlJdbq9J7yXCSjK/OZCPA+qEN1WyF4ZAf0YYJa4/shHJra2t/d/r8SV4Ji+x+8Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/filelist/-/filelist-1.0.4.tgz}
    name: filelist
    version: 1.0.4
    dependencies:
      minimatch: registry.npmmirror.com/minimatch@5.1.6
    dev: true

  registry.npmmirror.com/filename-reserved-regex@3.0.0:
    resolution: {integrity: sha512-hn4cQfU6GOT/7cFHXBqeBg2TbrMBgdD0kcjLhvSQYYwm3s4B6cjvBfb7nBALJLAXqmU5xajSa7X2NnUud/VCdw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/filename-reserved-regex/-/filename-reserved-regex-3.0.0.tgz}
    name: filename-reserved-regex
    version: 3.0.0
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dev: true

  registry.npmmirror.com/filenamify@6.0.0:
    resolution: {integrity: sha512-vqIlNogKeyD3yzrm0yhRMQg8hOVwYcYRfjEoODd49iCprMn4HL85gK3HcykQE53EPIpX3HcAbGA5ELQv216dAQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/filenamify/-/filenamify-6.0.0.tgz}
    name: filenamify
    version: 6.0.0
    engines: {node: '>=16'}
    dependencies:
      filename-reserved-regex: registry.npmmirror.com/filename-reserved-regex@3.0.0
    dev: true

  registry.npmmirror.com/fill-range@7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fill-range/-/fill-range-7.1.1.tgz}
    name: fill-range
    version: 7.1.1
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: registry.npmmirror.com/to-regex-range@5.0.1
    dev: true

  registry.npmmirror.com/finalhandler@2.1.0:
    resolution: {integrity: sha512-/t88Ty3d5JWQbWYgaOGCCYfXRwV1+be02WqYYlL6h0lEiUAMPM8o8qKGO01YIkOHzka2up08wvgYD0mDiI+q3Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/finalhandler/-/finalhandler-2.1.0.tgz}
    name: finalhandler
    version: 2.1.0
    engines: {node: '>= 0.8'}
    dependencies:
      debug: registry.npmmirror.com/debug@4.4.1
      encodeurl: registry.npmmirror.com/encodeurl@2.0.0
      escape-html: registry.npmmirror.com/escape-html@1.0.3
      on-finished: registry.npmmirror.com/on-finished@2.4.1
      parseurl: registry.npmmirror.com/parseurl@1.3.3
      statuses: registry.npmmirror.com/statuses@2.0.1
    transitivePeerDependencies:
      - supports-color

  registry.npmmirror.com/find-up@4.1.0:
    resolution: {integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/find-up/-/find-up-4.1.0.tgz}
    name: find-up
    version: 4.1.0
    engines: {node: '>=8'}
    dependencies:
      locate-path: registry.npmmirror.com/locate-path@5.0.0
      path-exists: registry.npmmirror.com/path-exists@4.0.0
    dev: true

  registry.npmmirror.com/find-up@5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/find-up/-/find-up-5.0.0.tgz}
    name: find-up
    version: 5.0.0
    engines: {node: '>=10'}
    dependencies:
      locate-path: registry.npmmirror.com/locate-path@6.0.0
      path-exists: registry.npmmirror.com/path-exists@4.0.0
    dev: true

  registry.npmmirror.com/find-versions@5.1.0:
    resolution: {integrity: sha512-+iwzCJ7C5v5KgcBuueqVoNiHVoQpwiUK5XFLjf0affFTep+Wcw93tPvmb8tqujDNmzhBDPddnWV/qgWSXgq+Hg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/find-versions/-/find-versions-5.1.0.tgz}
    name: find-versions
    version: 5.1.0
    engines: {node: '>=12'}
    dependencies:
      semver-regex: registry.npmmirror.com/semver-regex@4.0.5
    dev: true

  registry.npmmirror.com/flat-cache@4.0.1:
    resolution: {integrity: sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/flat-cache/-/flat-cache-4.0.1.tgz}
    name: flat-cache
    version: 4.0.1
    engines: {node: '>=16'}
    dependencies:
      flatted: registry.npmmirror.com/flatted@3.3.3
      keyv: registry.npmmirror.com/keyv@4.5.4
    dev: true

  registry.npmmirror.com/flatted@3.3.3:
    resolution: {integrity: sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/flatted/-/flatted-3.3.3.tgz}
    name: flatted
    version: 3.3.3
    dev: true

  registry.npmmirror.com/foreground-child@3.3.1:
    resolution: {integrity: sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/foreground-child/-/foreground-child-3.3.1.tgz}
    name: foreground-child
    version: 3.3.1
    engines: {node: '>=14'}
    dependencies:
      cross-spawn: registry.npmmirror.com/cross-spawn@7.0.6
      signal-exit: registry.npmmirror.com/signal-exit@4.1.0

  registry.npmmirror.com/fork-ts-checker-webpack-plugin@9.0.2(typescript@5.7.3)(webpack@5.97.1):
    resolution: {integrity: sha512-Uochze2R8peoN1XqlSi/rGUkDQpRogtLFocP9+PGu68zk1BDAKXfdeCdyVZpgTk8V8WFVQXdEz426VKjXLO1Gg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fork-ts-checker-webpack-plugin/-/fork-ts-checker-webpack-plugin-9.0.2.tgz}
    id: registry.npmmirror.com/fork-ts-checker-webpack-plugin/9.0.2
    name: fork-ts-checker-webpack-plugin
    version: 9.0.2
    engines: {node: '>=12.13.0', yarn: '>=1.0.0'}
    peerDependencies:
      typescript: '>3.6.0'
      webpack: ^5.11.0
    dependencies:
      '@babel/code-frame': registry.npmmirror.com/@babel/code-frame@7.27.1
      chalk: registry.npmmirror.com/chalk@4.1.2
      chokidar: registry.npmmirror.com/chokidar@3.6.0
      cosmiconfig: registry.npmmirror.com/cosmiconfig@8.3.6(typescript@5.7.3)
      deepmerge: registry.npmmirror.com/deepmerge@4.3.1
      fs-extra: registry.npmmirror.com/fs-extra@10.1.0
      memfs: registry.npmmirror.com/memfs@3.5.3
      minimatch: registry.npmmirror.com/minimatch@3.1.2
      node-abort-controller: registry.npmmirror.com/node-abort-controller@3.1.1
      schema-utils: registry.npmmirror.com/schema-utils@3.3.0
      semver: registry.npmmirror.com/semver@7.7.2
      tapable: registry.npmmirror.com/tapable@2.2.2
      typescript: registry.npmmirror.com/typescript@5.7.3
      webpack: registry.npmmirror.com/webpack@5.97.1(@swc/core@1.10.7)
    dev: true

  registry.npmmirror.com/form-data-encoder@2.1.4:
    resolution: {integrity: sha512-yDYSgNMraqvnxiEXO4hi88+YZxaHC6QKzb5N84iRCTDeRO7ZALpir/lVmf/uXUhnwUr2O4HU8s/n6x+yNjQkHw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/form-data-encoder/-/form-data-encoder-2.1.4.tgz}
    name: form-data-encoder
    version: 2.1.4
    engines: {node: '>= 14.17'}
    dev: true

  registry.npmmirror.com/form-data@4.0.3:
    resolution: {integrity: sha512-qsITQPfmvMOSAdeyZ+12I1c+CKSstAFAwu+97zrnWAbIr5u8wfsExUzCesVLC8NgHuRUqNN4Zy6UPWUTRGslcA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/form-data/-/form-data-4.0.3.tgz}
    name: form-data
    version: 4.0.3
    engines: {node: '>= 6'}
    dependencies:
      asynckit: registry.npmmirror.com/asynckit@0.4.0
      combined-stream: registry.npmmirror.com/combined-stream@1.0.8
      es-set-tostringtag: registry.npmmirror.com/es-set-tostringtag@2.1.0
      hasown: registry.npmmirror.com/hasown@2.0.2
      mime-types: registry.npmmirror.com/mime-types@2.1.35

  registry.npmmirror.com/formidable@3.5.4:
    resolution: {integrity: sha512-YikH+7CUTOtP44ZTnUhR7Ic2UASBPOqmaRkRKxRbywPTe5VxF7RRCck4af9wutiZ/QKM5nME9Bie2fFaPz5Gug==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/formidable/-/formidable-3.5.4.tgz}
    name: formidable
    version: 3.5.4
    engines: {node: '>=14.0.0'}
    dependencies:
      '@paralleldrive/cuid2': registry.npmmirror.com/@paralleldrive/cuid2@2.2.2
      dezalgo: registry.npmmirror.com/dezalgo@1.0.4
      once: registry.npmmirror.com/once@1.4.0
    dev: true

  registry.npmmirror.com/formstream@1.5.1:
    resolution: {integrity: sha512-q7ORzFqotpwn3Y/GBK2lK7PjtZZwJHz9QE9Phv8zb5IrL9ftGLyi2zjGURON3voK8TaZ+mqJKERYN4lrHYTkUQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/formstream/-/formstream-1.5.1.tgz}
    name: formstream
    version: 1.5.1
    dependencies:
      destroy: registry.npmmirror.com/destroy@1.2.0
      mime: registry.npmmirror.com/mime@2.6.0
      node-hex: registry.npmmirror.com/node-hex@1.0.1
      pause-stream: registry.npmmirror.com/pause-stream@0.0.11
    dev: false

  registry.npmmirror.com/forwarded@0.2.0:
    resolution: {integrity: sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/forwarded/-/forwarded-0.2.0.tgz}
    name: forwarded
    version: 0.2.0
    engines: {node: '>= 0.6'}

  registry.npmmirror.com/fresh@2.0.0:
    resolution: {integrity: sha512-Rx/WycZ60HOaqLKAi6cHRKKI7zxWbJ31MhntmtwMoaTeF7XFH9hhBp8vITaMidfljRQ6eYWCKkaTK+ykVJHP2A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fresh/-/fresh-2.0.0.tgz}
    name: fresh
    version: 2.0.0
    engines: {node: '>= 0.8'}

  registry.npmmirror.com/fs-extra@10.1.0:
    resolution: {integrity: sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fs-extra/-/fs-extra-10.1.0.tgz}
    name: fs-extra
    version: 10.1.0
    engines: {node: '>=12'}
    dependencies:
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
      jsonfile: registry.npmmirror.com/jsonfile@6.1.0
      universalify: registry.npmmirror.com/universalify@2.0.1
    dev: true

  registry.npmmirror.com/fs-monkey@1.0.6:
    resolution: {integrity: sha512-b1FMfwetIKymC0eioW7mTywihSQE4oLzQn1dB6rZB5fx/3NpNEdAWeCSMB+60/AeT0TCXsxzAlcYVEFCTAksWg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fs-monkey/-/fs-monkey-1.0.6.tgz}
    name: fs-monkey
    version: 1.0.6
    dev: true

  registry.npmmirror.com/fs.realpath@1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fs.realpath/-/fs.realpath-1.0.0.tgz}
    name: fs.realpath
    version: 1.0.0
    dev: true

  registry.npmmirror.com/fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fsevents/-/fsevents-2.3.3.tgz}
    name: fsevents
    version: 2.3.3
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/function-bind/-/function-bind-1.1.2.tgz}
    name: function-bind
    version: 1.1.2

  registry.npmmirror.com/generate-function@2.3.1:
    resolution: {integrity: sha512-eeB5GfMNeevm/GRYq20ShmsaGcmI81kIX2K9XQx5miC8KdHaC6Jm0qQ8ZNeGOi7wYB8OsdxKs+Y2oVuTFuVwKQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/generate-function/-/generate-function-2.3.1.tgz}
    name: generate-function
    version: 2.3.1
    dependencies:
      is-property: registry.npmmirror.com/is-property@1.0.2
    dev: false

  registry.npmmirror.com/gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/gensync/-/gensync-1.0.0-beta.2.tgz}
    name: gensync
    version: 1.0.0-beta.2
    engines: {node: '>=6.9.0'}
    dev: true

  registry.npmmirror.com/get-caller-file@2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/get-caller-file/-/get-caller-file-2.0.5.tgz}
    name: get-caller-file
    version: 2.0.5
    engines: {node: 6.* || 8.* || >= 10.*}

  registry.npmmirror.com/get-intrinsic@1.3.0:
    resolution: {integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.3.0.tgz}
    name: get-intrinsic
    version: 1.3.0
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: registry.npmmirror.com/call-bind-apply-helpers@1.0.2
      es-define-property: registry.npmmirror.com/es-define-property@1.0.1
      es-errors: registry.npmmirror.com/es-errors@1.3.0
      es-object-atoms: registry.npmmirror.com/es-object-atoms@1.1.1
      function-bind: registry.npmmirror.com/function-bind@1.1.2
      get-proto: registry.npmmirror.com/get-proto@1.0.1
      gopd: registry.npmmirror.com/gopd@1.2.0
      has-symbols: registry.npmmirror.com/has-symbols@1.1.0
      hasown: registry.npmmirror.com/hasown@2.0.2
      math-intrinsics: registry.npmmirror.com/math-intrinsics@1.1.0

  registry.npmmirror.com/get-package-type@0.1.0:
    resolution: {integrity: sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/get-package-type/-/get-package-type-0.1.0.tgz}
    name: get-package-type
    version: 0.1.0
    engines: {node: '>=8.0.0'}
    dev: true

  registry.npmmirror.com/get-proto@1.0.1:
    resolution: {integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/get-proto/-/get-proto-1.0.1.tgz}
    name: get-proto
    version: 1.0.1
    engines: {node: '>= 0.4'}
    dependencies:
      dunder-proto: registry.npmmirror.com/dunder-proto@1.0.1
      es-object-atoms: registry.npmmirror.com/es-object-atoms@1.1.1

  registry.npmmirror.com/get-stream@6.0.1:
    resolution: {integrity: sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/get-stream/-/get-stream-6.0.1.tgz}
    name: get-stream
    version: 6.0.1
    engines: {node: '>=10'}
    dev: true

  registry.npmmirror.com/get-stream@9.0.1:
    resolution: {integrity: sha512-kVCxPF3vQM/N0B1PmoqVUqgHP+EeVjmZSQn+1oCRPxd2P21P2F19lIgbR3HBosbB1PUhOAoctJnfEn2GbN2eZA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/get-stream/-/get-stream-9.0.1.tgz}
    name: get-stream
    version: 9.0.1
    engines: {node: '>=18'}
    dependencies:
      '@sec-ant/readable-stream': registry.npmmirror.com/@sec-ant/readable-stream@0.4.1
      is-stream: registry.npmmirror.com/is-stream@4.0.1
    dev: true

  registry.npmmirror.com/glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz}
    name: glob-parent
    version: 5.1.2
    engines: {node: '>= 6'}
    dependencies:
      is-glob: registry.npmmirror.com/is-glob@4.0.3
    dev: true

  registry.npmmirror.com/glob-parent@6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/glob-parent/-/glob-parent-6.0.2.tgz}
    name: glob-parent
    version: 6.0.2
    engines: {node: '>=10.13.0'}
    dependencies:
      is-glob: registry.npmmirror.com/is-glob@4.0.3
    dev: true

  registry.npmmirror.com/glob-to-regexp@0.4.1:
    resolution: {integrity: sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz}
    name: glob-to-regexp
    version: 0.4.1
    dev: true

  registry.npmmirror.com/glob@10.4.5:
    resolution: {integrity: sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/glob/-/glob-10.4.5.tgz}
    name: glob
    version: 10.4.5
    hasBin: true
    dependencies:
      foreground-child: registry.npmmirror.com/foreground-child@3.3.1
      jackspeak: registry.npmmirror.com/jackspeak@3.4.3
      minimatch: registry.npmmirror.com/minimatch@9.0.5
      minipass: registry.npmmirror.com/minipass@7.1.2
      package-json-from-dist: registry.npmmirror.com/package-json-from-dist@1.0.1
      path-scurry: registry.npmmirror.com/path-scurry@1.11.1
    dev: false

  registry.npmmirror.com/glob@11.0.1:
    resolution: {integrity: sha512-zrQDm8XPnYEKawJScsnM0QzobJxlT/kHOOlRTio8IH/GrmxRE5fjllkzdaHclIuNjUQTJYH2xHNIGfdpJkDJUw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/glob/-/glob-11.0.1.tgz}
    name: glob
    version: 11.0.1
    engines: {node: 20 || >=22}
    hasBin: true
    dependencies:
      foreground-child: registry.npmmirror.com/foreground-child@3.3.1
      jackspeak: registry.npmmirror.com/jackspeak@4.1.1
      minimatch: registry.npmmirror.com/minimatch@10.0.3
      minipass: registry.npmmirror.com/minipass@7.1.2
      package-json-from-dist: registry.npmmirror.com/package-json-from-dist@1.0.1
      path-scurry: registry.npmmirror.com/path-scurry@2.0.0
    dev: true

  registry.npmmirror.com/glob@7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/glob/-/glob-7.2.3.tgz}
    name: glob
    version: 7.2.3
    deprecated: Glob versions prior to v9 are no longer supported
    dependencies:
      fs.realpath: registry.npmmirror.com/fs.realpath@1.0.0
      inflight: registry.npmmirror.com/inflight@1.0.6
      inherits: registry.npmmirror.com/inherits@2.0.4
      minimatch: registry.npmmirror.com/minimatch@3.1.2
      once: registry.npmmirror.com/once@1.4.0
      path-is-absolute: registry.npmmirror.com/path-is-absolute@1.0.1
    dev: true

  registry.npmmirror.com/globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/globals/-/globals-11.12.0.tgz}
    name: globals
    version: 11.12.0
    engines: {node: '>=4'}
    dev: true

  registry.npmmirror.com/globals@14.0.0:
    resolution: {integrity: sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/globals/-/globals-14.0.0.tgz}
    name: globals
    version: 14.0.0
    engines: {node: '>=18'}
    dev: true

  registry.npmmirror.com/globals@16.0.0:
    resolution: {integrity: sha512-iInW14XItCXET01CQFqudPOWP2jYMl7T+QRQT+UNcR/iQncN/F0UNpgd76iFkBPgNQb4+X3LV9tLJYzwh+Gl3A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/globals/-/globals-16.0.0.tgz}
    name: globals
    version: 16.0.0
    engines: {node: '>=18'}
    dev: true

  registry.npmmirror.com/gopd@1.2.0:
    resolution: {integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/gopd/-/gopd-1.2.0.tgz}
    name: gopd
    version: 1.2.0
    engines: {node: '>= 0.4'}

  registry.npmmirror.com/got@13.0.0:
    resolution: {integrity: sha512-XfBk1CxOOScDcMr9O1yKkNaQyy865NbYs+F7dr4H0LZMVgCj2Le59k6PqbNHoL5ToeaEQUYh6c6yMfVcc6SJxA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/got/-/got-13.0.0.tgz}
    name: got
    version: 13.0.0
    engines: {node: '>=16'}
    dependencies:
      '@sindresorhus/is': registry.npmmirror.com/@sindresorhus/is@5.6.0
      '@szmarczak/http-timer': registry.npmmirror.com/@szmarczak/http-timer@5.0.1
      cacheable-lookup: registry.npmmirror.com/cacheable-lookup@7.0.0
      cacheable-request: registry.npmmirror.com/cacheable-request@10.2.14
      decompress-response: registry.npmmirror.com/decompress-response@6.0.0
      form-data-encoder: registry.npmmirror.com/form-data-encoder@2.1.4
      get-stream: registry.npmmirror.com/get-stream@6.0.1
      http2-wrapper: registry.npmmirror.com/http2-wrapper@2.2.1
      lowercase-keys: registry.npmmirror.com/lowercase-keys@3.0.0
      p-cancelable: registry.npmmirror.com/p-cancelable@3.0.0
      responselike: registry.npmmirror.com/responselike@3.0.0
    dev: true

  registry.npmmirror.com/graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.11.tgz}
    name: graceful-fs
    version: 4.2.11
    dev: true

  registry.npmmirror.com/graphemer@1.4.0:
    resolution: {integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/graphemer/-/graphemer-1.4.0.tgz}
    name: graphemer
    version: 1.4.0
    dev: true

  registry.npmmirror.com/has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz}
    name: has-flag
    version: 4.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/has-own-prop@2.0.0:
    resolution: {integrity: sha512-Pq0h+hvsVm6dDEa8x82GnLSYHOzNDt7f0ddFa3FqcQlgzEiptPqL+XrOJNavjOzSYiYWIrgeVYYgGlLmnxwilQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/has-own-prop/-/has-own-prop-2.0.0.tgz}
    name: has-own-prop
    version: 2.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/has-symbols@1.1.0:
    resolution: {integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/has-symbols/-/has-symbols-1.1.0.tgz}
    name: has-symbols
    version: 1.1.0
    engines: {node: '>= 0.4'}

  registry.npmmirror.com/has-tostringtag@1.0.2:
    resolution: {integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/has-tostringtag/-/has-tostringtag-1.0.2.tgz}
    name: has-tostringtag
    version: 1.0.2
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: registry.npmmirror.com/has-symbols@1.1.0

  registry.npmmirror.com/hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/hasown/-/hasown-2.0.2.tgz}
    name: hasown
    version: 2.0.2
    engines: {node: '>= 0.4'}
    dependencies:
      function-bind: registry.npmmirror.com/function-bind@1.1.2

  registry.npmmirror.com/html-escaper@2.0.2:
    resolution: {integrity: sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/html-escaper/-/html-escaper-2.0.2.tgz}
    name: html-escaper
    version: 2.0.2
    dev: true

  registry.npmmirror.com/http-cache-semantics@4.2.0:
    resolution: {integrity: sha512-dTxcvPXqPvXBQpq5dUr6mEMJX4oIEFv6bwom3FDwKRDsuIjjJGANqhBuoAn9c1RQJIdAKav33ED65E2ys+87QQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/http-cache-semantics/-/http-cache-semantics-4.2.0.tgz}
    name: http-cache-semantics
    version: 4.2.0
    dev: true

  registry.npmmirror.com/http-errors@2.0.0:
    resolution: {integrity: sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/http-errors/-/http-errors-2.0.0.tgz}
    name: http-errors
    version: 2.0.0
    engines: {node: '>= 0.8'}
    dependencies:
      depd: registry.npmmirror.com/depd@2.0.0
      inherits: registry.npmmirror.com/inherits@2.0.4
      setprototypeof: registry.npmmirror.com/setprototypeof@1.2.0
      statuses: registry.npmmirror.com/statuses@2.0.1
      toidentifier: registry.npmmirror.com/toidentifier@1.0.1

  registry.npmmirror.com/http2-wrapper@2.2.1:
    resolution: {integrity: sha512-V5nVw1PAOgfI3Lmeaj2Exmeg7fenjhRUgz1lPSezy1CuhPYbgQtbQj4jZfEAEMlaL+vupsvhjqCyjzob0yxsmQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/http2-wrapper/-/http2-wrapper-2.2.1.tgz}
    name: http2-wrapper
    version: 2.2.1
    engines: {node: '>=10.19.0'}
    dependencies:
      quick-lru: registry.npmmirror.com/quick-lru@5.1.1
      resolve-alpn: registry.npmmirror.com/resolve-alpn@1.2.1
    dev: true

  registry.npmmirror.com/human-signals@2.1.0:
    resolution: {integrity: sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/human-signals/-/human-signals-2.1.0.tgz}
    name: human-signals
    version: 2.1.0
    engines: {node: '>=10.17.0'}
    dev: true

  registry.npmmirror.com/iconv-lite@0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.24.tgz}
    name: iconv-lite
    version: 0.4.24
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: registry.npmmirror.com/safer-buffer@2.1.2

  registry.npmmirror.com/iconv-lite@0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.6.3.tgz}
    name: iconv-lite
    version: 0.6.3
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: registry.npmmirror.com/safer-buffer@2.1.2

  registry.npmmirror.com/ieee754@1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ieee754/-/ieee754-1.2.1.tgz}
    name: ieee754
    version: 1.2.1

  registry.npmmirror.com/ignore@5.3.2:
    resolution: {integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ignore/-/ignore-5.3.2.tgz}
    name: ignore
    version: 5.3.2
    engines: {node: '>= 4'}
    dev: true

  registry.npmmirror.com/import-fresh@3.3.1:
    resolution: {integrity: sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/import-fresh/-/import-fresh-3.3.1.tgz}
    name: import-fresh
    version: 3.3.1
    engines: {node: '>=6'}
    dependencies:
      parent-module: registry.npmmirror.com/parent-module@1.0.1
      resolve-from: registry.npmmirror.com/resolve-from@4.0.0
    dev: true

  registry.npmmirror.com/import-local@3.2.0:
    resolution: {integrity: sha512-2SPlun1JUPWoM6t3F0dw0FkCF/jWY8kttcY4f599GLTSjh2OCuuhdTkJQsEcZzBqbXZGKMK2OqW1oZsjtf/gQA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/import-local/-/import-local-3.2.0.tgz}
    name: import-local
    version: 3.2.0
    engines: {node: '>=8'}
    hasBin: true
    dependencies:
      pkg-dir: registry.npmmirror.com/pkg-dir@4.2.0
      resolve-cwd: registry.npmmirror.com/resolve-cwd@3.0.0
    dev: true

  registry.npmmirror.com/imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/imurmurhash/-/imurmurhash-0.1.4.tgz}
    name: imurmurhash
    version: 0.1.4
    engines: {node: '>=0.8.19'}
    dev: true

  registry.npmmirror.com/inflight@1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/inflight/-/inflight-1.0.6.tgz}
    name: inflight
    version: 1.0.6
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
    dependencies:
      once: registry.npmmirror.com/once@1.4.0
      wrappy: registry.npmmirror.com/wrappy@1.0.2
    dev: true

  registry.npmmirror.com/inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz}
    name: inherits
    version: 2.0.4

  registry.npmmirror.com/inspect-with-kind@1.0.5:
    resolution: {integrity: sha512-MAQUJuIo7Xqk8EVNP+6d3CKq9c80hi4tjIbIAT6lmGW9W6WzlHiu9PS8uSuUYU+Do+j1baiFp3H25XEVxDIG2g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/inspect-with-kind/-/inspect-with-kind-1.0.5.tgz}
    name: inspect-with-kind
    version: 1.0.5
    dependencies:
      kind-of: registry.npmmirror.com/kind-of@6.0.3
    dev: true

  registry.npmmirror.com/ipaddr.js@1.9.1:
    resolution: {integrity: sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ipaddr.js/-/ipaddr.js-1.9.1.tgz}
    name: ipaddr.js
    version: 1.9.1
    engines: {node: '>= 0.10'}

  registry.npmmirror.com/is-arrayish@0.2.1:
    resolution: {integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-arrayish/-/is-arrayish-0.2.1.tgz}
    name: is-arrayish
    version: 0.2.1
    dev: true

  registry.npmmirror.com/is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-binary-path/-/is-binary-path-2.1.0.tgz}
    name: is-binary-path
    version: 2.1.0
    engines: {node: '>=8'}
    dependencies:
      binary-extensions: registry.npmmirror.com/binary-extensions@2.3.0
    dev: true

  registry.npmmirror.com/is-core-module@2.16.1:
    resolution: {integrity: sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-core-module/-/is-core-module-2.16.1.tgz}
    name: is-core-module
    version: 2.16.1
    engines: {node: '>= 0.4'}
    dependencies:
      hasown: registry.npmmirror.com/hasown@2.0.2
    dev: true

  registry.npmmirror.com/is-extendable@0.1.1:
    resolution: {integrity: sha512-5BMULNob1vgFX6EjQw5izWDxrecWK9AM72rugNr0TFldMOi0fj6Jk+zeKIt0xGj4cEfQIJth4w3OKWOJ4f+AFw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-extendable/-/is-extendable-0.1.1.tgz}
    name: is-extendable
    version: 0.1.1
    engines: {node: '>=0.10.0'}
    dev: false

  registry.npmmirror.com/is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-extglob/-/is-extglob-2.1.1.tgz}
    name: is-extglob
    version: 2.1.1
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz}
    name: is-fullwidth-code-point
    version: 3.0.0
    engines: {node: '>=8'}

  registry.npmmirror.com/is-generator-fn@2.1.0:
    resolution: {integrity: sha512-cTIB4yPYL/Grw0EaSzASzg6bBy9gqCofvWN8okThAYIxKJZC+udlRAmGbM0XLeniEJSs8uEgHPGuHSe1XsOLSQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-generator-fn/-/is-generator-fn-2.1.0.tgz}
    name: is-generator-fn
    version: 2.1.0
    engines: {node: '>=6'}
    dev: true

  registry.npmmirror.com/is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz}
    name: is-glob
    version: 4.0.3
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: registry.npmmirror.com/is-extglob@2.1.1
    dev: true

  registry.npmmirror.com/is-interactive@1.0.0:
    resolution: {integrity: sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-interactive/-/is-interactive-1.0.0.tgz}
    name: is-interactive
    version: 1.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz}
    name: is-number
    version: 7.0.0
    engines: {node: '>=0.12.0'}
    dev: true

  registry.npmmirror.com/is-plain-obj@1.1.0:
    resolution: {integrity: sha512-yvkRyxmFKEOQ4pNXCmJG5AEQNlXJS5LaONXo5/cLdTZdWvsZ1ioJEonLGAosKlMWE8lwUy/bJzMjcw8az73+Fg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-plain-obj/-/is-plain-obj-1.1.0.tgz}
    name: is-plain-obj
    version: 1.1.0
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/is-promise@4.0.0:
    resolution: {integrity: sha512-hvpoI6korhJMnej285dSg6nu1+e6uxs7zG3BYAm5byqDsgJNWwxzM6z6iZiAgQR4TJ30JmBTOwqZUw3WlyH3AQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-promise/-/is-promise-4.0.0.tgz}
    name: is-promise
    version: 4.0.0

  registry.npmmirror.com/is-property@1.0.2:
    resolution: {integrity: sha512-Ks/IoX00TtClbGQr4TWXemAnktAQvYB7HzcCxDGqEZU6oCmb2INHuOoKxbtR+HFkmYWBKv/dOZtGRiAjDhj92g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-property/-/is-property-1.0.2.tgz}
    name: is-property
    version: 1.0.2
    dev: false

  registry.npmmirror.com/is-stream@2.0.1:
    resolution: {integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-stream/-/is-stream-2.0.1.tgz}
    name: is-stream
    version: 2.0.1
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/is-stream@4.0.1:
    resolution: {integrity: sha512-Dnz92NInDqYckGEUJv689RbRiTSEHCQ7wOVeALbkOz999YpqT46yMRIGtSNl2iCL1waAZSx40+h59NV/EwzV/A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-stream/-/is-stream-4.0.1.tgz}
    name: is-stream
    version: 4.0.1
    engines: {node: '>=18'}
    dev: true

  registry.npmmirror.com/is-unicode-supported@0.1.0:
    resolution: {integrity: sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz}
    name: is-unicode-supported
    version: 0.1.0
    engines: {node: '>=10'}
    dev: true

  registry.npmmirror.com/isarray@1.0.0:
    resolution: {integrity: sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/isarray/-/isarray-1.0.0.tgz}
    name: isarray
    version: 1.0.0

  registry.npmmirror.com/isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/isexe/-/isexe-2.0.0.tgz}
    name: isexe
    version: 2.0.0

  registry.npmmirror.com/istanbul-lib-coverage@3.2.2:
    resolution: {integrity: sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz}
    name: istanbul-lib-coverage
    version: 3.2.2
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/istanbul-lib-instrument@5.2.1:
    resolution: {integrity: sha512-pzqtp31nLv/XFOzXGuvhCb8qhjmTVo5vjVk19XE4CRlSWz0KoeJ3bw9XsA7nOp9YBf4qHjwBxkDzKcME/J29Yg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz}
    name: istanbul-lib-instrument
    version: 5.2.1
    engines: {node: '>=8'}
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@babel/parser': registry.npmmirror.com/@babel/parser@7.27.7
      '@istanbuljs/schema': registry.npmmirror.com/@istanbuljs/schema@0.1.3
      istanbul-lib-coverage: registry.npmmirror.com/istanbul-lib-coverage@3.2.2
      semver: registry.npmmirror.com/semver@6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/istanbul-lib-instrument@6.0.3:
    resolution: {integrity: sha512-Vtgk7L/R2JHyyGW07spoFlB8/lpjiOLTjMdms6AFMraYt3BaJauod/NGrfnVG/y4Ix1JEuMRPDPEj2ua+zz1/Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.3.tgz}
    name: istanbul-lib-instrument
    version: 6.0.3
    engines: {node: '>=10'}
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@babel/parser': registry.npmmirror.com/@babel/parser@7.27.7
      '@istanbuljs/schema': registry.npmmirror.com/@istanbuljs/schema@0.1.3
      istanbul-lib-coverage: registry.npmmirror.com/istanbul-lib-coverage@3.2.2
      semver: registry.npmmirror.com/semver@7.7.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/istanbul-lib-report@3.0.1:
    resolution: {integrity: sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz}
    name: istanbul-lib-report
    version: 3.0.1
    engines: {node: '>=10'}
    dependencies:
      istanbul-lib-coverage: registry.npmmirror.com/istanbul-lib-coverage@3.2.2
      make-dir: registry.npmmirror.com/make-dir@4.0.0
      supports-color: registry.npmmirror.com/supports-color@7.2.0
    dev: true

  registry.npmmirror.com/istanbul-lib-source-maps@4.0.1:
    resolution: {integrity: sha512-n3s8EwkdFIJCG3BPKBYvskgXGoy88ARzvegkitk60NxRdwltLOTaH7CUiMRXvwYorl0Q712iEjcWB+fK/MrWVw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz}
    name: istanbul-lib-source-maps
    version: 4.0.1
    engines: {node: '>=10'}
    dependencies:
      debug: registry.npmmirror.com/debug@4.4.1
      istanbul-lib-coverage: registry.npmmirror.com/istanbul-lib-coverage@3.2.2
      source-map: registry.npmmirror.com/source-map@0.6.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/istanbul-reports@3.1.7:
    resolution: {integrity: sha512-BewmUXImeuRk2YY0PVbxgKAysvhRPUQE0h5QRM++nVWyubKGV0l8qQ5op8+B2DOmwSe63Jivj0BjkPQVf8fP5g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/istanbul-reports/-/istanbul-reports-3.1.7.tgz}
    name: istanbul-reports
    version: 3.1.7
    engines: {node: '>=8'}
    dependencies:
      html-escaper: registry.npmmirror.com/html-escaper@2.0.2
      istanbul-lib-report: registry.npmmirror.com/istanbul-lib-report@3.0.1
    dev: true

  registry.npmmirror.com/iterare@1.2.1:
    resolution: {integrity: sha512-RKYVTCjAnRthyJes037NX/IiqeidgN1xc3j1RjFfECFp28A1GVwK9nA+i0rJPaHqSZwygLzRnFlzUuHFoWWy+Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/iterare/-/iterare-1.2.1.tgz}
    name: iterare
    version: 1.2.1
    engines: {node: '>=6'}

  registry.npmmirror.com/jackspeak@3.4.3:
    resolution: {integrity: sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jackspeak/-/jackspeak-3.4.3.tgz}
    name: jackspeak
    version: 3.4.3
    dependencies:
      '@isaacs/cliui': registry.npmmirror.com/@isaacs/cliui@8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': registry.npmmirror.com/@pkgjs/parseargs@0.11.0
    dev: false

  registry.npmmirror.com/jackspeak@4.1.1:
    resolution: {integrity: sha512-zptv57P3GpL+O0I7VdMJNBZCu+BPHVQUk55Ft8/QCJjTVxrnJHuVuX/0Bl2A6/+2oyR/ZMEuFKwmzqqZ/U5nPQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jackspeak/-/jackspeak-4.1.1.tgz}
    name: jackspeak
    version: 4.1.1
    engines: {node: 20 || >=22}
    dependencies:
      '@isaacs/cliui': registry.npmmirror.com/@isaacs/cliui@8.0.2
    dev: true

  registry.npmmirror.com/jake@10.9.2:
    resolution: {integrity: sha512-2P4SQ0HrLQ+fw6llpLnOaGAvN2Zu6778SJMrCUwns4fOoG9ayrTiZk3VV8sCPkVZF8ab0zksVpS8FDY5pRCNBA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jake/-/jake-10.9.2.tgz}
    name: jake
    version: 10.9.2
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      async: registry.npmmirror.com/async@3.2.6
      chalk: registry.npmmirror.com/chalk@4.1.2
      filelist: registry.npmmirror.com/filelist@1.0.4
      minimatch: registry.npmmirror.com/minimatch@3.1.2
    dev: true

  registry.npmmirror.com/jest-changed-files@29.7.0:
    resolution: {integrity: sha512-fEArFiwf1BpQ+4bXSprcDc3/x4HSzL4al2tozwVpDFpsxALjLYdyiIK4e5Vz66GQJIbXJ82+35PtysofptNX2w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-changed-files/-/jest-changed-files-29.7.0.tgz}
    name: jest-changed-files
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      execa: registry.npmmirror.com/execa@5.1.1
      jest-util: registry.npmmirror.com/jest-util@29.7.0
      p-limit: registry.npmmirror.com/p-limit@3.1.0
    dev: true

  registry.npmmirror.com/jest-circus@29.7.0:
    resolution: {integrity: sha512-3E1nCMgipcTkCocFwM90XXQab9bS+GMsjdpmPrlelaxwD93Ad8iVEjX/vvHPdLPnFf+L40u+5+iutRdA1N9myw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-circus/-/jest-circus-29.7.0.tgz}
    name: jest-circus
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/environment': registry.npmmirror.com/@jest/environment@29.7.0
      '@jest/expect': registry.npmmirror.com/@jest/expect@29.7.0
      '@jest/test-result': registry.npmmirror.com/@jest/test-result@29.7.0
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      chalk: registry.npmmirror.com/chalk@4.1.2
      co: registry.npmmirror.com/co@4.6.0
      dedent: registry.npmmirror.com/dedent@1.6.0
      is-generator-fn: registry.npmmirror.com/is-generator-fn@2.1.0
      jest-each: registry.npmmirror.com/jest-each@29.7.0
      jest-matcher-utils: registry.npmmirror.com/jest-matcher-utils@29.7.0
      jest-message-util: registry.npmmirror.com/jest-message-util@29.7.0
      jest-runtime: registry.npmmirror.com/jest-runtime@29.7.0
      jest-snapshot: registry.npmmirror.com/jest-snapshot@29.7.0
      jest-util: registry.npmmirror.com/jest-util@29.7.0
      p-limit: registry.npmmirror.com/p-limit@3.1.0
      pretty-format: registry.npmmirror.com/pretty-format@29.7.0
      pure-rand: registry.npmmirror.com/pure-rand@6.1.0
      slash: registry.npmmirror.com/slash@3.0.0
      stack-utils: registry.npmmirror.com/stack-utils@2.0.6
    transitivePeerDependencies:
      - babel-plugin-macros
      - supports-color
    dev: true

  registry.npmmirror.com/jest-cli@29.7.0(@types/node@22.10.7)(ts-node@10.9.2):
    resolution: {integrity: sha512-OVVobw2IubN/GSYsxETi+gOe7Ka59EFMR/twOU3Jb2GnKKeMGJB5SGUUrEz3SFVmJASUdZUzy83sLNNQ2gZslg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-cli/-/jest-cli-29.7.0.tgz}
    id: registry.npmmirror.com/jest-cli/29.7.0
    name: jest-cli
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    hasBin: true
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true
    dependencies:
      '@jest/core': registry.npmmirror.com/@jest/core@29.7.0(ts-node@10.9.2)
      '@jest/test-result': registry.npmmirror.com/@jest/test-result@29.7.0
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      chalk: registry.npmmirror.com/chalk@4.1.2
      create-jest: registry.npmmirror.com/create-jest@29.7.0(@types/node@22.10.7)(ts-node@10.9.2)
      exit: registry.npmmirror.com/exit@0.1.2
      import-local: registry.npmmirror.com/import-local@3.2.0
      jest-config: registry.npmmirror.com/jest-config@29.7.0(@types/node@22.10.7)(ts-node@10.9.2)
      jest-util: registry.npmmirror.com/jest-util@29.7.0
      jest-validate: registry.npmmirror.com/jest-validate@29.7.0
      yargs: registry.npmmirror.com/yargs@17.7.2
    transitivePeerDependencies:
      - '@types/node'
      - babel-plugin-macros
      - supports-color
      - ts-node
    dev: true

  registry.npmmirror.com/jest-config@29.7.0(@types/node@22.10.7)(ts-node@10.9.2):
    resolution: {integrity: sha512-uXbpfeQ7R6TZBqI3/TxCU4q4ttk3u0PJeC+E0zbfSoSjq6bJ7buBPxzQPL0ifrkY4DNu4JUdk0ImlBUYi840eQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-config/-/jest-config-29.7.0.tgz}
    id: registry.npmmirror.com/jest-config/29.7.0
    name: jest-config
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      '@types/node': '*'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      '@types/node':
        optional: true
      ts-node:
        optional: true
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@jest/test-sequencer': registry.npmmirror.com/@jest/test-sequencer@29.7.0
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      babel-jest: registry.npmmirror.com/babel-jest@29.7.0(@babel/core@7.27.7)
      chalk: registry.npmmirror.com/chalk@4.1.2
      ci-info: registry.npmmirror.com/ci-info@3.9.0
      deepmerge: registry.npmmirror.com/deepmerge@4.3.1
      glob: registry.npmmirror.com/glob@7.2.3
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
      jest-circus: registry.npmmirror.com/jest-circus@29.7.0
      jest-environment-node: registry.npmmirror.com/jest-environment-node@29.7.0
      jest-get-type: registry.npmmirror.com/jest-get-type@29.6.3
      jest-regex-util: registry.npmmirror.com/jest-regex-util@29.6.3
      jest-resolve: registry.npmmirror.com/jest-resolve@29.7.0
      jest-runner: registry.npmmirror.com/jest-runner@29.7.0
      jest-util: registry.npmmirror.com/jest-util@29.7.0
      jest-validate: registry.npmmirror.com/jest-validate@29.7.0
      micromatch: registry.npmmirror.com/micromatch@4.0.8
      parse-json: registry.npmmirror.com/parse-json@5.2.0
      pretty-format: registry.npmmirror.com/pretty-format@29.7.0
      slash: registry.npmmirror.com/slash@3.0.0
      strip-json-comments: registry.npmmirror.com/strip-json-comments@3.1.1
      ts-node: registry.npmmirror.com/ts-node@10.9.2(@swc/core@1.10.7)(@types/node@22.10.7)(typescript@5.7.3)
    transitivePeerDependencies:
      - babel-plugin-macros
      - supports-color
    dev: true

  registry.npmmirror.com/jest-diff@29.7.0:
    resolution: {integrity: sha512-LMIgiIrhigmPrs03JHpxUh2yISK3vLFPkAodPeo0+BuF7wA2FoQbkEg1u8gBYBThncu7e1oEDUfIXVuTqLRUjw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-diff/-/jest-diff-29.7.0.tgz}
    name: jest-diff
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      chalk: registry.npmmirror.com/chalk@4.1.2
      diff-sequences: registry.npmmirror.com/diff-sequences@29.6.3
      jest-get-type: registry.npmmirror.com/jest-get-type@29.6.3
      pretty-format: registry.npmmirror.com/pretty-format@29.7.0
    dev: true

  registry.npmmirror.com/jest-docblock@29.7.0:
    resolution: {integrity: sha512-q617Auw3A612guyaFgsbFeYpNP5t2aoUNLwBUbc/0kD1R4t9ixDbyFTHd1nok4epoVFpr7PmeWHrhvuV3XaJ4g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-docblock/-/jest-docblock-29.7.0.tgz}
    name: jest-docblock
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      detect-newline: registry.npmmirror.com/detect-newline@3.1.0
    dev: true

  registry.npmmirror.com/jest-each@29.7.0:
    resolution: {integrity: sha512-gns+Er14+ZrEoC5fhOfYCY1LOHHr0TI+rQUHZS8Ttw2l7gl+80eHc/gFf2Ktkw0+SIACDTeWvpFcv3B04VembQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-each/-/jest-each-29.7.0.tgz}
    name: jest-each
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      chalk: registry.npmmirror.com/chalk@4.1.2
      jest-get-type: registry.npmmirror.com/jest-get-type@29.6.3
      jest-util: registry.npmmirror.com/jest-util@29.7.0
      pretty-format: registry.npmmirror.com/pretty-format@29.7.0
    dev: true

  registry.npmmirror.com/jest-environment-node@29.7.0:
    resolution: {integrity: sha512-DOSwCRqXirTOyheM+4d5YZOrWcdu0LNZ87ewUoywbcb2XR4wKgqiG8vNeYwhjFMbEkfju7wx2GYH0P2gevGvFw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-environment-node/-/jest-environment-node-29.7.0.tgz}
    name: jest-environment-node
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/environment': registry.npmmirror.com/@jest/environment@29.7.0
      '@jest/fake-timers': registry.npmmirror.com/@jest/fake-timers@29.7.0
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      jest-mock: registry.npmmirror.com/jest-mock@29.7.0
      jest-util: registry.npmmirror.com/jest-util@29.7.0
    dev: true

  registry.npmmirror.com/jest-get-type@29.6.3:
    resolution: {integrity: sha512-zrteXnqYxfQh7l5FHyL38jL39di8H8rHoecLH3JNxH3BwOrBsNeabdap5e0I23lD4HHI8W5VFBZqG4Eaq5LNcw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-get-type/-/jest-get-type-29.6.3.tgz}
    name: jest-get-type
    version: 29.6.3
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dev: true

  registry.npmmirror.com/jest-haste-map@29.7.0:
    resolution: {integrity: sha512-fP8u2pyfqx0K1rGn1R9pyE0/KTn+G7PxktWidOBTqFPLYX0b9ksaMFkhK5vrS3DVun09pckLdlx90QthlW7AmA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-haste-map/-/jest-haste-map-29.7.0.tgz}
    name: jest-haste-map
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      '@types/graceful-fs': registry.npmmirror.com/@types/graceful-fs@4.1.9
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      anymatch: registry.npmmirror.com/anymatch@3.1.3
      fb-watchman: registry.npmmirror.com/fb-watchman@2.0.2
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
      jest-regex-util: registry.npmmirror.com/jest-regex-util@29.6.3
      jest-util: registry.npmmirror.com/jest-util@29.7.0
      jest-worker: registry.npmmirror.com/jest-worker@29.7.0
      micromatch: registry.npmmirror.com/micromatch@4.0.8
      walker: registry.npmmirror.com/walker@1.0.8
    optionalDependencies:
      fsevents: registry.npmmirror.com/fsevents@2.3.3
    dev: true

  registry.npmmirror.com/jest-leak-detector@29.7.0:
    resolution: {integrity: sha512-kYA8IJcSYtST2BY9I+SMC32nDpBT3J2NvWJx8+JCuCdl/CR1I4EKUJROiP8XtCcxqgTTBGJNdbB1A8XRKbTetw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-leak-detector/-/jest-leak-detector-29.7.0.tgz}
    name: jest-leak-detector
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      jest-get-type: registry.npmmirror.com/jest-get-type@29.6.3
      pretty-format: registry.npmmirror.com/pretty-format@29.7.0
    dev: true

  registry.npmmirror.com/jest-matcher-utils@29.7.0:
    resolution: {integrity: sha512-sBkD+Xi9DtcChsI3L3u0+N0opgPYnCRPtGcQYrgXmR+hmt/fYfWAL0xRXYU8eWOdfuLgBe0YCW3AFtnRLagq/g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-matcher-utils/-/jest-matcher-utils-29.7.0.tgz}
    name: jest-matcher-utils
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      chalk: registry.npmmirror.com/chalk@4.1.2
      jest-diff: registry.npmmirror.com/jest-diff@29.7.0
      jest-get-type: registry.npmmirror.com/jest-get-type@29.6.3
      pretty-format: registry.npmmirror.com/pretty-format@29.7.0
    dev: true

  registry.npmmirror.com/jest-message-util@29.7.0:
    resolution: {integrity: sha512-GBEV4GRADeP+qtB2+6u61stea8mGcOT4mCtrYISZwfu9/ISHFJ/5zOMXYbpBE9RsS5+Gb63DW4FgmnKJ79Kf6w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-message-util/-/jest-message-util-29.7.0.tgz}
    name: jest-message-util
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@babel/code-frame': registry.npmmirror.com/@babel/code-frame@7.27.1
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      '@types/stack-utils': registry.npmmirror.com/@types/stack-utils@2.0.3
      chalk: registry.npmmirror.com/chalk@4.1.2
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
      micromatch: registry.npmmirror.com/micromatch@4.0.8
      pretty-format: registry.npmmirror.com/pretty-format@29.7.0
      slash: registry.npmmirror.com/slash@3.0.0
      stack-utils: registry.npmmirror.com/stack-utils@2.0.6
    dev: true

  registry.npmmirror.com/jest-mock@29.7.0:
    resolution: {integrity: sha512-ITOMZn+UkYS4ZFh83xYAOzWStloNzJFO2s8DWrE4lhtGD+AorgnbkiKERe4wQVBydIGPx059g6riW5Btp6Llnw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-mock/-/jest-mock-29.7.0.tgz}
    name: jest-mock
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      jest-util: registry.npmmirror.com/jest-util@29.7.0
    dev: true

  registry.npmmirror.com/jest-pnp-resolver@1.2.3(jest-resolve@29.7.0):
    resolution: {integrity: sha512-+3NpwQEnRoIBtx4fyhblQDPgJI0H1IEIkX7ShLUjPGA7TtUTvI1oiKi3SR4oBR0hQhQR80l4WAe5RrXBwWMA8w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz}
    id: registry.npmmirror.com/jest-pnp-resolver/1.2.3
    name: jest-pnp-resolver
    version: 1.2.3
    engines: {node: '>=6'}
    peerDependencies:
      jest-resolve: '*'
    peerDependenciesMeta:
      jest-resolve:
        optional: true
    dependencies:
      jest-resolve: registry.npmmirror.com/jest-resolve@29.7.0
    dev: true

  registry.npmmirror.com/jest-regex-util@29.6.3:
    resolution: {integrity: sha512-KJJBsRCyyLNWCNBOvZyRDnAIfUiRJ8v+hOBQYGn8gDyF3UegwiP4gwRR3/SDa42g1YbVycTidUF3rKjyLFDWbg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-regex-util/-/jest-regex-util-29.6.3.tgz}
    name: jest-regex-util
    version: 29.6.3
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dev: true

  registry.npmmirror.com/jest-resolve-dependencies@29.7.0:
    resolution: {integrity: sha512-un0zD/6qxJ+S0et7WxeI3H5XSe9lTBBR7bOHCHXkKR6luG5mwDDlIzVQ0V5cZCuoTgEdcdwzTghYkTWfubi+nA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-resolve-dependencies/-/jest-resolve-dependencies-29.7.0.tgz}
    name: jest-resolve-dependencies
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      jest-regex-util: registry.npmmirror.com/jest-regex-util@29.6.3
      jest-snapshot: registry.npmmirror.com/jest-snapshot@29.7.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/jest-resolve@29.7.0:
    resolution: {integrity: sha512-IOVhZSrg+UvVAshDSDtHyFCCBUl/Q3AAJv8iZ6ZjnZ74xzvwuzLXid9IIIPgTnY62SJjfuupMKZsZQRsCvxEgA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-resolve/-/jest-resolve-29.7.0.tgz}
    name: jest-resolve
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      chalk: registry.npmmirror.com/chalk@4.1.2
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
      jest-haste-map: registry.npmmirror.com/jest-haste-map@29.7.0
      jest-pnp-resolver: registry.npmmirror.com/jest-pnp-resolver@1.2.3(jest-resolve@29.7.0)
      jest-util: registry.npmmirror.com/jest-util@29.7.0
      jest-validate: registry.npmmirror.com/jest-validate@29.7.0
      resolve: registry.npmmirror.com/resolve@1.22.10
      resolve.exports: registry.npmmirror.com/resolve.exports@2.0.3
      slash: registry.npmmirror.com/slash@3.0.0
    dev: true

  registry.npmmirror.com/jest-runner@29.7.0:
    resolution: {integrity: sha512-fsc4N6cPCAahybGBfTRcq5wFR6fpLznMg47sY5aDpsoejOcVYFb07AHuSnR0liMcPTgBsA3ZJL6kFOjPdoNipQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-runner/-/jest-runner-29.7.0.tgz}
    name: jest-runner
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/console': registry.npmmirror.com/@jest/console@29.7.0
      '@jest/environment': registry.npmmirror.com/@jest/environment@29.7.0
      '@jest/test-result': registry.npmmirror.com/@jest/test-result@29.7.0
      '@jest/transform': registry.npmmirror.com/@jest/transform@29.7.0
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      chalk: registry.npmmirror.com/chalk@4.1.2
      emittery: registry.npmmirror.com/emittery@0.13.1
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
      jest-docblock: registry.npmmirror.com/jest-docblock@29.7.0
      jest-environment-node: registry.npmmirror.com/jest-environment-node@29.7.0
      jest-haste-map: registry.npmmirror.com/jest-haste-map@29.7.0
      jest-leak-detector: registry.npmmirror.com/jest-leak-detector@29.7.0
      jest-message-util: registry.npmmirror.com/jest-message-util@29.7.0
      jest-resolve: registry.npmmirror.com/jest-resolve@29.7.0
      jest-runtime: registry.npmmirror.com/jest-runtime@29.7.0
      jest-util: registry.npmmirror.com/jest-util@29.7.0
      jest-watcher: registry.npmmirror.com/jest-watcher@29.7.0
      jest-worker: registry.npmmirror.com/jest-worker@29.7.0
      p-limit: registry.npmmirror.com/p-limit@3.1.0
      source-map-support: registry.npmmirror.com/source-map-support@0.5.13
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/jest-runtime@29.7.0:
    resolution: {integrity: sha512-gUnLjgwdGqW7B4LvOIkbKs9WGbn+QLqRQQ9juC6HndeDiezIwhDP+mhMwHWCEcfQ5RUXa6OPnFF8BJh5xegwwQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-runtime/-/jest-runtime-29.7.0.tgz}
    name: jest-runtime
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/environment': registry.npmmirror.com/@jest/environment@29.7.0
      '@jest/fake-timers': registry.npmmirror.com/@jest/fake-timers@29.7.0
      '@jest/globals': registry.npmmirror.com/@jest/globals@29.7.0
      '@jest/source-map': registry.npmmirror.com/@jest/source-map@29.6.3
      '@jest/test-result': registry.npmmirror.com/@jest/test-result@29.7.0
      '@jest/transform': registry.npmmirror.com/@jest/transform@29.7.0
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      chalk: registry.npmmirror.com/chalk@4.1.2
      cjs-module-lexer: registry.npmmirror.com/cjs-module-lexer@1.4.3
      collect-v8-coverage: registry.npmmirror.com/collect-v8-coverage@1.0.2
      glob: registry.npmmirror.com/glob@7.2.3
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
      jest-haste-map: registry.npmmirror.com/jest-haste-map@29.7.0
      jest-message-util: registry.npmmirror.com/jest-message-util@29.7.0
      jest-mock: registry.npmmirror.com/jest-mock@29.7.0
      jest-regex-util: registry.npmmirror.com/jest-regex-util@29.6.3
      jest-resolve: registry.npmmirror.com/jest-resolve@29.7.0
      jest-snapshot: registry.npmmirror.com/jest-snapshot@29.7.0
      jest-util: registry.npmmirror.com/jest-util@29.7.0
      slash: registry.npmmirror.com/slash@3.0.0
      strip-bom: registry.npmmirror.com/strip-bom@4.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/jest-snapshot@29.7.0:
    resolution: {integrity: sha512-Rm0BMWtxBcioHr1/OX5YCP8Uov4riHvKPknOGs804Zg9JGZgmIBkbtlxJC/7Z4msKYVbIJtfU+tKb8xlYNfdkw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-snapshot/-/jest-snapshot-29.7.0.tgz}
    name: jest-snapshot
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      '@babel/generator': registry.npmmirror.com/@babel/generator@7.27.5
      '@babel/plugin-syntax-jsx': registry.npmmirror.com/@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.27.7)
      '@babel/plugin-syntax-typescript': registry.npmmirror.com/@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.27.7)
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.7
      '@jest/expect-utils': registry.npmmirror.com/@jest/expect-utils@29.7.0
      '@jest/transform': registry.npmmirror.com/@jest/transform@29.7.0
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      babel-preset-current-node-syntax: registry.npmmirror.com/babel-preset-current-node-syntax@1.1.0(@babel/core@7.27.7)
      chalk: registry.npmmirror.com/chalk@4.1.2
      expect: registry.npmmirror.com/expect@29.7.0
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
      jest-diff: registry.npmmirror.com/jest-diff@29.7.0
      jest-get-type: registry.npmmirror.com/jest-get-type@29.6.3
      jest-matcher-utils: registry.npmmirror.com/jest-matcher-utils@29.7.0
      jest-message-util: registry.npmmirror.com/jest-message-util@29.7.0
      jest-util: registry.npmmirror.com/jest-util@29.7.0
      natural-compare: registry.npmmirror.com/natural-compare@1.4.0
      pretty-format: registry.npmmirror.com/pretty-format@29.7.0
      semver: registry.npmmirror.com/semver@7.7.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/jest-util@29.7.0:
    resolution: {integrity: sha512-z6EbKajIpqGKU56y5KBUgy1dt1ihhQJgWzUlZHArA/+X2ad7Cb5iF+AK1EWVL/Bo7Rz9uurpqw6SiBCefUbCGA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-util/-/jest-util-29.7.0.tgz}
    name: jest-util
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      chalk: registry.npmmirror.com/chalk@4.1.2
      ci-info: registry.npmmirror.com/ci-info@3.9.0
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
      picomatch: registry.npmmirror.com/picomatch@2.3.1
    dev: true

  registry.npmmirror.com/jest-validate@29.7.0:
    resolution: {integrity: sha512-ZB7wHqaRGVw/9hST/OuFUReG7M8vKeq0/J2egIGLdvjHCmYqGARhzXmtgi+gVeZ5uXFF219aOc3Ls2yLg27tkw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-validate/-/jest-validate-29.7.0.tgz}
    name: jest-validate
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      camelcase: registry.npmmirror.com/camelcase@6.3.0
      chalk: registry.npmmirror.com/chalk@4.1.2
      jest-get-type: registry.npmmirror.com/jest-get-type@29.6.3
      leven: registry.npmmirror.com/leven@3.1.0
      pretty-format: registry.npmmirror.com/pretty-format@29.7.0
    dev: true

  registry.npmmirror.com/jest-watcher@29.7.0:
    resolution: {integrity: sha512-49Fg7WXkU3Vl2h6LbLtMQ/HyB6rXSIX7SqvBLQmssRBGN9I0PNvPmAmCWSOY6SOvrjhI/F7/bGAv9RtnsPA03g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-watcher/-/jest-watcher-29.7.0.tgz}
    name: jest-watcher
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/test-result': registry.npmmirror.com/@jest/test-result@29.7.0
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      ansi-escapes: registry.npmmirror.com/ansi-escapes@4.3.2
      chalk: registry.npmmirror.com/chalk@4.1.2
      emittery: registry.npmmirror.com/emittery@0.13.1
      jest-util: registry.npmmirror.com/jest-util@29.7.0
      string-length: registry.npmmirror.com/string-length@4.0.2
    dev: true

  registry.npmmirror.com/jest-worker@27.5.1:
    resolution: {integrity: sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-worker/-/jest-worker-27.5.1.tgz}
    name: jest-worker
    version: 27.5.1
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      merge-stream: registry.npmmirror.com/merge-stream@2.0.0
      supports-color: registry.npmmirror.com/supports-color@8.1.1
    dev: true

  registry.npmmirror.com/jest-worker@29.7.0:
    resolution: {integrity: sha512-eIz2msL/EzL9UFTFFx7jBTkeZfku0yUAyZZZmJ93H2TYEiroIx2PQjEXcwYtYl8zXCxb+PAmA2hLIt/6ZEkPHw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest-worker/-/jest-worker-29.7.0.tgz}
    name: jest-worker
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      jest-util: registry.npmmirror.com/jest-util@29.7.0
      merge-stream: registry.npmmirror.com/merge-stream@2.0.0
      supports-color: registry.npmmirror.com/supports-color@8.1.1
    dev: true

  registry.npmmirror.com/jest@29.7.0(@types/node@22.10.7)(ts-node@10.9.2):
    resolution: {integrity: sha512-NIy3oAFp9shda19hy4HK0HRTWKtPJmGdnvywu01nOqNC2vZg+Z+fvJDxpMQA88eb2I9EcafcdjYgsDthnYTvGw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jest/-/jest-29.7.0.tgz}
    id: registry.npmmirror.com/jest/29.7.0
    name: jest
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    hasBin: true
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true
    dependencies:
      '@jest/core': registry.npmmirror.com/@jest/core@29.7.0(ts-node@10.9.2)
      '@jest/types': registry.npmmirror.com/@jest/types@29.6.3
      import-local: registry.npmmirror.com/import-local@3.2.0
      jest-cli: registry.npmmirror.com/jest-cli@29.7.0(@types/node@22.10.7)(ts-node@10.9.2)
    transitivePeerDependencies:
      - '@types/node'
      - babel-plugin-macros
      - supports-color
      - ts-node
    dev: true

  registry.npmmirror.com/js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/js-tokens/-/js-tokens-4.0.0.tgz}
    name: js-tokens
    version: 4.0.0
    dev: true

  registry.npmmirror.com/js-yaml@3.14.1:
    resolution: {integrity: sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/js-yaml/-/js-yaml-3.14.1.tgz}
    name: js-yaml
    version: 3.14.1
    hasBin: true
    dependencies:
      argparse: registry.npmmirror.com/argparse@1.0.10
      esprima: registry.npmmirror.com/esprima@4.0.1
    dev: true

  registry.npmmirror.com/js-yaml@4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/js-yaml/-/js-yaml-4.1.0.tgz}
    name: js-yaml
    version: 4.1.0
    hasBin: true
    dependencies:
      argparse: registry.npmmirror.com/argparse@2.0.1

  registry.npmmirror.com/jsesc@3.1.0:
    resolution: {integrity: sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jsesc/-/jsesc-3.1.0.tgz}
    name: jsesc
    version: 3.1.0
    engines: {node: '>=6'}
    hasBin: true
    dev: true

  registry.npmmirror.com/json-buffer@3.0.1:
    resolution: {integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/json-buffer/-/json-buffer-3.0.1.tgz}
    name: json-buffer
    version: 3.0.1
    dev: true

  registry.npmmirror.com/json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz}
    name: json-parse-even-better-errors
    version: 2.3.1
    dev: true

  registry.npmmirror.com/json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz}
    name: json-schema-traverse
    version: 0.4.1
    dev: true

  registry.npmmirror.com/json-schema-traverse@1.0.0:
    resolution: {integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz}
    name: json-schema-traverse
    version: 1.0.0
    dev: true

  registry.npmmirror.com/json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz}
    name: json-stable-stringify-without-jsonify
    version: 1.0.1
    dev: true

  registry.npmmirror.com/json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/json5/-/json5-2.2.3.tgz}
    name: json5
    version: 2.2.3
    engines: {node: '>=6'}
    hasBin: true
    dev: true

  registry.npmmirror.com/jsonc-parser@3.3.1:
    resolution: {integrity: sha512-HUgH65KyejrUFPvHFPbqOY0rsFip3Bo5wb4ngvdi1EpCYWUQDC5V+Y7mZws+DLkr4M//zQJoanu1SP+87Dv1oQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jsonc-parser/-/jsonc-parser-3.3.1.tgz}
    name: jsonc-parser
    version: 3.3.1
    dev: true

  registry.npmmirror.com/jsonfile@6.1.0:
    resolution: {integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jsonfile/-/jsonfile-6.1.0.tgz}
    name: jsonfile
    version: 6.1.0
    dependencies:
      universalify: registry.npmmirror.com/universalify@2.0.1
    optionalDependencies:
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
    dev: true

  registry.npmmirror.com/jsonwebtoken@9.0.2:
    resolution: {integrity: sha512-PRp66vJ865SSqOlgqS8hujT5U4AOgMfhrwYIuIhfKaoSCZcirrmASQr8CX7cUg+RMih+hgznrjp99o+W4pJLHQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-9.0.2.tgz}
    name: jsonwebtoken
    version: 9.0.2
    engines: {node: '>=12', npm: '>=6'}
    dependencies:
      jws: registry.npmmirror.com/jws@3.2.2
      lodash.includes: registry.npmmirror.com/lodash.includes@4.3.0
      lodash.isboolean: registry.npmmirror.com/lodash.isboolean@3.0.3
      lodash.isinteger: registry.npmmirror.com/lodash.isinteger@4.0.4
      lodash.isnumber: registry.npmmirror.com/lodash.isnumber@3.0.3
      lodash.isplainobject: registry.npmmirror.com/lodash.isplainobject@4.0.6
      lodash.isstring: registry.npmmirror.com/lodash.isstring@4.0.1
      lodash.once: registry.npmmirror.com/lodash.once@4.1.1
      ms: registry.npmmirror.com/ms@2.1.3
      semver: registry.npmmirror.com/semver@7.7.2
    dev: false

  registry.npmmirror.com/jwa@1.4.2:
    resolution: {integrity: sha512-eeH5JO+21J78qMvTIDdBXidBd6nG2kZjg5Ohz/1fpa28Z4CcsWUzJ1ZZyFq/3z3N17aZy+ZuBoHljASbL1WfOw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jwa/-/jwa-1.4.2.tgz}
    name: jwa
    version: 1.4.2
    dependencies:
      buffer-equal-constant-time: registry.npmmirror.com/buffer-equal-constant-time@1.0.1
      ecdsa-sig-formatter: registry.npmmirror.com/ecdsa-sig-formatter@1.0.11
      safe-buffer: registry.npmmirror.com/safe-buffer@5.2.1
    dev: false

  registry.npmmirror.com/jws@3.2.2:
    resolution: {integrity: sha512-YHlZCB6lMTllWDtSPHz/ZXTsi8S00usEV6v1tjq8tOUZzw7DpSDWVXjXDre6ed1w/pd495ODpHZYSdkRTsa0HA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jws/-/jws-3.2.2.tgz}
    name: jws
    version: 3.2.2
    dependencies:
      jwa: registry.npmmirror.com/jwa@1.4.2
      safe-buffer: registry.npmmirror.com/safe-buffer@5.2.1
    dev: false

  registry.npmmirror.com/keyv@4.5.4:
    resolution: {integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/keyv/-/keyv-4.5.4.tgz}
    name: keyv
    version: 4.5.4
    dependencies:
      json-buffer: registry.npmmirror.com/json-buffer@3.0.1
    dev: true

  registry.npmmirror.com/kind-of@6.0.3:
    resolution: {integrity: sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/kind-of/-/kind-of-6.0.3.tgz}
    name: kind-of
    version: 6.0.3
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/kleur@3.0.3:
    resolution: {integrity: sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/kleur/-/kleur-3.0.3.tgz}
    name: kleur
    version: 3.0.3
    engines: {node: '>=6'}
    dev: true

  registry.npmmirror.com/leven@3.1.0:
    resolution: {integrity: sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/leven/-/leven-3.1.0.tgz}
    name: leven
    version: 3.1.0
    engines: {node: '>=6'}
    dev: true

  registry.npmmirror.com/levn@0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/levn/-/levn-0.4.1.tgz}
    name: levn
    version: 0.4.1
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: registry.npmmirror.com/prelude-ls@1.2.1
      type-check: registry.npmmirror.com/type-check@0.4.0
    dev: true

  registry.npmmirror.com/libphonenumber-js@1.12.9:
    resolution: {integrity: sha512-VWwAdNeJgN7jFOD+wN4qx83DTPMVPPAUyx9/TUkBXKLiNkuWWk6anV0439tgdtwaJDrEdqkvdN22iA6J4bUCZg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/libphonenumber-js/-/libphonenumber-js-1.12.9.tgz}
    name: libphonenumber-js
    version: 1.12.9

  registry.npmmirror.com/lines-and-columns@1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lines-and-columns/-/lines-and-columns-1.2.4.tgz}
    name: lines-and-columns
    version: 1.2.4
    dev: true

  registry.npmmirror.com/loader-runner@4.3.0:
    resolution: {integrity: sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/loader-runner/-/loader-runner-4.3.0.tgz}
    name: loader-runner
    version: 4.3.0
    engines: {node: '>=6.11.5'}
    dev: true

  registry.npmmirror.com/locate-path@5.0.0:
    resolution: {integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/locate-path/-/locate-path-5.0.0.tgz}
    name: locate-path
    version: 5.0.0
    engines: {node: '>=8'}
    dependencies:
      p-locate: registry.npmmirror.com/p-locate@4.1.0
    dev: true

  registry.npmmirror.com/locate-path@6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/locate-path/-/locate-path-6.0.0.tgz}
    name: locate-path
    version: 6.0.0
    engines: {node: '>=10'}
    dependencies:
      p-locate: registry.npmmirror.com/p-locate@5.0.0
    dev: true

  registry.npmmirror.com/lodash.includes@4.3.0:
    resolution: {integrity: sha512-W3Bx6mdkRTGtlJISOvVD/lbqjTlPPUDTMnlXZFnVwi9NKJ6tiAk6LVdlhZMm17VZisqhKcgzpO5Wz91PCt5b0w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lodash.includes/-/lodash.includes-4.3.0.tgz}
    name: lodash.includes
    version: 4.3.0
    dev: false

  registry.npmmirror.com/lodash.isboolean@3.0.3:
    resolution: {integrity: sha512-Bz5mupy2SVbPHURB98VAcw+aHh4vRV5IPNhILUCsOzRmsTmSQ17jIuqopAentWoehktxGd9e/hbIXq980/1QJg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz}
    name: lodash.isboolean
    version: 3.0.3
    dev: false

  registry.npmmirror.com/lodash.isinteger@4.0.4:
    resolution: {integrity: sha512-DBwtEWN2caHQ9/imiNeEA5ys1JoRtRfY3d7V9wkqtbycnAmTvRRmbHKDV4a0EYc678/dia0jrte4tjYwVBaZUA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lodash.isinteger/-/lodash.isinteger-4.0.4.tgz}
    name: lodash.isinteger
    version: 4.0.4
    dev: false

  registry.npmmirror.com/lodash.isnumber@3.0.3:
    resolution: {integrity: sha512-QYqzpfwO3/CWf3XP+Z+tkQsfaLL/EnUlXWVkIk5FUPc4sBdTehEqZONuyRt2P67PXAk+NXmTBcc97zw9t1FQrw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lodash.isnumber/-/lodash.isnumber-3.0.3.tgz}
    name: lodash.isnumber
    version: 3.0.3
    dev: false

  registry.npmmirror.com/lodash.isplainobject@4.0.6:
    resolution: {integrity: sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz}
    name: lodash.isplainobject
    version: 4.0.6
    dev: false

  registry.npmmirror.com/lodash.isstring@4.0.1:
    resolution: {integrity: sha512-0wJxfxH1wgO3GrbuP+dTTk7op+6L41QCXbGINEmD+ny/G/eCqGzxyCsh7159S+mgDDcoarnBw6PC1PS5+wUGgw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lodash.isstring/-/lodash.isstring-4.0.1.tgz}
    name: lodash.isstring
    version: 4.0.1
    dev: false

  registry.npmmirror.com/lodash.memoize@4.1.2:
    resolution: {integrity: sha512-t7j+NzmgnQzTAYXcsHYLgimltOV1MXHtlOWf6GjL9Kj8GK5FInw5JotxvbOs+IvV1/Dzo04/fCGfLVs7aXb4Ag==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lodash.memoize/-/lodash.memoize-4.1.2.tgz}
    name: lodash.memoize
    version: 4.1.2
    dev: true

  registry.npmmirror.com/lodash.merge@4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lodash.merge/-/lodash.merge-4.6.2.tgz}
    name: lodash.merge
    version: 4.6.2
    dev: true

  registry.npmmirror.com/lodash.once@4.1.1:
    resolution: {integrity: sha512-Sb487aTOCr9drQVL8pIxOzVhafOjZN9UU54hiN8PU3uAiSV7lx1yYNpbNmex2PK6dSJoNTSJUUswT651yww3Mg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lodash.once/-/lodash.once-4.1.1.tgz}
    name: lodash.once
    version: 4.1.1
    dev: false

  registry.npmmirror.com/lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz}
    name: lodash
    version: 4.17.21

  registry.npmmirror.com/log-symbols@4.1.0:
    resolution: {integrity: sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/log-symbols/-/log-symbols-4.1.0.tgz}
    name: log-symbols
    version: 4.1.0
    engines: {node: '>=10'}
    dependencies:
      chalk: registry.npmmirror.com/chalk@4.1.2
      is-unicode-supported: registry.npmmirror.com/is-unicode-supported@0.1.0
    dev: true

  registry.npmmirror.com/long@5.3.2:
    resolution: {integrity: sha512-mNAgZ1GmyNhD7AuqnTG3/VQ26o760+ZYBPKjPvugO8+nLbYfX6TVpJPseBvopbdY+qpZ/lKUnmEc1LeZYS3QAA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/long/-/long-5.3.2.tgz}
    name: long
    version: 5.3.2
    dev: false

  registry.npmmirror.com/lower-case@2.0.2:
    resolution: {integrity: sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lower-case/-/lower-case-2.0.2.tgz}
    name: lower-case
    version: 2.0.2
    dependencies:
      tslib: registry.npmmirror.com/tslib@2.8.1
    dev: false

  registry.npmmirror.com/lowercase-keys@3.0.0:
    resolution: {integrity: sha512-ozCC6gdQ+glXOQsveKD0YsDy8DSQFjDTz4zyzEHNV5+JP5D62LmfDZ6o1cycFx9ouG940M5dE8C8CTewdj2YWQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lowercase-keys/-/lowercase-keys-3.0.0.tgz}
    name: lowercase-keys
    version: 3.0.0
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dev: true

  registry.npmmirror.com/lru-cache@10.4.3:
    resolution: {integrity: sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lru-cache/-/lru-cache-10.4.3.tgz}
    name: lru-cache
    version: 10.4.3
    dev: false

  registry.npmmirror.com/lru-cache@11.1.0:
    resolution: {integrity: sha512-QIXZUBJUx+2zHUdQujWejBkcD9+cs94tLn0+YL8UrCh+D5sCXZ4c7LaEH48pNwRY3MLDgqUFyhlCyjJPf1WP0A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lru-cache/-/lru-cache-11.1.0.tgz}
    name: lru-cache
    version: 11.1.0
    engines: {node: 20 || >=22}
    dev: true

  registry.npmmirror.com/lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lru-cache/-/lru-cache-5.1.1.tgz}
    name: lru-cache
    version: 5.1.1
    dependencies:
      yallist: registry.npmmirror.com/yallist@3.1.1
    dev: true

  registry.npmmirror.com/lru-cache@7.18.3:
    resolution: {integrity: sha512-jumlc0BIUrS3qJGgIkWZsyfAM7NCWiBcCDhnd+3NNM5KbBmLTgHVfWBcg6W+rLUsIpzpERPsvwUP7CckAQSOoA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lru-cache/-/lru-cache-7.18.3.tgz}
    name: lru-cache
    version: 7.18.3
    engines: {node: '>=12'}
    dev: false

  registry.npmmirror.com/lru.min@1.1.2:
    resolution: {integrity: sha512-Nv9KddBcQSlQopmBHXSsZVY5xsdlZkdH/Iey0BlcBYggMd4two7cZnKOK9vmy3nY0O5RGH99z1PCeTpPqszUYg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lru.min/-/lru.min-1.1.2.tgz}
    name: lru.min
    version: 1.1.2
    engines: {bun: '>=1.0.0', deno: '>=1.30.0', node: '>=8.0.0'}
    dev: false

  registry.npmmirror.com/magic-string@0.30.12:
    resolution: {integrity: sha512-Ea8I3sQMVXr8JhN4z+H/d8zwo+tYDgHE9+5G4Wnrwhs0gaK9fXTKx0Tw5Xwsd/bCPTTZNRAdpyzvoeORe9LYpw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/magic-string/-/magic-string-0.30.12.tgz}
    name: magic-string
    version: 0.30.12
    dependencies:
      '@jridgewell/sourcemap-codec': registry.npmmirror.com/@jridgewell/sourcemap-codec@1.5.0
    dev: true

  registry.npmmirror.com/magic-string@0.30.17:
    resolution: {integrity: sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/magic-string/-/magic-string-0.30.17.tgz}
    name: magic-string
    version: 0.30.17
    dependencies:
      '@jridgewell/sourcemap-codec': registry.npmmirror.com/@jridgewell/sourcemap-codec@1.5.0
    dev: true

  registry.npmmirror.com/make-dir@4.0.0:
    resolution: {integrity: sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/make-dir/-/make-dir-4.0.0.tgz}
    name: make-dir
    version: 4.0.0
    engines: {node: '>=10'}
    dependencies:
      semver: registry.npmmirror.com/semver@7.7.2
    dev: true

  registry.npmmirror.com/make-error@1.3.6:
    resolution: {integrity: sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/make-error/-/make-error-1.3.6.tgz}
    name: make-error
    version: 1.3.6

  registry.npmmirror.com/makeerror@1.0.12:
    resolution: {integrity: sha512-JmqCvUhmt43madlpFzG4BQzG2Z3m6tvQDNKdClZnO3VbIudJYmxsT0FNJMeiB2+JTSlTQTSbU8QdesVmwJcmLg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/makeerror/-/makeerror-1.0.12.tgz}
    name: makeerror
    version: 1.0.12
    dependencies:
      tmpl: registry.npmmirror.com/tmpl@1.0.5
    dev: true

  registry.npmmirror.com/map-obj@4.3.0:
    resolution: {integrity: sha512-hdN1wVrZbb29eBGiGjJbeP8JbKjq1urkHJ/LIP/NY48MZ1QVXUsQBV1G1zvYFHn1XE06cwjBsOI2K3Ulnj1YXQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/map-obj/-/map-obj-4.3.0.tgz}
    name: map-obj
    version: 4.3.0
    engines: {node: '>=8'}
    dev: false

  registry.npmmirror.com/math-intrinsics@1.1.0:
    resolution: {integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/math-intrinsics/-/math-intrinsics-1.1.0.tgz}
    name: math-intrinsics
    version: 1.1.0
    engines: {node: '>= 0.4'}

  registry.npmmirror.com/media-typer@0.3.0:
    resolution: {integrity: sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/media-typer/-/media-typer-0.3.0.tgz}
    name: media-typer
    version: 0.3.0
    engines: {node: '>= 0.6'}

  registry.npmmirror.com/media-typer@1.1.0:
    resolution: {integrity: sha512-aisnrDP4GNe06UcKFnV5bfMNPBUw4jsLGaWwWfnH3v02GnBuXX2MCVn5RbrWo0j3pczUilYblq7fQ7Nw2t5XKw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/media-typer/-/media-typer-1.1.0.tgz}
    name: media-typer
    version: 1.1.0
    engines: {node: '>= 0.8'}

  registry.npmmirror.com/memfs@3.5.3:
    resolution: {integrity: sha512-UERzLsxzllchadvbPs5aolHh65ISpKpM+ccLbOJ8/vvpBKmAWf+la7dXFy7Mr0ySHbdHrFv5kGFCUHHe6GFEmw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/memfs/-/memfs-3.5.3.tgz}
    name: memfs
    version: 3.5.3
    engines: {node: '>= 4.0.0'}
    dependencies:
      fs-monkey: registry.npmmirror.com/fs-monkey@1.0.6
    dev: true

  registry.npmmirror.com/merge-descriptors@2.0.0:
    resolution: {integrity: sha512-Snk314V5ayFLhp3fkUREub6WtjBfPdCPY1Ln8/8munuLuiYhsABgBVWsozAG+MWMbVEvcdcpbi9R7ww22l9Q3g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/merge-descriptors/-/merge-descriptors-2.0.0.tgz}
    name: merge-descriptors
    version: 2.0.0
    engines: {node: '>=18'}

  registry.npmmirror.com/merge-stream@2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/merge-stream/-/merge-stream-2.0.0.tgz}
    name: merge-stream
    version: 2.0.0
    dev: true

  registry.npmmirror.com/merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/merge2/-/merge2-1.4.1.tgz}
    name: merge2
    version: 1.4.1
    engines: {node: '>= 8'}
    dev: true

  registry.npmmirror.com/methods@1.1.2:
    resolution: {integrity: sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/methods/-/methods-1.1.2.tgz}
    name: methods
    version: 1.1.2
    engines: {node: '>= 0.6'}

  registry.npmmirror.com/micromatch@4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/micromatch/-/micromatch-4.0.8.tgz}
    name: micromatch
    version: 4.0.8
    engines: {node: '>=8.6'}
    dependencies:
      braces: registry.npmmirror.com/braces@3.0.3
      picomatch: registry.npmmirror.com/picomatch@2.3.1
    dev: true

  registry.npmmirror.com/mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz}
    name: mime-db
    version: 1.52.0
    engines: {node: '>= 0.6'}

  registry.npmmirror.com/mime-db@1.54.0:
    resolution: {integrity: sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mime-db/-/mime-db-1.54.0.tgz}
    name: mime-db
    version: 1.54.0
    engines: {node: '>= 0.6'}

  registry.npmmirror.com/mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz}
    name: mime-types
    version: 2.1.35
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: registry.npmmirror.com/mime-db@1.52.0

  registry.npmmirror.com/mime-types@3.0.1:
    resolution: {integrity: sha512-xRc4oEhT6eaBpU1XF7AjpOFD+xQmXNB5OVKwp4tqCuBpHLS/ZbBDrc07mYTDqVMg6PfxUjjNp85O6Cd2Z/5HWA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mime-types/-/mime-types-3.0.1.tgz}
    name: mime-types
    version: 3.0.1
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: registry.npmmirror.com/mime-db@1.54.0

  registry.npmmirror.com/mime@2.6.0:
    resolution: {integrity: sha512-USPkMeET31rOMiarsBNIHZKLGgvKc/LrjofAnBlOttf5ajRvqiRA8QsenbcooctK6d6Ts6aqZXBA+XbkKthiQg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mime/-/mime-2.6.0.tgz}
    name: mime
    version: 2.6.0
    engines: {node: '>=4.0.0'}
    hasBin: true

  registry.npmmirror.com/mimic-fn@2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mimic-fn/-/mimic-fn-2.1.0.tgz}
    name: mimic-fn
    version: 2.1.0
    engines: {node: '>=6'}
    dev: true

  registry.npmmirror.com/mimic-response@3.1.0:
    resolution: {integrity: sha512-z0yWI+4FDrrweS8Zmt4Ej5HdJmky15+L2e6Wgn3+iK5fWzb6T3fhNFq2+MeTRb064c6Wr4N/wv0DzQTjNzHNGQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mimic-response/-/mimic-response-3.1.0.tgz}
    name: mimic-response
    version: 3.1.0
    engines: {node: '>=10'}
    dev: true

  registry.npmmirror.com/mimic-response@4.0.0:
    resolution: {integrity: sha512-e5ISH9xMYU0DzrT+jl8q2ze9D6eWBto+I8CNpe+VI+K2J/F/k3PdkdTdz4wvGVH4NTpo+NRYTVIuMQEMMcsLqg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mimic-response/-/mimic-response-4.0.0.tgz}
    name: mimic-response
    version: 4.0.0
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dev: true

  registry.npmmirror.com/minimatch@10.0.3:
    resolution: {integrity: sha512-IPZ167aShDZZUMdRk66cyQAW3qr0WzbHkPdMYa8bzZhlHhO3jALbKdxcaak7W9FfT2rZNpQuUu4Od7ILEpXSaw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/minimatch/-/minimatch-10.0.3.tgz}
    name: minimatch
    version: 10.0.3
    engines: {node: 20 || >=22}
    dependencies:
      '@isaacs/brace-expansion': registry.npmmirror.com/@isaacs/brace-expansion@5.0.0
    dev: true

  registry.npmmirror.com/minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz}
    name: minimatch
    version: 3.1.2
    dependencies:
      brace-expansion: registry.npmmirror.com/brace-expansion@1.1.12
    dev: true

  registry.npmmirror.com/minimatch@5.1.6:
    resolution: {integrity: sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/minimatch/-/minimatch-5.1.6.tgz}
    name: minimatch
    version: 5.1.6
    engines: {node: '>=10'}
    dependencies:
      brace-expansion: registry.npmmirror.com/brace-expansion@2.0.2
    dev: true

  registry.npmmirror.com/minimatch@9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/minimatch/-/minimatch-9.0.5.tgz}
    name: minimatch
    version: 9.0.5
    engines: {node: '>=16 || 14 >=14.17'}
    dependencies:
      brace-expansion: registry.npmmirror.com/brace-expansion@2.0.2

  registry.npmmirror.com/minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/minimist/-/minimist-1.2.8.tgz}
    name: minimist
    version: 1.2.8

  registry.npmmirror.com/minipass@7.1.2:
    resolution: {integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/minipass/-/minipass-7.1.2.tgz}
    name: minipass
    version: 7.1.2
    engines: {node: '>=16 || 14 >=14.17'}

  registry.npmmirror.com/mkdirp@0.5.6:
    resolution: {integrity: sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mkdirp/-/mkdirp-0.5.6.tgz}
    name: mkdirp
    version: 0.5.6
    hasBin: true
    dependencies:
      minimist: registry.npmmirror.com/minimist@1.2.8

  registry.npmmirror.com/ms@2.0.0:
    resolution: {integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ms/-/ms-2.0.0.tgz}
    name: ms
    version: 2.0.0

  registry.npmmirror.com/ms@2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ms/-/ms-2.1.2.tgz}
    name: ms
    version: 2.1.2

  registry.npmmirror.com/ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz}
    name: ms
    version: 2.1.3

  registry.npmmirror.com/multer@1.4.5-lts.1:
    resolution: {integrity: sha512-ywPWvcDMeH+z9gQq5qYHCCy+ethsk4goepZ45GLD63fOu0YcNecQxi64nDs3qluZB+murG3/D4dJ7+dGctcCQQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/multer/-/multer-1.4.5-lts.1.tgz}
    name: multer
    version: 1.4.5-lts.1
    engines: {node: '>= 6.0.0'}
    deprecated: Multer 1.x is impacted by a number of vulnerabilities, which have been patched in 2.x. You should upgrade to the latest 2.x version.
    dependencies:
      append-field: registry.npmmirror.com/append-field@1.0.0
      busboy: registry.npmmirror.com/busboy@1.6.0
      concat-stream: registry.npmmirror.com/concat-stream@1.6.2
      mkdirp: registry.npmmirror.com/mkdirp@0.5.6
      object-assign: registry.npmmirror.com/object-assign@4.1.1
      type-is: registry.npmmirror.com/type-is@1.6.18
      xtend: registry.npmmirror.com/xtend@4.0.2

  registry.npmmirror.com/mute-stream@2.0.0:
    resolution: {integrity: sha512-WWdIxpyjEn+FhQJQQv9aQAYlHoNVdzIzUySNV1gHUPDSdZJ3yZn7pAAbQcV7B56Mvu881q9FZV+0Vx2xC44VWA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mute-stream/-/mute-stream-2.0.0.tgz}
    name: mute-stream
    version: 2.0.0
    engines: {node: ^18.17.0 || >=20.5.0}
    dev: true

  registry.npmmirror.com/mysql2@3.14.1:
    resolution: {integrity: sha512-7ytuPQJjQB8TNAYX/H2yhL+iQOnIBjAMam361R7UAL0lOVXWjtdrmoL9HYKqKoLp/8UUTRcvo1QPvK9KL7wA8w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mysql2/-/mysql2-3.14.1.tgz}
    name: mysql2
    version: 3.14.1
    engines: {node: '>= 8.0'}
    dependencies:
      aws-ssl-profiles: registry.npmmirror.com/aws-ssl-profiles@1.1.2
      denque: registry.npmmirror.com/denque@2.1.0
      generate-function: registry.npmmirror.com/generate-function@2.3.1
      iconv-lite: registry.npmmirror.com/iconv-lite@0.6.3
      long: registry.npmmirror.com/long@5.3.2
      lru.min: registry.npmmirror.com/lru.min@1.1.2
      named-placeholders: registry.npmmirror.com/named-placeholders@1.1.3
      seq-queue: registry.npmmirror.com/seq-queue@0.0.5
      sqlstring: registry.npmmirror.com/sqlstring@2.3.3
    dev: false

  registry.npmmirror.com/named-placeholders@1.1.3:
    resolution: {integrity: sha512-eLoBxg6wE/rZkJPhU/xRX1WTpkFEwDJEN96oxFrTsqBdbT5ec295Q+CoHrL9IT0DipqKhmGcaZmwOt8OON5x1w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/named-placeholders/-/named-placeholders-1.1.3.tgz}
    name: named-placeholders
    version: 1.1.3
    engines: {node: '>=12.0.0'}
    dependencies:
      lru-cache: registry.npmmirror.com/lru-cache@7.18.3
    dev: false

  registry.npmmirror.com/natural-compare@1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/natural-compare/-/natural-compare-1.4.0.tgz}
    name: natural-compare
    version: 1.4.0
    dev: true

  registry.npmmirror.com/negotiator@1.0.0:
    resolution: {integrity: sha512-8Ofs/AUQh8MaEcrlq5xOX0CQ9ypTF5dl78mjlMNfOK08fzpgTHQRQPBxcPlEtIw0yRpws+Zo/3r+5WRby7u3Gg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/negotiator/-/negotiator-1.0.0.tgz}
    name: negotiator
    version: 1.0.0
    engines: {node: '>= 0.6'}

  registry.npmmirror.com/neo-async@2.6.2:
    resolution: {integrity: sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/neo-async/-/neo-async-2.6.2.tgz}
    name: neo-async
    version: 2.6.2
    dev: true

  registry.npmmirror.com/no-case@3.0.4:
    resolution: {integrity: sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/no-case/-/no-case-3.0.4.tgz}
    name: no-case
    version: 3.0.4
    dependencies:
      lower-case: registry.npmmirror.com/lower-case@2.0.2
      tslib: registry.npmmirror.com/tslib@2.8.1
    dev: false

  registry.npmmirror.com/node-abort-controller@3.1.1:
    resolution: {integrity: sha512-AGK2yQKIjRuqnc6VkX2Xj5d+QW8xZ87pa1UK6yA6ouUyuxfHuMP6umE5QK7UmTeOAymo+Zx1Fxiuw9rVx8taHQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/node-abort-controller/-/node-abort-controller-3.1.1.tgz}
    name: node-abort-controller
    version: 3.1.1
    dev: true

  registry.npmmirror.com/node-emoji@1.11.0:
    resolution: {integrity: sha512-wo2DpQkQp7Sjm2A0cq+sN7EHKO6Sl0ctXeBdFZrL9T9+UywORbufTcTZxom8YqpLQt/FqNMUkOpkZrJVYSKD3A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/node-emoji/-/node-emoji-1.11.0.tgz}
    name: node-emoji
    version: 1.11.0
    dependencies:
      lodash: registry.npmmirror.com/lodash@4.17.21
    dev: true

  registry.npmmirror.com/node-hex@1.0.1:
    resolution: {integrity: sha512-iwpZdvW6Umz12ICmu9IYPRxg0tOLGmU3Tq2tKetejCj3oZd7b2nUXwP3a7QA5M9glWy8wlPS1G3RwM/CdsUbdQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/node-hex/-/node-hex-1.0.1.tgz}
    name: node-hex
    version: 1.0.1
    engines: {node: '>=8.0.0'}
    dev: false

  registry.npmmirror.com/node-int64@0.4.0:
    resolution: {integrity: sha512-O5lz91xSOeoXP6DulyHfllpq+Eg00MWitZIbtPfoSEvqIHdl5gfcY6hYzDWnj0qD5tz52PI08u9qUvSVeUBeHw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/node-int64/-/node-int64-0.4.0.tgz}
    name: node-int64
    version: 0.4.0
    dev: true

  registry.npmmirror.com/node-releases@2.0.19:
    resolution: {integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/node-releases/-/node-releases-2.0.19.tgz}
    name: node-releases
    version: 2.0.19
    dev: true

  registry.npmmirror.com/normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/normalize-path/-/normalize-path-3.0.0.tgz}
    name: normalize-path
    version: 3.0.0
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/normalize-url@8.0.2:
    resolution: {integrity: sha512-Ee/R3SyN4BuynXcnTaekmaVdbDAEiNrHqjQIA37mHU8G9pf7aaAD4ZX3XjBLo6rsdcxA/gtkcNYZLt30ACgynw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/normalize-url/-/normalize-url-8.0.2.tgz}
    name: normalize-url
    version: 8.0.2
    engines: {node: '>=14.16'}
    dev: true

  registry.npmmirror.com/npm-run-path@4.0.1:
    resolution: {integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/npm-run-path/-/npm-run-path-4.0.1.tgz}
    name: npm-run-path
    version: 4.0.1
    engines: {node: '>=8'}
    dependencies:
      path-key: registry.npmmirror.com/path-key@3.1.1
    dev: true

  registry.npmmirror.com/object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/object-assign/-/object-assign-4.1.1.tgz}
    name: object-assign
    version: 4.1.1
    engines: {node: '>=0.10.0'}

  registry.npmmirror.com/object-inspect@1.13.4:
    resolution: {integrity: sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/object-inspect/-/object-inspect-1.13.4.tgz}
    name: object-inspect
    version: 1.13.4
    engines: {node: '>= 0.4'}

  registry.npmmirror.com/on-finished@2.4.1:
    resolution: {integrity: sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/on-finished/-/on-finished-2.4.1.tgz}
    name: on-finished
    version: 2.4.1
    engines: {node: '>= 0.8'}
    dependencies:
      ee-first: registry.npmmirror.com/ee-first@1.1.1

  registry.npmmirror.com/once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/once/-/once-1.4.0.tgz}
    name: once
    version: 1.4.0
    dependencies:
      wrappy: registry.npmmirror.com/wrappy@1.0.2

  registry.npmmirror.com/onetime@5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/onetime/-/onetime-5.1.2.tgz}
    name: onetime
    version: 5.1.2
    engines: {node: '>=6'}
    dependencies:
      mimic-fn: registry.npmmirror.com/mimic-fn@2.1.0
    dev: true

  registry.npmmirror.com/optionator@0.9.4:
    resolution: {integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/optionator/-/optionator-0.9.4.tgz}
    name: optionator
    version: 0.9.4
    engines: {node: '>= 0.8.0'}
    dependencies:
      deep-is: registry.npmmirror.com/deep-is@0.1.4
      fast-levenshtein: registry.npmmirror.com/fast-levenshtein@2.0.6
      levn: registry.npmmirror.com/levn@0.4.1
      prelude-ls: registry.npmmirror.com/prelude-ls@1.2.1
      type-check: registry.npmmirror.com/type-check@0.4.0
      word-wrap: registry.npmmirror.com/word-wrap@1.2.5
    dev: true

  registry.npmmirror.com/ora@5.4.1:
    resolution: {integrity: sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ora/-/ora-5.4.1.tgz}
    name: ora
    version: 5.4.1
    engines: {node: '>=10'}
    dependencies:
      bl: registry.npmmirror.com/bl@4.1.0
      chalk: registry.npmmirror.com/chalk@4.1.2
      cli-cursor: registry.npmmirror.com/cli-cursor@3.1.0
      cli-spinners: registry.npmmirror.com/cli-spinners@2.9.2
      is-interactive: registry.npmmirror.com/is-interactive@1.0.0
      is-unicode-supported: registry.npmmirror.com/is-unicode-supported@0.1.0
      log-symbols: registry.npmmirror.com/log-symbols@4.1.0
      strip-ansi: registry.npmmirror.com/strip-ansi@6.0.1
      wcwidth: registry.npmmirror.com/wcwidth@1.0.1
    dev: true

  registry.npmmirror.com/os-tmpdir@1.0.2:
    resolution: {integrity: sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/os-tmpdir/-/os-tmpdir-1.0.2.tgz}
    name: os-tmpdir
    version: 1.0.2
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/p-cancelable@3.0.0:
    resolution: {integrity: sha512-mlVgR3PGuzlo0MmTdk4cXqXWlwQDLnONTAg6sm62XkMJEiRxN3GL3SffkYvqwonbkJBcrI7Uvv5Zh9yjvn2iUw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/p-cancelable/-/p-cancelable-3.0.0.tgz}
    name: p-cancelable
    version: 3.0.0
    engines: {node: '>=12.20'}
    dev: true

  registry.npmmirror.com/p-limit@2.3.0:
    resolution: {integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/p-limit/-/p-limit-2.3.0.tgz}
    name: p-limit
    version: 2.3.0
    engines: {node: '>=6'}
    dependencies:
      p-try: registry.npmmirror.com/p-try@2.2.0
    dev: true

  registry.npmmirror.com/p-limit@3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/p-limit/-/p-limit-3.1.0.tgz}
    name: p-limit
    version: 3.1.0
    engines: {node: '>=10'}
    dependencies:
      yocto-queue: registry.npmmirror.com/yocto-queue@0.1.0
    dev: true

  registry.npmmirror.com/p-locate@4.1.0:
    resolution: {integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/p-locate/-/p-locate-4.1.0.tgz}
    name: p-locate
    version: 4.1.0
    engines: {node: '>=8'}
    dependencies:
      p-limit: registry.npmmirror.com/p-limit@2.3.0
    dev: true

  registry.npmmirror.com/p-locate@5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/p-locate/-/p-locate-5.0.0.tgz}
    name: p-locate
    version: 5.0.0
    engines: {node: '>=10'}
    dependencies:
      p-limit: registry.npmmirror.com/p-limit@3.1.0
    dev: true

  registry.npmmirror.com/p-try@2.2.0:
    resolution: {integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/p-try/-/p-try-2.2.0.tgz}
    name: p-try
    version: 2.2.0
    engines: {node: '>=6'}
    dev: true

  registry.npmmirror.com/package-json-from-dist@1.0.1:
    resolution: {integrity: sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz}
    name: package-json-from-dist
    version: 1.0.1

  registry.npmmirror.com/parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/parent-module/-/parent-module-1.0.1.tgz}
    name: parent-module
    version: 1.0.1
    engines: {node: '>=6'}
    dependencies:
      callsites: registry.npmmirror.com/callsites@3.1.0
    dev: true

  registry.npmmirror.com/parse-json@5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/parse-json/-/parse-json-5.2.0.tgz}
    name: parse-json
    version: 5.2.0
    engines: {node: '>=8'}
    dependencies:
      '@babel/code-frame': registry.npmmirror.com/@babel/code-frame@7.27.1
      error-ex: registry.npmmirror.com/error-ex@1.3.2
      json-parse-even-better-errors: registry.npmmirror.com/json-parse-even-better-errors@2.3.1
      lines-and-columns: registry.npmmirror.com/lines-and-columns@1.2.4
    dev: true

  registry.npmmirror.com/parseurl@1.3.3:
    resolution: {integrity: sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/parseurl/-/parseurl-1.3.3.tgz}
    name: parseurl
    version: 1.3.3
    engines: {node: '>= 0.8'}

  registry.npmmirror.com/passport-jwt@4.0.1:
    resolution: {integrity: sha512-UCKMDYhNuGOBE9/9Ycuoyh7vP6jpeTp/+sfMJl7nLff/t6dps+iaeE0hhNkKN8/HZHcJ7lCdOyDxHdDoxoSvdQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/passport-jwt/-/passport-jwt-4.0.1.tgz}
    name: passport-jwt
    version: 4.0.1
    dependencies:
      jsonwebtoken: registry.npmmirror.com/jsonwebtoken@9.0.2
      passport-strategy: registry.npmmirror.com/passport-strategy@1.0.0
    dev: false

  registry.npmmirror.com/passport-strategy@1.0.0:
    resolution: {integrity: sha512-CB97UUvDKJde2V0KDWWB3lyf6PC3FaZP7YxZ2G8OAtn9p4HI9j9JLP9qjOGZFvyl8uwNT8qM+hGnz/n16NI7oA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/passport-strategy/-/passport-strategy-1.0.0.tgz}
    name: passport-strategy
    version: 1.0.0
    engines: {node: '>= 0.4.0'}
    dev: false

  registry.npmmirror.com/passport@0.7.0:
    resolution: {integrity: sha512-cPLl+qZpSc+ireUvt+IzqbED1cHHkDoVYMo30jbJIdOOjQ1MQYZBPiNvmi8UM6lJuOpTPXJGZQk0DtC4y61MYQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/passport/-/passport-0.7.0.tgz}
    name: passport
    version: 0.7.0
    engines: {node: '>= 0.4.0'}
    dependencies:
      passport-strategy: registry.npmmirror.com/passport-strategy@1.0.0
      pause: registry.npmmirror.com/pause@0.0.1
      utils-merge: registry.npmmirror.com/utils-merge@1.0.1
    dev: false

  registry.npmmirror.com/path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/path-exists/-/path-exists-4.0.0.tgz}
    name: path-exists
    version: 4.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/path-is-absolute@1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz}
    name: path-is-absolute
    version: 1.0.1
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz}
    name: path-key
    version: 3.1.1
    engines: {node: '>=8'}

  registry.npmmirror.com/path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz}
    name: path-parse
    version: 1.0.7
    dev: true

  registry.npmmirror.com/path-scurry@1.11.1:
    resolution: {integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/path-scurry/-/path-scurry-1.11.1.tgz}
    name: path-scurry
    version: 1.11.1
    engines: {node: '>=16 || 14 >=14.18'}
    dependencies:
      lru-cache: registry.npmmirror.com/lru-cache@10.4.3
      minipass: registry.npmmirror.com/minipass@7.1.2
    dev: false

  registry.npmmirror.com/path-scurry@2.0.0:
    resolution: {integrity: sha512-ypGJsmGtdXUOeM5u93TyeIEfEhM6s+ljAhrk5vAvSx8uyY/02OvrZnA0YNGUrPXfpJMgI1ODd3nwz8Npx4O4cg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/path-scurry/-/path-scurry-2.0.0.tgz}
    name: path-scurry
    version: 2.0.0
    engines: {node: 20 || >=22}
    dependencies:
      lru-cache: registry.npmmirror.com/lru-cache@11.1.0
      minipass: registry.npmmirror.com/minipass@7.1.2
    dev: true

  registry.npmmirror.com/path-to-regexp@8.2.0:
    resolution: {integrity: sha512-TdrF7fW9Rphjq4RjrW0Kp2AW0Ahwu9sRGTkS6bvDi0SCwZlEZYmcfDbEsTz8RVk0EHIS/Vd1bv3JhG+1xZuAyQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/path-to-regexp/-/path-to-regexp-8.2.0.tgz}
    name: path-to-regexp
    version: 8.2.0
    engines: {node: '>=16'}

  registry.npmmirror.com/path-type@4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/path-type/-/path-type-4.0.0.tgz}
    name: path-type
    version: 4.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/pause-stream@0.0.11:
    resolution: {integrity: sha512-e3FBlXLmN/D1S+zHzanP4E/4Z60oFAa3O051qt1pxa7DEJWKAyil6upYVXCWadEnuoqa4Pkc9oUx9zsxYeRv8A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/pause-stream/-/pause-stream-0.0.11.tgz}
    name: pause-stream
    version: 0.0.11
    dependencies:
      through: registry.npmmirror.com/through@2.3.8
    dev: false

  registry.npmmirror.com/pause@0.0.1:
    resolution: {integrity: sha512-KG8UEiEVkR3wGEb4m5yZkVCzigAD+cVEJck2CzYZO37ZGJfctvVptVO192MwrtPhzONn6go8ylnOdMhKqi4nfg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/pause/-/pause-0.0.1.tgz}
    name: pause
    version: 0.0.1
    dev: false

  registry.npmmirror.com/peek-readable@5.4.2:
    resolution: {integrity: sha512-peBp3qZyuS6cNIJ2akRNG1uo1WJ1d0wTxg/fxMdZ0BqCVhx242bSFHM9eNqflfJVS9SsgkzgT/1UgnsurBOTMg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/peek-readable/-/peek-readable-5.4.2.tgz}
    name: peek-readable
    version: 5.4.2
    engines: {node: '>=14.16'}
    dev: true

  registry.npmmirror.com/pend@1.2.0:
    resolution: {integrity: sha512-F3asv42UuXchdzt+xXqfW1OGlVBe+mxa2mqI0pg5yAHZPvFmY3Y6drSf/GQ1A86WgWEN9Kzh/WrgKa6iGcHXLg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/pend/-/pend-1.2.0.tgz}
    name: pend
    version: 1.2.0
    dev: true

  registry.npmmirror.com/picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/picocolors/-/picocolors-1.1.1.tgz}
    name: picocolors
    version: 1.1.1
    dev: true

  registry.npmmirror.com/picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz}
    name: picomatch
    version: 2.3.1
    engines: {node: '>=8.6'}
    dev: true

  registry.npmmirror.com/picomatch@4.0.2:
    resolution: {integrity: sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/picomatch/-/picomatch-4.0.2.tgz}
    name: picomatch
    version: 4.0.2
    engines: {node: '>=12'}
    dev: true

  registry.npmmirror.com/pirates@4.0.7:
    resolution: {integrity: sha512-TfySrs/5nm8fQJDcBDuUng3VOUKsd7S+zqvbOTiGXHfxX4wK31ard+hoNuvkicM/2YFzlpDgABOevKSsB4G/FA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/pirates/-/pirates-4.0.7.tgz}
    name: pirates
    version: 4.0.7
    engines: {node: '>= 6'}
    dev: true

  registry.npmmirror.com/piscina@4.9.2:
    resolution: {integrity: sha512-Fq0FERJWFEUpB4eSY59wSNwXD4RYqR+nR/WiEVcZW8IWfVBxJJafcgTEZDQo8k3w0sUarJ8RyVbbUF4GQ2LGbQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/piscina/-/piscina-4.9.2.tgz}
    name: piscina
    version: 4.9.2
    optionalDependencies:
      '@napi-rs/nice': registry.npmmirror.com/@napi-rs/nice@1.0.1
    dev: true

  registry.npmmirror.com/pkg-dir@4.2.0:
    resolution: {integrity: sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/pkg-dir/-/pkg-dir-4.2.0.tgz}
    name: pkg-dir
    version: 4.2.0
    engines: {node: '>=8'}
    dependencies:
      find-up: registry.npmmirror.com/find-up@4.1.0
    dev: true

  registry.npmmirror.com/pluralize@8.0.0:
    resolution: {integrity: sha512-Nc3IT5yHzflTfbjgqWcCPpo7DaKy4FnpB0l/zCAW0Tc7jxAiuqSxHasntB3D7887LSrA93kDJ9IXovxJYxyLCA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/pluralize/-/pluralize-8.0.0.tgz}
    name: pluralize
    version: 8.0.0
    engines: {node: '>=4'}
    dev: true

  registry.npmmirror.com/prelude-ls@1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/prelude-ls/-/prelude-ls-1.2.1.tgz}
    name: prelude-ls
    version: 1.2.1
    engines: {node: '>= 0.8.0'}
    dev: true

  registry.npmmirror.com/prettier-linter-helpers@1.0.0:
    resolution: {integrity: sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz}
    name: prettier-linter-helpers
    version: 1.0.0
    engines: {node: '>=6.0.0'}
    dependencies:
      fast-diff: registry.npmmirror.com/fast-diff@1.3.0
    dev: true

  registry.npmmirror.com/prettier@3.4.2:
    resolution: {integrity: sha512-e9MewbtFo+Fevyuxn/4rrcDAaq0IYxPGLvObpQjiZBMAzB9IGmzlnG9RZy3FFas+eBMu2vA0CszMeduow5dIuQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/prettier/-/prettier-3.4.2.tgz}
    name: prettier
    version: 3.4.2
    engines: {node: '>=14'}
    hasBin: true
    dev: true

  registry.npmmirror.com/pretty-format@29.7.0:
    resolution: {integrity: sha512-Pdlw/oPxN+aXdmM9R00JVC9WVFoCLTKJvDVLgmJ+qAffBMxsV85l/Lu7sNx4zSzPyoL2euImuEwHhOXdEgNFZQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/pretty-format/-/pretty-format-29.7.0.tgz}
    name: pretty-format
    version: 29.7.0
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/schemas': registry.npmmirror.com/@jest/schemas@29.6.3
      ansi-styles: registry.npmmirror.com/ansi-styles@5.2.0
      react-is: registry.npmmirror.com/react-is@18.3.1
    dev: true

  registry.npmmirror.com/process-nextick-args@2.0.1:
    resolution: {integrity: sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/process-nextick-args/-/process-nextick-args-2.0.1.tgz}
    name: process-nextick-args
    version: 2.0.1

  registry.npmmirror.com/prompts@2.4.2:
    resolution: {integrity: sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/prompts/-/prompts-2.4.2.tgz}
    name: prompts
    version: 2.4.2
    engines: {node: '>= 6'}
    dependencies:
      kleur: registry.npmmirror.com/kleur@3.0.3
      sisteransi: registry.npmmirror.com/sisteransi@1.0.5
    dev: true

  registry.npmmirror.com/proxy-addr@2.0.7:
    resolution: {integrity: sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/proxy-addr/-/proxy-addr-2.0.7.tgz}
    name: proxy-addr
    version: 2.0.7
    engines: {node: '>= 0.10'}
    dependencies:
      forwarded: registry.npmmirror.com/forwarded@0.2.0
      ipaddr.js: registry.npmmirror.com/ipaddr.js@1.9.1

  registry.npmmirror.com/punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/punycode/-/punycode-2.3.1.tgz}
    name: punycode
    version: 2.3.1
    engines: {node: '>=6'}
    dev: true

  registry.npmmirror.com/pure-rand@6.1.0:
    resolution: {integrity: sha512-bVWawvoZoBYpp6yIoQtQXHZjmz35RSVHnUOTefl8Vcjr8snTPY1wnpSPMWekcFwbxI6gtmT7rSYPFvz71ldiOA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/pure-rand/-/pure-rand-6.1.0.tgz}
    name: pure-rand
    version: 6.1.0
    dev: true

  registry.npmmirror.com/qs@6.13.0:
    resolution: {integrity: sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/qs/-/qs-6.13.0.tgz}
    name: qs
    version: 6.13.0
    engines: {node: '>=0.6'}
    dependencies:
      side-channel: registry.npmmirror.com/side-channel@1.1.0

  registry.npmmirror.com/qs@6.14.0:
    resolution: {integrity: sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/qs/-/qs-6.14.0.tgz}
    name: qs
    version: 6.14.0
    engines: {node: '>=0.6'}
    dependencies:
      side-channel: registry.npmmirror.com/side-channel@1.1.0

  registry.npmmirror.com/queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/queue-microtask/-/queue-microtask-1.2.3.tgz}
    name: queue-microtask
    version: 1.2.3
    dev: true

  registry.npmmirror.com/quick-lru@5.1.1:
    resolution: {integrity: sha512-WuyALRjWPDGtt/wzJiadO5AXY+8hZ80hVpe6MyivgraREW751X3SbhRvG3eLKOYN+8VEvqLcf3wdnt44Z4S4SA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/quick-lru/-/quick-lru-5.1.1.tgz}
    name: quick-lru
    version: 5.1.1
    engines: {node: '>=10'}

  registry.npmmirror.com/randombytes@2.1.0:
    resolution: {integrity: sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/randombytes/-/randombytes-2.1.0.tgz}
    name: randombytes
    version: 2.1.0
    dependencies:
      safe-buffer: registry.npmmirror.com/safe-buffer@5.2.1
    dev: true

  registry.npmmirror.com/range-parser@1.2.1:
    resolution: {integrity: sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/range-parser/-/range-parser-1.2.1.tgz}
    name: range-parser
    version: 1.2.1
    engines: {node: '>= 0.6'}

  registry.npmmirror.com/raw-body@2.5.2:
    resolution: {integrity: sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/raw-body/-/raw-body-2.5.2.tgz}
    name: raw-body
    version: 2.5.2
    engines: {node: '>= 0.8'}
    dependencies:
      bytes: registry.npmmirror.com/bytes@3.1.2
      http-errors: registry.npmmirror.com/http-errors@2.0.0
      iconv-lite: registry.npmmirror.com/iconv-lite@0.4.24
      unpipe: registry.npmmirror.com/unpipe@1.0.0

  registry.npmmirror.com/raw-body@3.0.0:
    resolution: {integrity: sha512-RmkhL8CAyCRPXCE28MMH0z2PNWQBNk2Q09ZdxM9IOOXwxwZbN+qbWaatPkdkWIKL2ZVDImrN/pK5HTRz2PcS4g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/raw-body/-/raw-body-3.0.0.tgz}
    name: raw-body
    version: 3.0.0
    engines: {node: '>= 0.8'}
    dependencies:
      bytes: registry.npmmirror.com/bytes@3.1.2
      http-errors: registry.npmmirror.com/http-errors@2.0.0
      iconv-lite: registry.npmmirror.com/iconv-lite@0.6.3
      unpipe: registry.npmmirror.com/unpipe@1.0.0

  registry.npmmirror.com/react-is@18.3.1:
    resolution: {integrity: sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/react-is/-/react-is-18.3.1.tgz}
    name: react-is
    version: 18.3.1
    dev: true

  registry.npmmirror.com/readable-stream@2.3.8:
    resolution: {integrity: sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/readable-stream/-/readable-stream-2.3.8.tgz}
    name: readable-stream
    version: 2.3.8
    dependencies:
      core-util-is: registry.npmmirror.com/core-util-is@1.0.3
      inherits: registry.npmmirror.com/inherits@2.0.4
      isarray: registry.npmmirror.com/isarray@1.0.0
      process-nextick-args: registry.npmmirror.com/process-nextick-args@2.0.1
      safe-buffer: registry.npmmirror.com/safe-buffer@5.1.2
      string_decoder: registry.npmmirror.com/string_decoder@1.1.1
      util-deprecate: registry.npmmirror.com/util-deprecate@1.0.2

  registry.npmmirror.com/readable-stream@3.6.2:
    resolution: {integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/readable-stream/-/readable-stream-3.6.2.tgz}
    name: readable-stream
    version: 3.6.2
    engines: {node: '>= 6'}
    dependencies:
      inherits: registry.npmmirror.com/inherits@2.0.4
      string_decoder: registry.npmmirror.com/string_decoder@1.3.0
      util-deprecate: registry.npmmirror.com/util-deprecate@1.0.2
    dev: true

  registry.npmmirror.com/readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/readdirp/-/readdirp-3.6.0.tgz}
    name: readdirp
    version: 3.6.0
    engines: {node: '>=8.10.0'}
    dependencies:
      picomatch: registry.npmmirror.com/picomatch@2.3.1
    dev: true

  registry.npmmirror.com/readdirp@4.1.2:
    resolution: {integrity: sha512-GDhwkLfywWL2s6vEjyhri+eXmfH6j1L7JE27WhqLeYzoh/A3DBaYGEj2H/HFZCn/kMfim73FXxEJTw06WtxQwg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/readdirp/-/readdirp-4.1.2.tgz}
    name: readdirp
    version: 4.1.2
    engines: {node: '>= 14.18.0'}
    dev: true

  registry.npmmirror.com/reflect-metadata@0.2.2:
    resolution: {integrity: sha512-urBwgfrvVP/eAyXx4hluJivBKzuEbSQs9rKWCrCkbSxNv8mxPcUZKeuoF3Uy4mJl3Lwprp6yy5/39VWigZ4K6Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/reflect-metadata/-/reflect-metadata-0.2.2.tgz}
    name: reflect-metadata
    version: 0.2.2

  registry.npmmirror.com/repeat-string@1.6.1:
    resolution: {integrity: sha512-PV0dzCYDNfRi1jCDbJzpW7jNNDRuCOG/jI5ctQcGKt/clZD+YcPS3yIlWuTJMmESC8aevCFmWJy5wjAFgNqN6w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/repeat-string/-/repeat-string-1.6.1.tgz}
    name: repeat-string
    version: 1.6.1
    engines: {node: '>=0.10'}
    dev: true

  registry.npmmirror.com/require-directory@2.1.1:
    resolution: {integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/require-directory/-/require-directory-2.1.1.tgz}
    name: require-directory
    version: 2.1.1
    engines: {node: '>=0.10.0'}

  registry.npmmirror.com/require-from-string@2.0.2:
    resolution: {integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/require-from-string/-/require-from-string-2.0.2.tgz}
    name: require-from-string
    version: 2.0.2
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/resolve-alpn@1.2.1:
    resolution: {integrity: sha512-0a1F4l73/ZFZOakJnQ3FvkJ2+gSTQWz/r2KE5OdDY0TxPm5h4GkqkWWfM47T7HsbnOtcJVEF4epCVy6u7Q3K+g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/resolve-alpn/-/resolve-alpn-1.2.1.tgz}
    name: resolve-alpn
    version: 1.2.1
    dev: true

  registry.npmmirror.com/resolve-cwd@3.0.0:
    resolution: {integrity: sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/resolve-cwd/-/resolve-cwd-3.0.0.tgz}
    name: resolve-cwd
    version: 3.0.0
    engines: {node: '>=8'}
    dependencies:
      resolve-from: registry.npmmirror.com/resolve-from@5.0.0
    dev: true

  registry.npmmirror.com/resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/resolve-from/-/resolve-from-4.0.0.tgz}
    name: resolve-from
    version: 4.0.0
    engines: {node: '>=4'}
    dev: true

  registry.npmmirror.com/resolve-from@5.0.0:
    resolution: {integrity: sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/resolve-from/-/resolve-from-5.0.0.tgz}
    name: resolve-from
    version: 5.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/resolve.exports@2.0.3:
    resolution: {integrity: sha512-OcXjMsGdhL4XnbShKpAcSqPMzQoYkYyhbEaeSko47MjRP9NfEQMhZkXL1DoFlt9LWQn4YttrdnV6X2OiyzBi+A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/resolve.exports/-/resolve.exports-2.0.3.tgz}
    name: resolve.exports
    version: 2.0.3
    engines: {node: '>=10'}
    dev: true

  registry.npmmirror.com/resolve@1.22.10:
    resolution: {integrity: sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/resolve/-/resolve-1.22.10.tgz}
    name: resolve
    version: 1.22.10
    engines: {node: '>= 0.4'}
    hasBin: true
    dependencies:
      is-core-module: registry.npmmirror.com/is-core-module@2.16.1
      path-parse: registry.npmmirror.com/path-parse@1.0.7
      supports-preserve-symlinks-flag: registry.npmmirror.com/supports-preserve-symlinks-flag@1.0.0
    dev: true

  registry.npmmirror.com/responselike@3.0.0:
    resolution: {integrity: sha512-40yHxbNcl2+rzXvZuVkrYohathsSJlMTXKryG5y8uciHv1+xDLHQpgjG64JUO9nrEq2jGLH6IZ8BcZyw3wrweg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/responselike/-/responselike-3.0.0.tgz}
    name: responselike
    version: 3.0.0
    engines: {node: '>=14.16'}
    dependencies:
      lowercase-keys: registry.npmmirror.com/lowercase-keys@3.0.0
    dev: true

  registry.npmmirror.com/restore-cursor@3.1.0:
    resolution: {integrity: sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/restore-cursor/-/restore-cursor-3.1.0.tgz}
    name: restore-cursor
    version: 3.1.0
    engines: {node: '>=8'}
    dependencies:
      onetime: registry.npmmirror.com/onetime@5.1.2
      signal-exit: registry.npmmirror.com/signal-exit@3.0.7
    dev: true

  registry.npmmirror.com/reusify@1.1.0:
    resolution: {integrity: sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/reusify/-/reusify-1.1.0.tgz}
    name: reusify
    version: 1.1.0
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/router@2.2.0:
    resolution: {integrity: sha512-nLTrUKm2UyiL7rlhapu/Zl45FwNgkZGaCpZbIHajDYgwlJCOzLSk+cIPAnsEqV955GjILJnKbdQC1nVPz+gAYQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/router/-/router-2.2.0.tgz}
    name: router
    version: 2.2.0
    engines: {node: '>= 18'}
    dependencies:
      debug: registry.npmmirror.com/debug@4.4.1
      depd: registry.npmmirror.com/depd@2.0.0
      is-promise: registry.npmmirror.com/is-promise@4.0.0
      parseurl: registry.npmmirror.com/parseurl@1.3.3
      path-to-regexp: registry.npmmirror.com/path-to-regexp@8.2.0
    transitivePeerDependencies:
      - supports-color

  registry.npmmirror.com/run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/run-parallel/-/run-parallel-1.2.0.tgz}
    name: run-parallel
    version: 1.2.0
    dependencies:
      queue-microtask: registry.npmmirror.com/queue-microtask@1.2.3
    dev: true

  registry.npmmirror.com/rxjs@7.8.1:
    resolution: {integrity: sha512-AA3TVj+0A2iuIoQkWEK/tqFjBq2j+6PO6Y0zJcvzLAFhEFIO3HL0vls9hWLncZbAAbK0mar7oZ4V079I/qPMxg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/rxjs/-/rxjs-7.8.1.tgz}
    name: rxjs
    version: 7.8.1
    dependencies:
      tslib: registry.npmmirror.com/tslib@2.8.1

  registry.npmmirror.com/safe-buffer@5.1.2:
    resolution: {integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.1.2.tgz}
    name: safe-buffer
    version: 5.1.2

  registry.npmmirror.com/safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz}
    name: safe-buffer
    version: 5.2.1

  registry.npmmirror.com/safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/safer-buffer/-/safer-buffer-2.1.2.tgz}
    name: safer-buffer
    version: 2.1.2

  registry.npmmirror.com/schema-utils@3.3.0:
    resolution: {integrity: sha512-pN/yOAvcC+5rQ5nERGuwrjLlYvLTbCibnZ1I7B1LaiAz9BRBlE9GMgE/eqV30P7aJQUf7Ddimy/RsbYO/GrVGg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/schema-utils/-/schema-utils-3.3.0.tgz}
    name: schema-utils
    version: 3.3.0
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/json-schema': registry.npmmirror.com/@types/json-schema@7.0.15
      ajv: registry.npmmirror.com/ajv@6.12.6
      ajv-keywords: registry.npmmirror.com/ajv-keywords@3.5.2(ajv@6.12.6)
    dev: true

  registry.npmmirror.com/schema-utils@4.3.2:
    resolution: {integrity: sha512-Gn/JaSk/Mt9gYubxTtSn/QCV4em9mpAPiR1rqy/Ocu19u/G9J5WWdNoUT4SiV6mFC3y6cxyFcFwdzPM3FgxGAQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/schema-utils/-/schema-utils-4.3.2.tgz}
    name: schema-utils
    version: 4.3.2
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/json-schema': registry.npmmirror.com/@types/json-schema@7.0.15
      ajv: registry.npmmirror.com/ajv@8.17.1
      ajv-formats: registry.npmmirror.com/ajv-formats@2.1.1(ajv@8.17.1)
      ajv-keywords: registry.npmmirror.com/ajv-keywords@5.1.0(ajv@8.17.1)
    dev: true

  registry.npmmirror.com/seek-bzip@2.0.0:
    resolution: {integrity: sha512-SMguiTnYrhpLdk3PwfzHeotrcwi8bNV4iemL9tx9poR/yeaMYwB9VzR1w7b57DuWpuqR8n6oZboi0hj3AxZxQg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/seek-bzip/-/seek-bzip-2.0.0.tgz}
    name: seek-bzip
    version: 2.0.0
    hasBin: true
    dependencies:
      commander: registry.npmmirror.com/commander@6.2.1
    dev: true

  registry.npmmirror.com/semver-regex@4.0.5:
    resolution: {integrity: sha512-hunMQrEy1T6Jr2uEVjrAIqjwWcQTgOAcIM52C8MY1EZSD3DDNft04XzvYKPqjED65bNVVko0YI38nYeEHCX3yw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/semver-regex/-/semver-regex-4.0.5.tgz}
    name: semver-regex
    version: 4.0.5
    engines: {node: '>=12'}
    dev: true

  registry.npmmirror.com/semver-truncate@3.0.0:
    resolution: {integrity: sha512-LJWA9kSvMolR51oDE6PN3kALBNaUdkxzAGcexw8gjMA8xr5zUqK0JiR3CgARSqanYF3Z1YHvsErb1KDgh+v7Rg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/semver-truncate/-/semver-truncate-3.0.0.tgz}
    name: semver-truncate
    version: 3.0.0
    engines: {node: '>=12'}
    dependencies:
      semver: registry.npmmirror.com/semver@7.7.2
    dev: true

  registry.npmmirror.com/semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/semver/-/semver-6.3.1.tgz}
    name: semver
    version: 6.3.1
    hasBin: true
    dev: true

  registry.npmmirror.com/semver@7.7.2:
    resolution: {integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/semver/-/semver-7.7.2.tgz}
    name: semver
    version: 7.7.2
    engines: {node: '>=10'}
    hasBin: true

  registry.npmmirror.com/send@1.2.0:
    resolution: {integrity: sha512-uaW0WwXKpL9blXE2o0bRhoL2EGXIrZxQ2ZQ4mgcfoBxdFmQold+qWsD2jLrfZ0trjKL6vOw0j//eAwcALFjKSw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/send/-/send-1.2.0.tgz}
    name: send
    version: 1.2.0
    engines: {node: '>= 18'}
    dependencies:
      debug: registry.npmmirror.com/debug@4.4.1
      encodeurl: registry.npmmirror.com/encodeurl@2.0.0
      escape-html: registry.npmmirror.com/escape-html@1.0.3
      etag: registry.npmmirror.com/etag@1.8.1
      fresh: registry.npmmirror.com/fresh@2.0.0
      http-errors: registry.npmmirror.com/http-errors@2.0.0
      mime-types: registry.npmmirror.com/mime-types@3.0.1
      ms: registry.npmmirror.com/ms@2.1.3
      on-finished: registry.npmmirror.com/on-finished@2.4.1
      range-parser: registry.npmmirror.com/range-parser@1.2.1
      statuses: registry.npmmirror.com/statuses@2.0.1
    transitivePeerDependencies:
      - supports-color

  registry.npmmirror.com/seq-queue@0.0.5:
    resolution: {integrity: sha512-hr3Wtp/GZIc/6DAGPDcV4/9WoZhjrkXsi5B/07QgX8tsdc6ilr7BFM6PM6rbdAX1kFSDYeZGLipIZZKyQP0O5Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/seq-queue/-/seq-queue-0.0.5.tgz}
    name: seq-queue
    version: 0.0.5
    dev: false

  registry.npmmirror.com/serialize-javascript@6.0.2:
    resolution: {integrity: sha512-Saa1xPByTTq2gdeFZYLLo+RFE35NHZkAbqZeWNd3BpzppeVisAqpDjcp8dyf6uIvEqJRd46jemmyA4iFIeVk8g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/serialize-javascript/-/serialize-javascript-6.0.2.tgz}
    name: serialize-javascript
    version: 6.0.2
    dependencies:
      randombytes: registry.npmmirror.com/randombytes@2.1.0
    dev: true

  registry.npmmirror.com/serve-static@2.2.0:
    resolution: {integrity: sha512-61g9pCh0Vnh7IutZjtLGGpTA355+OPn2TyDv/6ivP2h/AdAVX9azsoxmg2/M6nZeQZNYBEwIcsne1mJd9oQItQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/serve-static/-/serve-static-2.2.0.tgz}
    name: serve-static
    version: 2.2.0
    engines: {node: '>= 18'}
    dependencies:
      encodeurl: registry.npmmirror.com/encodeurl@2.0.0
      escape-html: registry.npmmirror.com/escape-html@1.0.3
      parseurl: registry.npmmirror.com/parseurl@1.3.3
      send: registry.npmmirror.com/send@1.2.0
    transitivePeerDependencies:
      - supports-color

  registry.npmmirror.com/setprototypeof@1.2.0:
    resolution: {integrity: sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/setprototypeof/-/setprototypeof-1.2.0.tgz}
    name: setprototypeof
    version: 1.2.0

  registry.npmmirror.com/sha.js@2.4.11:
    resolution: {integrity: sha512-QMEp5B7cftE7APOjk5Y6xgrbWu+WkLVQwk8JNjZ8nKRciZaByEW6MubieAiToS7+dwvrjGhH8jRXz3MVd0AYqQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/sha.js/-/sha.js-2.4.11.tgz}
    name: sha.js
    version: 2.4.11
    hasBin: true
    dependencies:
      inherits: registry.npmmirror.com/inherits@2.0.4
      safe-buffer: registry.npmmirror.com/safe-buffer@5.2.1
    dev: false

  registry.npmmirror.com/shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/shebang-command/-/shebang-command-2.0.0.tgz}
    name: shebang-command
    version: 2.0.0
    engines: {node: '>=8'}
    dependencies:
      shebang-regex: registry.npmmirror.com/shebang-regex@3.0.0

  registry.npmmirror.com/shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/shebang-regex/-/shebang-regex-3.0.0.tgz}
    name: shebang-regex
    version: 3.0.0
    engines: {node: '>=8'}

  registry.npmmirror.com/side-channel-list@1.0.0:
    resolution: {integrity: sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/side-channel-list/-/side-channel-list-1.0.0.tgz}
    name: side-channel-list
    version: 1.0.0
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: registry.npmmirror.com/es-errors@1.3.0
      object-inspect: registry.npmmirror.com/object-inspect@1.13.4

  registry.npmmirror.com/side-channel-map@1.0.1:
    resolution: {integrity: sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/side-channel-map/-/side-channel-map-1.0.1.tgz}
    name: side-channel-map
    version: 1.0.1
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: registry.npmmirror.com/call-bound@1.0.4
      es-errors: registry.npmmirror.com/es-errors@1.3.0
      get-intrinsic: registry.npmmirror.com/get-intrinsic@1.3.0
      object-inspect: registry.npmmirror.com/object-inspect@1.13.4

  registry.npmmirror.com/side-channel-weakmap@1.0.2:
    resolution: {integrity: sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz}
    name: side-channel-weakmap
    version: 1.0.2
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: registry.npmmirror.com/call-bound@1.0.4
      es-errors: registry.npmmirror.com/es-errors@1.3.0
      get-intrinsic: registry.npmmirror.com/get-intrinsic@1.3.0
      object-inspect: registry.npmmirror.com/object-inspect@1.13.4
      side-channel-map: registry.npmmirror.com/side-channel-map@1.0.1

  registry.npmmirror.com/side-channel@1.1.0:
    resolution: {integrity: sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/side-channel/-/side-channel-1.1.0.tgz}
    name: side-channel
    version: 1.1.0
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: registry.npmmirror.com/es-errors@1.3.0
      object-inspect: registry.npmmirror.com/object-inspect@1.13.4
      side-channel-list: registry.npmmirror.com/side-channel-list@1.0.0
      side-channel-map: registry.npmmirror.com/side-channel-map@1.0.1
      side-channel-weakmap: registry.npmmirror.com/side-channel-weakmap@1.0.2

  registry.npmmirror.com/signal-exit@3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.7.tgz}
    name: signal-exit
    version: 3.0.7
    dev: true

  registry.npmmirror.com/signal-exit@4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/signal-exit/-/signal-exit-4.1.0.tgz}
    name: signal-exit
    version: 4.1.0
    engines: {node: '>=14'}

  registry.npmmirror.com/sisteransi@1.0.5:
    resolution: {integrity: sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/sisteransi/-/sisteransi-1.0.5.tgz}
    name: sisteransi
    version: 1.0.5
    dev: true

  registry.npmmirror.com/slash@3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/slash/-/slash-3.0.0.tgz}
    name: slash
    version: 3.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/snake-case@3.0.4:
    resolution: {integrity: sha512-LAOh4z89bGQvl9pFfNF8V146i7o7/CqFPbqzYgP+yYzDIDeS9HaNFtXABamRW+AQzEVODcvE79ljJ+8a9YSdMg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/snake-case/-/snake-case-3.0.4.tgz}
    name: snake-case
    version: 3.0.4
    dependencies:
      dot-case: registry.npmmirror.com/dot-case@3.0.4
      tslib: registry.npmmirror.com/tslib@2.8.1
    dev: false

  registry.npmmirror.com/snakecase-keys@8.0.1:
    resolution: {integrity: sha512-Sj51kE1zC7zh6TDlNNz0/Jn1n5HiHdoQErxO8jLtnyrkJW/M5PrI7x05uDgY3BO7OUQYKCvmeMurW6BPUdwEOw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/snakecase-keys/-/snakecase-keys-8.0.1.tgz}
    name: snakecase-keys
    version: 8.0.1
    engines: {node: '>=18'}
    dependencies:
      map-obj: registry.npmmirror.com/map-obj@4.3.0
      snake-case: registry.npmmirror.com/snake-case@3.0.4
      type-fest: registry.npmmirror.com/type-fest@4.41.0
    dev: false

  registry.npmmirror.com/sort-keys-length@1.0.1:
    resolution: {integrity: sha512-GRbEOUqCxemTAk/b32F2xa8wDTs+Z1QHOkbhJDQTvv/6G3ZkbJ+frYWsTcc7cBB3Fu4wy4XlLCuNtJuMn7Gsvw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/sort-keys-length/-/sort-keys-length-1.0.1.tgz}
    name: sort-keys-length
    version: 1.0.1
    engines: {node: '>=0.10.0'}
    dependencies:
      sort-keys: registry.npmmirror.com/sort-keys@1.1.2
    dev: true

  registry.npmmirror.com/sort-keys@1.1.2:
    resolution: {integrity: sha512-vzn8aSqKgytVik0iwdBEi+zevbTYZogewTUM6dtpmGwEcdzbub/TX4bCzRhebDCRC3QzXgJsLRKB2V/Oof7HXg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/sort-keys/-/sort-keys-1.1.2.tgz}
    name: sort-keys
    version: 1.1.2
    engines: {node: '>=0.10.0'}
    dependencies:
      is-plain-obj: registry.npmmirror.com/is-plain-obj@1.1.0
    dev: true

  registry.npmmirror.com/source-map-support@0.5.13:
    resolution: {integrity: sha512-SHSKFHadjVA5oR4PPqhtAVdcBWwRYVd6g6cAXnIbRiIwc2EhPrTuKUBdSLvlEKyIP3GCf89fltvcZiP9MMFA1w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/source-map-support/-/source-map-support-0.5.13.tgz}
    name: source-map-support
    version: 0.5.13
    dependencies:
      buffer-from: registry.npmmirror.com/buffer-from@1.1.2
      source-map: registry.npmmirror.com/source-map@0.6.1
    dev: true

  registry.npmmirror.com/source-map-support@0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/source-map-support/-/source-map-support-0.5.21.tgz}
    name: source-map-support
    version: 0.5.21
    dependencies:
      buffer-from: registry.npmmirror.com/buffer-from@1.1.2
      source-map: registry.npmmirror.com/source-map@0.6.1
    dev: true

  registry.npmmirror.com/source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz}
    name: source-map
    version: 0.6.1
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/source-map@0.7.4:
    resolution: {integrity: sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/source-map/-/source-map-0.7.4.tgz}
    name: source-map
    version: 0.7.4
    engines: {node: '>= 8'}
    dev: true

  registry.npmmirror.com/sprintf-js@1.0.3:
    resolution: {integrity: sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/sprintf-js/-/sprintf-js-1.0.3.tgz}
    name: sprintf-js
    version: 1.0.3
    dev: true

  registry.npmmirror.com/sql-highlight@6.1.0:
    resolution: {integrity: sha512-ed7OK4e9ywpE7pgRMkMQmZDPKSVdm0oX5IEtZiKnFucSF0zu6c80GZBe38UqHuVhTWJ9xsKgSMjCG2bml86KvA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/sql-highlight/-/sql-highlight-6.1.0.tgz}
    name: sql-highlight
    version: 6.1.0
    engines: {node: '>=14'}
    dev: false

  registry.npmmirror.com/sqlstring@2.3.3:
    resolution: {integrity: sha512-qC9iz2FlN7DQl3+wjwn3802RTyjCx7sDvfQEXchwa6CWOx07/WVfh91gBmQ9fahw8snwGEWU3xGzOt4tFyHLxg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/sqlstring/-/sqlstring-2.3.3.tgz}
    name: sqlstring
    version: 2.3.3
    engines: {node: '>= 0.6'}
    dev: false

  registry.npmmirror.com/sse-decoder@1.0.0:
    resolution: {integrity: sha512-JPopy3jfNmPcUz5Ru6skKhHNRJbsvcEW6Z4SirKkucLS8Jya1Bmf4FVX8giOkLm8xQJ7kK68P6GXoVSTkbedUA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/sse-decoder/-/sse-decoder-1.0.0.tgz}
    name: sse-decoder
    version: 1.0.0
    engines: {node: '>= 14.19.3'}
    dev: false

  registry.npmmirror.com/stack-utils@2.0.6:
    resolution: {integrity: sha512-XlkWvfIm6RmsWtNJx+uqtKLS8eqFbxUg0ZzLXqY0caEy9l7hruX8IpiDnjsLavoBgqCCR71TqWO8MaXYheJ3RQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/stack-utils/-/stack-utils-2.0.6.tgz}
    name: stack-utils
    version: 2.0.6
    engines: {node: '>=10'}
    dependencies:
      escape-string-regexp: registry.npmmirror.com/escape-string-regexp@2.0.0
    dev: true

  registry.npmmirror.com/statuses@2.0.1:
    resolution: {integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/statuses/-/statuses-2.0.1.tgz}
    name: statuses
    version: 2.0.1
    engines: {node: '>= 0.8'}

  registry.npmmirror.com/streamsearch@1.1.0:
    resolution: {integrity: sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/streamsearch/-/streamsearch-1.1.0.tgz}
    name: streamsearch
    version: 1.1.0
    engines: {node: '>=10.0.0'}

  registry.npmmirror.com/streamx@2.22.1:
    resolution: {integrity: sha512-znKXEBxfatz2GBNK02kRnCXjV+AA4kjZIUxeWSr3UGirZMJfTE9uiwKHobnbgxWyL/JWro8tTq+vOqAK1/qbSA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/streamx/-/streamx-2.22.1.tgz}
    name: streamx
    version: 2.22.1
    dependencies:
      fast-fifo: registry.npmmirror.com/fast-fifo@1.3.2
      text-decoder: registry.npmmirror.com/text-decoder@1.2.3
    optionalDependencies:
      bare-events: registry.npmmirror.com/bare-events@2.5.4
    dev: true

  registry.npmmirror.com/string-length@4.0.2:
    resolution: {integrity: sha512-+l6rNN5fYHNhZZy41RXsYptCjA2Igmq4EG7kZAYFQI1E1VTXarr6ZPXBg6eq7Y6eK4FEhY6AJlyuFIb/v/S0VQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/string-length/-/string-length-4.0.2.tgz}
    name: string-length
    version: 4.0.2
    engines: {node: '>=10'}
    dependencies:
      char-regex: registry.npmmirror.com/char-regex@1.0.2
      strip-ansi: registry.npmmirror.com/strip-ansi@6.0.1
    dev: true

  registry.npmmirror.com/string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz}
    name: string-width
    version: 4.2.3
    engines: {node: '>=8'}
    dependencies:
      emoji-regex: registry.npmmirror.com/emoji-regex@8.0.0
      is-fullwidth-code-point: registry.npmmirror.com/is-fullwidth-code-point@3.0.0
      strip-ansi: registry.npmmirror.com/strip-ansi@6.0.1

  registry.npmmirror.com/string-width@5.1.2:
    resolution: {integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/string-width/-/string-width-5.1.2.tgz}
    name: string-width
    version: 5.1.2
    engines: {node: '>=12'}
    dependencies:
      eastasianwidth: registry.npmmirror.com/eastasianwidth@0.2.0
      emoji-regex: registry.npmmirror.com/emoji-regex@9.2.2
      strip-ansi: registry.npmmirror.com/strip-ansi@7.1.0

  registry.npmmirror.com/string_decoder@1.1.1:
    resolution: {integrity: sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/string_decoder/-/string_decoder-1.1.1.tgz}
    name: string_decoder
    version: 1.1.1
    dependencies:
      safe-buffer: registry.npmmirror.com/safe-buffer@5.1.2

  registry.npmmirror.com/string_decoder@1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/string_decoder/-/string_decoder-1.3.0.tgz}
    name: string_decoder
    version: 1.3.0
    dependencies:
      safe-buffer: registry.npmmirror.com/safe-buffer@5.2.1
    dev: true

  registry.npmmirror.com/strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz}
    name: strip-ansi
    version: 6.0.1
    engines: {node: '>=8'}
    dependencies:
      ansi-regex: registry.npmmirror.com/ansi-regex@5.0.1

  registry.npmmirror.com/strip-ansi@7.1.0:
    resolution: {integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/strip-ansi/-/strip-ansi-7.1.0.tgz}
    name: strip-ansi
    version: 7.1.0
    engines: {node: '>=12'}
    dependencies:
      ansi-regex: registry.npmmirror.com/ansi-regex@6.1.0

  registry.npmmirror.com/strip-bom@3.0.0:
    resolution: {integrity: sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/strip-bom/-/strip-bom-3.0.0.tgz}
    name: strip-bom
    version: 3.0.0
    engines: {node: '>=4'}
    dev: true

  registry.npmmirror.com/strip-bom@4.0.0:
    resolution: {integrity: sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/strip-bom/-/strip-bom-4.0.0.tgz}
    name: strip-bom
    version: 4.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/strip-dirs@3.0.0:
    resolution: {integrity: sha512-I0sdgcFTfKQlUPZyAqPJmSG3HLO9rWDFnxonnIbskYNM3DwFOeTNB5KzVq3dA1GdRAc/25b5Y7UO2TQfKWw4aQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/strip-dirs/-/strip-dirs-3.0.0.tgz}
    name: strip-dirs
    version: 3.0.0
    dependencies:
      inspect-with-kind: registry.npmmirror.com/inspect-with-kind@1.0.5
      is-plain-obj: registry.npmmirror.com/is-plain-obj@1.1.0
    dev: true

  registry.npmmirror.com/strip-final-newline@2.0.0:
    resolution: {integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/strip-final-newline/-/strip-final-newline-2.0.0.tgz}
    name: strip-final-newline
    version: 2.0.0
    engines: {node: '>=6'}
    dev: true

  registry.npmmirror.com/strip-json-comments@3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/strip-json-comments/-/strip-json-comments-3.1.1.tgz}
    name: strip-json-comments
    version: 3.1.1
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/strtok3@9.1.1:
    resolution: {integrity: sha512-FhwotcEqjr241ZbjFzjlIYg6c5/L/s4yBGWSMvJ9UoExiSqL+FnFA/CaeZx17WGaZMS/4SOZp8wH18jSS4R4lw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/strtok3/-/strtok3-9.1.1.tgz}
    name: strtok3
    version: 9.1.1
    engines: {node: '>=16'}
    dependencies:
      '@tokenizer/token': registry.npmmirror.com/@tokenizer/token@0.3.0
      peek-readable: registry.npmmirror.com/peek-readable@5.4.2
    dev: true

  registry.npmmirror.com/superagent@9.0.2:
    resolution: {integrity: sha512-xuW7dzkUpcJq7QnhOsnNUgtYp3xRwpt2F7abdRYIpCsAt0hhUqia0EdxyXZQQpNmGtsCzYHryaKSV3q3GJnq7w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/superagent/-/superagent-9.0.2.tgz}
    name: superagent
    version: 9.0.2
    engines: {node: '>=14.18.0'}
    dependencies:
      component-emitter: registry.npmmirror.com/component-emitter@1.3.1
      cookiejar: registry.npmmirror.com/cookiejar@2.1.4
      debug: registry.npmmirror.com/debug@4.4.1
      fast-safe-stringify: registry.npmmirror.com/fast-safe-stringify@2.1.1
      form-data: registry.npmmirror.com/form-data@4.0.3
      formidable: registry.npmmirror.com/formidable@3.5.4
      methods: registry.npmmirror.com/methods@1.1.2
      mime: registry.npmmirror.com/mime@2.6.0
      qs: registry.npmmirror.com/qs@6.14.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/supertest@7.0.0:
    resolution: {integrity: sha512-qlsr7fIC0lSddmA3tzojvzubYxvlGtzumcdHgPwbFWMISQwL22MhM2Y3LNt+6w9Yyx7559VW5ab70dgphm8qQA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/supertest/-/supertest-7.0.0.tgz}
    name: supertest
    version: 7.0.0
    engines: {node: '>=14.18.0'}
    dependencies:
      methods: registry.npmmirror.com/methods@1.1.2
      superagent: registry.npmmirror.com/superagent@9.0.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz}
    name: supports-color
    version: 7.2.0
    engines: {node: '>=8'}
    dependencies:
      has-flag: registry.npmmirror.com/has-flag@4.0.0
    dev: true

  registry.npmmirror.com/supports-color@8.1.1:
    resolution: {integrity: sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/supports-color/-/supports-color-8.1.1.tgz}
    name: supports-color
    version: 8.1.1
    engines: {node: '>=10'}
    dependencies:
      has-flag: registry.npmmirror.com/has-flag@4.0.0
    dev: true

  registry.npmmirror.com/supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz}
    name: supports-preserve-symlinks-flag
    version: 1.0.0
    engines: {node: '>= 0.4'}
    dev: true

  registry.npmmirror.com/swagger-ui-dist@5.21.0:
    resolution: {integrity: sha512-E0K3AB6HvQd8yQNSMR7eE5bk+323AUxjtCz/4ZNKiahOlPhPJxqn3UPIGs00cyY/dhrTDJ61L7C/a8u6zhGrZg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/swagger-ui-dist/-/swagger-ui-dist-5.21.0.tgz}
    name: swagger-ui-dist
    version: 5.21.0
    dependencies:
      '@scarf/scarf': registry.npmmirror.com/@scarf/scarf@1.4.0
    dev: false

  registry.npmmirror.com/swagger-ui-dist@5.25.3:
    resolution: {integrity: sha512-mqWJAhfl8mhVKJezwszUqRJAlrvKG/22am5xRUWzr7ya0MFaFBAAd7Nm+tD4BdKnVx7KRWkWYJMYRkFm5a8iTg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/swagger-ui-dist/-/swagger-ui-dist-5.25.3.tgz}
    name: swagger-ui-dist
    version: 5.25.3
    dependencies:
      '@scarf/scarf': registry.npmmirror.com/@scarf/scarf@1.4.0
    dev: false

  registry.npmmirror.com/swagger-ui-express@5.0.1(express@5.0.1):
    resolution: {integrity: sha512-SrNU3RiBGTLLmFU8GIJdOdanJTl4TOmT27tt3bWWHppqYmAZ6IDuEuBvMU6nZq0zLEe6b/1rACXCgLZqO6ZfrA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/swagger-ui-express/-/swagger-ui-express-5.0.1.tgz}
    id: registry.npmmirror.com/swagger-ui-express/5.0.1
    name: swagger-ui-express
    version: 5.0.1
    engines: {node: '>= v0.10.32'}
    peerDependencies:
      express: '>=4.0.0 || >=5.0.0-beta'
    dependencies:
      express: registry.npmmirror.com/express@5.0.1
      swagger-ui-dist: registry.npmmirror.com/swagger-ui-dist@5.25.3
    dev: false

  registry.npmmirror.com/symbol-observable@4.0.0:
    resolution: {integrity: sha512-b19dMThMV4HVFynSAM1++gBHAbk2Tc/osgLIBZMKsyqh34jb2e8Os7T6ZW/Bt3pJFdBTd2JwAnAAEQV7rSNvcQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/symbol-observable/-/symbol-observable-4.0.0.tgz}
    name: symbol-observable
    version: 4.0.0
    engines: {node: '>=0.10'}
    dev: true

  registry.npmmirror.com/synckit@0.9.3:
    resolution: {integrity: sha512-JJoOEKTfL1urb1mDoEblhD9NhEbWmq9jHEMEnxoC4ujUaZ4itA8vKgwkFAyNClgxplLi9tsUKX+EduK0p/l7sg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/synckit/-/synckit-0.9.3.tgz}
    name: synckit
    version: 0.9.3
    engines: {node: ^14.18.0 || >=16.0.0}
    dependencies:
      '@pkgr/core': registry.npmmirror.com/@pkgr/core@0.1.2
      tslib: registry.npmmirror.com/tslib@2.8.1
    dev: true

  registry.npmmirror.com/tapable@2.2.2:
    resolution: {integrity: sha512-Re10+NauLTMCudc7T5WLFLAwDhQ0JWdrMK+9B2M8zR5hRExKmsRDCBA7/aV/pNJFltmBFO5BAMlQFi/vq3nKOg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/tapable/-/tapable-2.2.2.tgz}
    name: tapable
    version: 2.2.2
    engines: {node: '>=6'}
    dev: true

  registry.npmmirror.com/tar-stream@3.1.7:
    resolution: {integrity: sha512-qJj60CXt7IU1Ffyc3NJMjh6EkuCFej46zUqJ4J7pqYlThyd9bO0XBTmcOIhSzZJVWfsLks0+nle/j538YAW9RQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/tar-stream/-/tar-stream-3.1.7.tgz}
    name: tar-stream
    version: 3.1.7
    dependencies:
      b4a: registry.npmmirror.com/b4a@1.6.7
      fast-fifo: registry.npmmirror.com/fast-fifo@1.3.2
      streamx: registry.npmmirror.com/streamx@2.22.1
    dev: true

  registry.npmmirror.com/terser-webpack-plugin@5.3.14(@swc/core@1.10.7)(webpack@5.97.1):
    resolution: {integrity: sha512-vkZjpUjb6OMS7dhV+tILUW6BhpDR7P2L/aQSAv+Uwk+m8KATX9EccViHTJR2qDtACKPIYndLGCyl3FMo+r2LMw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/terser-webpack-plugin/-/terser-webpack-plugin-5.3.14.tgz}
    id: registry.npmmirror.com/terser-webpack-plugin/5.3.14
    name: terser-webpack-plugin
    version: 5.3.14
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      '@swc/core': '*'
      esbuild: '*'
      uglify-js: '*'
      webpack: ^5.1.0
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      esbuild:
        optional: true
      uglify-js:
        optional: true
    dependencies:
      '@jridgewell/trace-mapping': registry.npmmirror.com/@jridgewell/trace-mapping@0.3.25
      '@swc/core': registry.npmmirror.com/@swc/core@1.10.7
      jest-worker: registry.npmmirror.com/jest-worker@27.5.1
      schema-utils: registry.npmmirror.com/schema-utils@4.3.2
      serialize-javascript: registry.npmmirror.com/serialize-javascript@6.0.2
      terser: registry.npmmirror.com/terser@5.43.1
      webpack: registry.npmmirror.com/webpack@5.97.1(@swc/core@1.10.7)
    dev: true

  registry.npmmirror.com/terser-webpack-plugin@5.3.14(@swc/core@1.10.7)(webpack@5.99.9):
    resolution: {integrity: sha512-vkZjpUjb6OMS7dhV+tILUW6BhpDR7P2L/aQSAv+Uwk+m8KATX9EccViHTJR2qDtACKPIYndLGCyl3FMo+r2LMw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/terser-webpack-plugin/-/terser-webpack-plugin-5.3.14.tgz}
    id: registry.npmmirror.com/terser-webpack-plugin/5.3.14
    name: terser-webpack-plugin
    version: 5.3.14
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      '@swc/core': '*'
      esbuild: '*'
      uglify-js: '*'
      webpack: ^5.1.0
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      esbuild:
        optional: true
      uglify-js:
        optional: true
    dependencies:
      '@jridgewell/trace-mapping': registry.npmmirror.com/@jridgewell/trace-mapping@0.3.25
      '@swc/core': registry.npmmirror.com/@swc/core@1.10.7
      jest-worker: registry.npmmirror.com/jest-worker@27.5.1
      schema-utils: registry.npmmirror.com/schema-utils@4.3.2
      serialize-javascript: registry.npmmirror.com/serialize-javascript@6.0.2
      terser: registry.npmmirror.com/terser@5.43.1
      webpack: registry.npmmirror.com/webpack@5.99.9(@swc/core@1.10.7)
    dev: true

  registry.npmmirror.com/terser@5.43.1:
    resolution: {integrity: sha512-+6erLbBm0+LROX2sPXlUYx/ux5PyE9K/a92Wrt6oA+WDAoFTdpHE5tCYCI5PNzq2y8df4rA+QgHLJuR4jNymsg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/terser/-/terser-5.43.1.tgz}
    name: terser
    version: 5.43.1
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      '@jridgewell/source-map': registry.npmmirror.com/@jridgewell/source-map@0.3.6
      acorn: registry.npmmirror.com/acorn@8.15.0
      commander: registry.npmmirror.com/commander@2.20.3
      source-map-support: registry.npmmirror.com/source-map-support@0.5.21
    dev: true

  registry.npmmirror.com/test-exclude@6.0.0:
    resolution: {integrity: sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/test-exclude/-/test-exclude-6.0.0.tgz}
    name: test-exclude
    version: 6.0.0
    engines: {node: '>=8'}
    dependencies:
      '@istanbuljs/schema': registry.npmmirror.com/@istanbuljs/schema@0.1.3
      glob: registry.npmmirror.com/glob@7.2.3
      minimatch: registry.npmmirror.com/minimatch@3.1.2
    dev: true

  registry.npmmirror.com/text-decoder@1.2.3:
    resolution: {integrity: sha512-3/o9z3X0X0fTupwsYvR03pJ/DjWuqqrfwBgTQzdWDiQSm9KitAyz/9WqsT2JQW7KV2m+bC2ol/zqpW37NHxLaA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/text-decoder/-/text-decoder-1.2.3.tgz}
    name: text-decoder
    version: 1.2.3
    dependencies:
      b4a: registry.npmmirror.com/b4a@1.6.7
    dev: true

  registry.npmmirror.com/through@2.3.8:
    resolution: {integrity: sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/through/-/through-2.3.8.tgz}
    name: through
    version: 2.3.8

  registry.npmmirror.com/tmp@0.0.33:
    resolution: {integrity: sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/tmp/-/tmp-0.0.33.tgz}
    name: tmp
    version: 0.0.33
    engines: {node: '>=0.6.0'}
    dependencies:
      os-tmpdir: registry.npmmirror.com/os-tmpdir@1.0.2
    dev: true

  registry.npmmirror.com/tmpl@1.0.5:
    resolution: {integrity: sha512-3f0uOEAQwIqGuWW2MVzYg8fV/QNnc/IpuJNG837rLuczAaLVHslWHZQj4IGiEl5Hs3kkbhwL9Ab7Hrsmuj+Smw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/tmpl/-/tmpl-1.0.5.tgz}
    name: tmpl
    version: 1.0.5
    dev: true

  registry.npmmirror.com/to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz}
    name: to-regex-range
    version: 5.0.1
    engines: {node: '>=8.0'}
    dependencies:
      is-number: registry.npmmirror.com/is-number@7.0.0
    dev: true

  registry.npmmirror.com/toidentifier@1.0.1:
    resolution: {integrity: sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/toidentifier/-/toidentifier-1.0.1.tgz}
    name: toidentifier
    version: 1.0.1
    engines: {node: '>=0.6'}

  registry.npmmirror.com/token-types@6.0.3:
    resolution: {integrity: sha512-IKJ6EzuPPWtKtEIEPpIdXv9j5j2LGJEYk0CKY2efgKoYKLBiZdh6iQkLVBow/CB3phyWAWCyk+bZeaimJn6uRQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/token-types/-/token-types-6.0.3.tgz}
    name: token-types
    version: 6.0.3
    engines: {node: '>=14.16'}
    dependencies:
      '@tokenizer/token': registry.npmmirror.com/@tokenizer/token@0.3.0
      ieee754: registry.npmmirror.com/ieee754@1.2.1
    dev: true

  registry.npmmirror.com/tree-kill@1.2.2:
    resolution: {integrity: sha512-L0Orpi8qGpRG//Nd+H90vFB+3iHnue1zSSGmNOOCh1GLJ7rUKVwV2HvijphGQS2UmhUZewS9VgvxYIdgr+fG1A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/tree-kill/-/tree-kill-1.2.2.tgz}
    name: tree-kill
    version: 1.2.2
    hasBin: true
    dev: true

  registry.npmmirror.com/ts-api-utils@2.1.0(typescript@5.7.3):
    resolution: {integrity: sha512-CUgTZL1irw8u29bzrOD/nH85jqyc74D6SshFgujOIA7osm2Rz7dYH77agkx7H4FBNxDq7Cjf+IjaX/8zwFW+ZQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ts-api-utils/-/ts-api-utils-2.1.0.tgz}
    id: registry.npmmirror.com/ts-api-utils/2.1.0
    name: ts-api-utils
    version: 2.1.0
    engines: {node: '>=18.12'}
    peerDependencies:
      typescript: '>=4.8.4'
    dependencies:
      typescript: registry.npmmirror.com/typescript@5.7.3
    dev: true

  registry.npmmirror.com/ts-jest@29.2.5(@babel/core@7.27.7)(jest@29.7.0)(typescript@5.7.3):
    resolution: {integrity: sha512-KD8zB2aAZrcKIdGk4OwpJggeLcH1FgrICqDSROWqlnJXGCXK4Mn6FcdK2B6670Xr73lHMG1kHw8R87A0ecZ+vA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ts-jest/-/ts-jest-29.2.5.tgz}
    id: registry.npmmirror.com/ts-jest/29.2.5
    name: ts-jest
    version: 29.2.5
    engines: {node: ^14.15.0 || ^16.10.0 || ^18.0.0 || >=20.0.0}
    hasBin: true
    peerDependencies:
      '@babel/core': '>=7.0.0-beta.0 <8'
      '@jest/transform': ^29.0.0
      '@jest/types': ^29.0.0
      babel-jest: ^29.0.0
      esbuild: '*'
      jest: ^29.0.0
      typescript: '>=4.3 <6'
    peerDependenciesMeta:
      '@babel/core':
        optional: true
      '@jest/transform':
        optional: true
      '@jest/types':
        optional: true
      babel-jest:
        optional: true
      esbuild:
        optional: true
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.7
      bs-logger: registry.npmmirror.com/bs-logger@0.2.6
      ejs: registry.npmmirror.com/ejs@3.1.10
      fast-json-stable-stringify: registry.npmmirror.com/fast-json-stable-stringify@2.1.0
      jest: registry.npmmirror.com/jest@29.7.0(@types/node@22.10.7)(ts-node@10.9.2)
      jest-util: registry.npmmirror.com/jest-util@29.7.0
      json5: registry.npmmirror.com/json5@2.2.3
      lodash.memoize: registry.npmmirror.com/lodash.memoize@4.1.2
      make-error: registry.npmmirror.com/make-error@1.3.6
      semver: registry.npmmirror.com/semver@7.7.2
      typescript: registry.npmmirror.com/typescript@5.7.3
      yargs-parser: registry.npmmirror.com/yargs-parser@21.1.1
    dev: true

  registry.npmmirror.com/ts-loader@9.5.2(typescript@5.7.3)(webpack@5.99.9):
    resolution: {integrity: sha512-Qo4piXvOTWcMGIgRiuFa6nHNm+54HbYaZCKqc9eeZCLRy3XqafQgwX2F7mofrbJG3g7EEb+lkiR+z2Lic2s3Zw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ts-loader/-/ts-loader-9.5.2.tgz}
    id: registry.npmmirror.com/ts-loader/9.5.2
    name: ts-loader
    version: 9.5.2
    engines: {node: '>=12.0.0'}
    peerDependencies:
      typescript: '*'
      webpack: ^5.0.0
    dependencies:
      chalk: registry.npmmirror.com/chalk@4.1.2
      enhanced-resolve: registry.npmmirror.com/enhanced-resolve@5.18.2
      micromatch: registry.npmmirror.com/micromatch@4.0.8
      semver: registry.npmmirror.com/semver@7.7.2
      source-map: registry.npmmirror.com/source-map@0.7.4
      typescript: registry.npmmirror.com/typescript@5.7.3
      webpack: registry.npmmirror.com/webpack@5.99.9(@swc/core@1.10.7)
    dev: true

  registry.npmmirror.com/ts-node@10.9.2(@swc/core@1.10.7)(@types/node@22.10.7)(typescript@5.7.3):
    resolution: {integrity: sha512-f0FFpIdcHgn8zcPSbf1dRevwt047YMnaiJM3u2w2RewrB+fob/zePZcrOyQoLMMO7aBIddLcQIEK5dYjkLnGrQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ts-node/-/ts-node-10.9.2.tgz}
    id: registry.npmmirror.com/ts-node/10.9.2
    name: ts-node
    version: 10.9.2
    hasBin: true
    peerDependencies:
      '@swc/core': '>=1.2.50'
      '@swc/wasm': '>=1.2.50'
      '@types/node': '*'
      typescript: '>=2.7'
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      '@swc/wasm':
        optional: true
    dependencies:
      '@cspotcode/source-map-support': registry.npmmirror.com/@cspotcode/source-map-support@0.8.1
      '@swc/core': registry.npmmirror.com/@swc/core@1.10.7
      '@tsconfig/node10': registry.npmmirror.com/@tsconfig/node10@1.0.11
      '@tsconfig/node12': registry.npmmirror.com/@tsconfig/node12@1.0.11
      '@tsconfig/node14': registry.npmmirror.com/@tsconfig/node14@1.0.3
      '@tsconfig/node16': registry.npmmirror.com/@tsconfig/node16@1.0.4
      '@types/node': registry.npmmirror.com/@types/node@22.10.7
      acorn: registry.npmmirror.com/acorn@8.15.0
      acorn-walk: registry.npmmirror.com/acorn-walk@8.3.4
      arg: registry.npmmirror.com/arg@4.1.3
      create-require: registry.npmmirror.com/create-require@1.1.1
      diff: registry.npmmirror.com/diff@4.0.2
      make-error: registry.npmmirror.com/make-error@1.3.6
      typescript: registry.npmmirror.com/typescript@5.7.3
      v8-compile-cache-lib: registry.npmmirror.com/v8-compile-cache-lib@3.0.1
      yn: registry.npmmirror.com/yn@3.1.1

  registry.npmmirror.com/tsconfig-paths-webpack-plugin@4.2.0:
    resolution: {integrity: sha512-zbem3rfRS8BgeNK50Zz5SIQgXzLafiHjOwUAvk/38/o1jHn/V5QAgVUcz884or7WYcPaH3N2CIfUc2u0ul7UcA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/tsconfig-paths-webpack-plugin/-/tsconfig-paths-webpack-plugin-4.2.0.tgz}
    name: tsconfig-paths-webpack-plugin
    version: 4.2.0
    engines: {node: '>=10.13.0'}
    dependencies:
      chalk: registry.npmmirror.com/chalk@4.1.2
      enhanced-resolve: registry.npmmirror.com/enhanced-resolve@5.18.2
      tapable: registry.npmmirror.com/tapable@2.2.2
      tsconfig-paths: registry.npmmirror.com/tsconfig-paths@4.2.0
    dev: true

  registry.npmmirror.com/tsconfig-paths@4.2.0:
    resolution: {integrity: sha512-NoZ4roiN7LnbKn9QqE1amc9DJfzvZXxF4xDavcOWt1BPkdx+m+0gJuPM+S0vCe7zTJMYUP0R8pO2XMr+Y8oLIg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/tsconfig-paths/-/tsconfig-paths-4.2.0.tgz}
    name: tsconfig-paths
    version: 4.2.0
    engines: {node: '>=6'}
    dependencies:
      json5: registry.npmmirror.com/json5@2.2.3
      minimist: registry.npmmirror.com/minimist@1.2.8
      strip-bom: registry.npmmirror.com/strip-bom@3.0.0
    dev: true

  registry.npmmirror.com/tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/tslib/-/tslib-2.8.1.tgz}
    name: tslib
    version: 2.8.1

  registry.npmmirror.com/tweetnacl@1.0.3:
    resolution: {integrity: sha512-6rt+RN7aOi1nGMyC4Xa5DdYiukl2UWCbcJft7YhxReBGQD7OAM8Pbxw6YMo4r2diNEA8FEmu32YOn9rhaiE5yw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/tweetnacl/-/tweetnacl-1.0.3.tgz}
    name: tweetnacl
    version: 1.0.3
    dev: false

  registry.npmmirror.com/type-check@0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/type-check/-/type-check-0.4.0.tgz}
    name: type-check
    version: 0.4.0
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: registry.npmmirror.com/prelude-ls@1.2.1
    dev: true

  registry.npmmirror.com/type-detect@4.0.8:
    resolution: {integrity: sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/type-detect/-/type-detect-4.0.8.tgz}
    name: type-detect
    version: 4.0.8
    engines: {node: '>=4'}
    dev: true

  registry.npmmirror.com/type-fest@0.21.3:
    resolution: {integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/type-fest/-/type-fest-0.21.3.tgz}
    name: type-fest
    version: 0.21.3
    engines: {node: '>=10'}
    dev: true

  registry.npmmirror.com/type-fest@1.4.0:
    resolution: {integrity: sha512-yGSza74xk0UG8k+pLh5oeoYirvIiWo5t0/o3zHHAO2tRDiZcxWP7fywNlXhqb6/r6sWvwi+RsyQMWhVLe4BVuA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/type-fest/-/type-fest-1.4.0.tgz}
    name: type-fest
    version: 1.4.0
    engines: {node: '>=10'}
    dev: false

  registry.npmmirror.com/type-fest@4.41.0:
    resolution: {integrity: sha512-TeTSQ6H5YHvpqVwBRcnLDCBnDOHWYu7IvGbHT6N8AOymcr9PJGjc1GTtiWZTYg0NCgYwvnYWEkVChQAr9bjfwA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/type-fest/-/type-fest-4.41.0.tgz}
    name: type-fest
    version: 4.41.0
    engines: {node: '>=16'}
    dev: false

  registry.npmmirror.com/type-is@1.6.18:
    resolution: {integrity: sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/type-is/-/type-is-1.6.18.tgz}
    name: type-is
    version: 1.6.18
    engines: {node: '>= 0.6'}
    dependencies:
      media-typer: registry.npmmirror.com/media-typer@0.3.0
      mime-types: registry.npmmirror.com/mime-types@2.1.35

  registry.npmmirror.com/type-is@2.0.1:
    resolution: {integrity: sha512-OZs6gsjF4vMp32qrCbiVSkrFmXtG/AZhY3t0iAMrMBiAZyV9oALtXO8hsrHbMXF9x6L3grlFuwW2oAz7cav+Gw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/type-is/-/type-is-2.0.1.tgz}
    name: type-is
    version: 2.0.1
    engines: {node: '>= 0.6'}
    dependencies:
      content-type: registry.npmmirror.com/content-type@1.0.5
      media-typer: registry.npmmirror.com/media-typer@1.1.0
      mime-types: registry.npmmirror.com/mime-types@3.0.1

  registry.npmmirror.com/typedarray@0.0.6:
    resolution: {integrity: sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/typedarray/-/typedarray-0.0.6.tgz}
    name: typedarray
    version: 0.0.6

  registry.npmmirror.com/typeorm@0.3.25(mysql2@3.14.1)(reflect-metadata@0.2.2)(ts-node@10.9.2):
    resolution: {integrity: sha512-fTKDFzWXKwAaBdEMU4k661seZewbNYET4r1J/z3Jwf+eAvlzMVpTLKAVcAzg75WwQk7GDmtsmkZ5MfkmXCiFWg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/typeorm/-/typeorm-0.3.25.tgz}
    id: registry.npmmirror.com/typeorm/0.3.25
    name: typeorm
    version: 0.3.25
    engines: {node: '>=16.13.0'}
    hasBin: true
    peerDependencies:
      '@google-cloud/spanner': ^5.18.0 || ^6.0.0 || ^7.0.0
      '@sap/hana-client': ^2.12.25
      better-sqlite3: ^8.0.0 || ^9.0.0 || ^10.0.0 || ^11.0.0
      hdb-pool: ^0.1.6
      ioredis: ^5.0.4
      mongodb: ^5.8.0 || ^6.0.0
      mssql: ^9.1.1 || ^10.0.1 || ^11.0.1
      mysql2: ^2.2.5 || ^3.0.1
      oracledb: ^6.3.0
      pg: ^8.5.1
      pg-native: ^3.0.0
      pg-query-stream: ^4.0.0
      redis: ^3.1.1 || ^4.0.0
      reflect-metadata: ^0.1.14 || ^0.2.0
      sql.js: ^1.4.0
      sqlite3: ^5.0.3
      ts-node: ^10.7.0
      typeorm-aurora-data-api-driver: ^2.0.0 || ^3.0.0
    peerDependenciesMeta:
      '@google-cloud/spanner':
        optional: true
      '@sap/hana-client':
        optional: true
      better-sqlite3:
        optional: true
      hdb-pool:
        optional: true
      ioredis:
        optional: true
      mongodb:
        optional: true
      mssql:
        optional: true
      mysql2:
        optional: true
      oracledb:
        optional: true
      pg:
        optional: true
      pg-native:
        optional: true
      pg-query-stream:
        optional: true
      redis:
        optional: true
      sql.js:
        optional: true
      sqlite3:
        optional: true
      ts-node:
        optional: true
      typeorm-aurora-data-api-driver:
        optional: true
    dependencies:
      '@sqltools/formatter': registry.npmmirror.com/@sqltools/formatter@1.2.5
      ansis: registry.npmmirror.com/ansis@3.17.0
      app-root-path: registry.npmmirror.com/app-root-path@3.1.0
      buffer: registry.npmmirror.com/buffer@6.0.3
      dayjs: registry.npmmirror.com/dayjs@1.11.13
      debug: registry.npmmirror.com/debug@4.4.1
      dedent: registry.npmmirror.com/dedent@1.6.0
      dotenv: registry.npmmirror.com/dotenv@16.6.1
      glob: registry.npmmirror.com/glob@10.4.5
      mysql2: registry.npmmirror.com/mysql2@3.14.1
      reflect-metadata: registry.npmmirror.com/reflect-metadata@0.2.2
      sha.js: registry.npmmirror.com/sha.js@2.4.11
      sql-highlight: registry.npmmirror.com/sql-highlight@6.1.0
      ts-node: registry.npmmirror.com/ts-node@10.9.2(@swc/core@1.10.7)(@types/node@22.10.7)(typescript@5.7.3)
      tslib: registry.npmmirror.com/tslib@2.8.1
      uuid: registry.npmmirror.com/uuid@11.1.0
      yargs: registry.npmmirror.com/yargs@17.7.2
    transitivePeerDependencies:
      - babel-plugin-macros
      - supports-color
    dev: false

  registry.npmmirror.com/typescript-eslint@8.20.0(eslint@9.18.0)(typescript@5.7.3):
    resolution: {integrity: sha512-Kxz2QRFsgbWj6Xcftlw3Dd154b3cEPFqQC+qMZrMypSijPd4UanKKvoKDrJ4o8AIfZFKAF+7sMaEIR8mTElozA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/typescript-eslint/-/typescript-eslint-8.20.0.tgz}
    id: registry.npmmirror.com/typescript-eslint/8.20.0
    name: typescript-eslint
    version: 8.20.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.8.0'
    dependencies:
      '@typescript-eslint/eslint-plugin': registry.npmmirror.com/@typescript-eslint/eslint-plugin@8.20.0(@typescript-eslint/parser@8.20.0)(eslint@9.18.0)(typescript@5.7.3)
      '@typescript-eslint/parser': registry.npmmirror.com/@typescript-eslint/parser@8.20.0(eslint@9.18.0)(typescript@5.7.3)
      '@typescript-eslint/utils': registry.npmmirror.com/@typescript-eslint/utils@8.20.0(eslint@9.18.0)(typescript@5.7.3)
      eslint: registry.npmmirror.com/eslint@9.18.0
      typescript: registry.npmmirror.com/typescript@5.7.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/typescript@5.7.3:
    resolution: {integrity: sha512-84MVSjMEHP+FQRPy3pX9sTVV/INIex71s9TL2Gm5FG/WG1SqXeKyZ0k7/blY/4FdOzI12CBy1vGc4og/eus0fw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/typescript/-/typescript-5.7.3.tgz}
    name: typescript
    version: 5.7.3
    engines: {node: '>=14.17'}
    hasBin: true

  registry.npmmirror.com/uid@2.0.2:
    resolution: {integrity: sha512-u3xV3X7uzvi5b1MncmZo3i2Aw222Zk1keqLA1YkHldREkAhAqi65wuPfe7lHx8H/Wzy+8CE7S7uS3jekIM5s8g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/uid/-/uid-2.0.2.tgz}
    name: uid
    version: 2.0.2
    engines: {node: '>=8'}
    dependencies:
      '@lukeed/csprng': registry.npmmirror.com/@lukeed/csprng@1.1.0

  registry.npmmirror.com/uint8array-extras@1.4.0:
    resolution: {integrity: sha512-ZPtzy0hu4cZjv3z5NW9gfKnNLjoz4y6uv4HlelAjDK7sY/xOkKZv9xK/WQpcsBB3jEybChz9DPC2U/+cusjJVQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/uint8array-extras/-/uint8array-extras-1.4.0.tgz}
    name: uint8array-extras
    version: 1.4.0
    engines: {node: '>=18'}
    dev: true

  registry.npmmirror.com/unbzip2-stream@1.4.3:
    resolution: {integrity: sha512-mlExGW4w71ebDJviH16lQLtZS32VKqsSfk80GCfUlwT/4/hNRFsoscrF/c++9xinkMzECL1uL9DDwXqFWkruPg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/unbzip2-stream/-/unbzip2-stream-1.4.3.tgz}
    name: unbzip2-stream
    version: 1.4.3
    dependencies:
      buffer: registry.npmmirror.com/buffer@5.7.1
      through: registry.npmmirror.com/through@2.3.8
    dev: true

  registry.npmmirror.com/undici-types@6.20.0:
    resolution: {integrity: sha512-Ny6QZ2Nju20vw1SRHe3d9jVu6gJ+4e3+MMpqu7pqE5HT6WsTSlce++GQmK5UXS8mzV8DSYHrQH+Xrf2jVcuKNg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/undici-types/-/undici-types-6.20.0.tgz}
    name: undici-types
    version: 6.20.0

  registry.npmmirror.com/undici@7.11.0:
    resolution: {integrity: sha512-heTSIac3iLhsmZhUCjyS3JQEkZELateufzZuBaVM5RHXdSBMb1LPMQf5x+FH7qjsZYDP0ttAc3nnVpUB+wYbOg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/undici/-/undici-7.11.0.tgz}
    name: undici
    version: 7.11.0
    engines: {node: '>=20.18.1'}
    dev: false

  registry.npmmirror.com/unescape@1.0.1:
    resolution: {integrity: sha512-O0+af1Gs50lyH1nUu3ZyYS1cRh01Q/kUKatTOkSs7jukXE6/NebucDVxyiDsA9AQ4JC1V1jUH9EO8JX2nMDgGQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/unescape/-/unescape-1.0.1.tgz}
    name: unescape
    version: 1.0.1
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: registry.npmmirror.com/extend-shallow@2.0.1
    dev: false

  registry.npmmirror.com/universalify@2.0.1:
    resolution: {integrity: sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/universalify/-/universalify-2.0.1.tgz}
    name: universalify
    version: 2.0.1
    engines: {node: '>= 10.0.0'}
    dev: true

  registry.npmmirror.com/unpipe@1.0.0:
    resolution: {integrity: sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/unpipe/-/unpipe-1.0.0.tgz}
    name: unpipe
    version: 1.0.0
    engines: {node: '>= 0.8'}

  registry.npmmirror.com/update-browserslist-db@1.1.3(browserslist@4.25.1):
    resolution: {integrity: sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz}
    id: registry.npmmirror.com/update-browserslist-db/1.1.3
    name: update-browserslist-db
    version: 1.1.3
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'
    dependencies:
      browserslist: registry.npmmirror.com/browserslist@4.25.1
      escalade: registry.npmmirror.com/escalade@3.2.0
      picocolors: registry.npmmirror.com/picocolors@1.1.1
    dev: true

  registry.npmmirror.com/uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/uri-js/-/uri-js-4.4.1.tgz}
    name: uri-js
    version: 4.4.1
    dependencies:
      punycode: registry.npmmirror.com/punycode@2.3.1
    dev: true

  registry.npmmirror.com/urllib@4.6.12:
    resolution: {integrity: sha512-TGo3hUyHOiIOGtdvGuZm8gLgBHOVNq3R3KKAFS3ov1d8NJNN1BD/9DdQoXgX8NU25zlrScVfF/lj/A++2R3WTA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/urllib/-/urllib-4.6.12.tgz}
    name: urllib
    version: 4.6.12
    engines: {node: '>= 18.19.0'}
    dependencies:
      form-data: registry.npmmirror.com/form-data@4.0.3
      formstream: registry.npmmirror.com/formstream@1.5.1
      mime-types: registry.npmmirror.com/mime-types@2.1.35
      qs: registry.npmmirror.com/qs@6.14.0
      type-fest: registry.npmmirror.com/type-fest@4.41.0
      undici: registry.npmmirror.com/undici@7.11.0
      ylru: registry.npmmirror.com/ylru@2.0.0
    dev: false

  registry.npmmirror.com/util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/util-deprecate/-/util-deprecate-1.0.2.tgz}
    name: util-deprecate
    version: 1.0.2

  registry.npmmirror.com/utility@2.5.0:
    resolution: {integrity: sha512-lDbOVde5UAKgtxrSyZNhqrTA7f7anba6DTqbsDWgUFk6PZlmr7djqPYw0FnL5a6TbJvRt38VmYqt07zVLzXG2A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/utility/-/utility-2.5.0.tgz}
    name: utility
    version: 2.5.0
    engines: {node: '>= 16.0.0'}
    dependencies:
      escape-html: registry.npmmirror.com/escape-html@1.0.3
      unescape: registry.npmmirror.com/unescape@1.0.1
      ylru: registry.npmmirror.com/ylru@2.0.0
    dev: false

  registry.npmmirror.com/utils-merge@1.0.1:
    resolution: {integrity: sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/utils-merge/-/utils-merge-1.0.1.tgz}
    name: utils-merge
    version: 1.0.1
    engines: {node: '>= 0.4.0'}

  registry.npmmirror.com/uuid@11.1.0:
    resolution: {integrity: sha512-0/A9rDy9P7cJ+8w1c9WD9V//9Wj15Ce2MPz8Ri6032usz+NfePxx5AcN3bN+r6ZL6jEo066/yNYB3tn4pQEx+A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/uuid/-/uuid-11.1.0.tgz}
    name: uuid
    version: 11.1.0
    hasBin: true
    dev: false

  registry.npmmirror.com/v8-compile-cache-lib@3.0.1:
    resolution: {integrity: sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/v8-compile-cache-lib/-/v8-compile-cache-lib-3.0.1.tgz}
    name: v8-compile-cache-lib
    version: 3.0.1

  registry.npmmirror.com/v8-to-istanbul@9.3.0:
    resolution: {integrity: sha512-kiGUalWN+rgBJ/1OHZsBtU4rXZOfj/7rKQxULKlIzwzQSvMJUUNgPwJEEh7gU6xEVxC0ahoOBvN2YI8GH6FNgA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/v8-to-istanbul/-/v8-to-istanbul-9.3.0.tgz}
    name: v8-to-istanbul
    version: 9.3.0
    engines: {node: '>=10.12.0'}
    dependencies:
      '@jridgewell/trace-mapping': registry.npmmirror.com/@jridgewell/trace-mapping@0.3.25
      '@types/istanbul-lib-coverage': registry.npmmirror.com/@types/istanbul-lib-coverage@2.0.6
      convert-source-map: registry.npmmirror.com/convert-source-map@2.0.0
    dev: true

  registry.npmmirror.com/validator@13.15.15:
    resolution: {integrity: sha512-BgWVbCI72aIQy937xbawcs+hrVaN/CZ2UwutgaJ36hGqRrLNM+f5LUT/YPRbo8IV/ASeFzXszezV+y2+rq3l8A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/validator/-/validator-13.15.15.tgz}
    name: validator
    version: 13.15.15
    engines: {node: '>= 0.10'}

  registry.npmmirror.com/vary@1.1.2:
    resolution: {integrity: sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/vary/-/vary-1.1.2.tgz}
    name: vary
    version: 1.1.2
    engines: {node: '>= 0.8'}

  registry.npmmirror.com/walker@1.0.8:
    resolution: {integrity: sha512-ts/8E8l5b7kY0vlWLewOkDXMmPdLcVV4GmOQLyxuSswIJsweeFZtAsMF7k1Nszz+TYBQrlYRmzOnr398y1JemQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/walker/-/walker-1.0.8.tgz}
    name: walker
    version: 1.0.8
    dependencies:
      makeerror: registry.npmmirror.com/makeerror@1.0.12
    dev: true

  registry.npmmirror.com/watchpack@2.4.4:
    resolution: {integrity: sha512-c5EGNOiyxxV5qmTtAB7rbiXxi1ooX1pQKMLX/MIabJjRA0SJBQOjKF+KSVfHkr9U1cADPon0mRiVe/riyaiDUA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/watchpack/-/watchpack-2.4.4.tgz}
    name: watchpack
    version: 2.4.4
    engines: {node: '>=10.13.0'}
    dependencies:
      glob-to-regexp: registry.npmmirror.com/glob-to-regexp@0.4.1
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
    dev: true

  registry.npmmirror.com/wcwidth@1.0.1:
    resolution: {integrity: sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/wcwidth/-/wcwidth-1.0.1.tgz}
    name: wcwidth
    version: 1.0.1
    dependencies:
      defaults: registry.npmmirror.com/defaults@1.0.4
    dev: true

  registry.npmmirror.com/webpack-node-externals@3.0.0:
    resolution: {integrity: sha512-LnL6Z3GGDPht/AigwRh2dvL9PQPFQ8skEpVrWZXLWBYmqcaojHNN0onvHzie6rq7EWKrrBfPYqNEzTJgiwEQDQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/webpack-node-externals/-/webpack-node-externals-3.0.0.tgz}
    name: webpack-node-externals
    version: 3.0.0
    engines: {node: '>=6'}
    dev: true

  registry.npmmirror.com/webpack-sources@3.3.3:
    resolution: {integrity: sha512-yd1RBzSGanHkitROoPFd6qsrxt+oFhg/129YzheDGqeustzX0vTZJZsSsQjVQC4yzBQ56K55XU8gaNCtIzOnTg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/webpack-sources/-/webpack-sources-3.3.3.tgz}
    name: webpack-sources
    version: 3.3.3
    engines: {node: '>=10.13.0'}
    dev: true

  registry.npmmirror.com/webpack@5.97.1(@swc/core@1.10.7):
    resolution: {integrity: sha512-EksG6gFY3L1eFMROS/7Wzgrii5mBAFe4rIr3r2BTfo7bcc+DWwFZ4OJ/miOuHJO/A85HwyI4eQ0F6IKXesO7Fg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/webpack/-/webpack-5.97.1.tgz}
    id: registry.npmmirror.com/webpack/5.97.1
    name: webpack
    version: 5.97.1
    engines: {node: '>=10.13.0'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true
    dependencies:
      '@types/eslint-scope': registry.npmmirror.com/@types/eslint-scope@3.7.7
      '@types/estree': registry.npmmirror.com/@types/estree@1.0.8
      '@webassemblyjs/ast': registry.npmmirror.com/@webassemblyjs/ast@1.14.1
      '@webassemblyjs/wasm-edit': registry.npmmirror.com/@webassemblyjs/wasm-edit@1.14.1
      '@webassemblyjs/wasm-parser': registry.npmmirror.com/@webassemblyjs/wasm-parser@1.14.1
      acorn: registry.npmmirror.com/acorn@8.15.0
      browserslist: registry.npmmirror.com/browserslist@4.25.1
      chrome-trace-event: registry.npmmirror.com/chrome-trace-event@1.0.4
      enhanced-resolve: registry.npmmirror.com/enhanced-resolve@5.18.2
      es-module-lexer: registry.npmmirror.com/es-module-lexer@1.7.0
      eslint-scope: registry.npmmirror.com/eslint-scope@5.1.1
      events: registry.npmmirror.com/events@3.3.0
      glob-to-regexp: registry.npmmirror.com/glob-to-regexp@0.4.1
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
      json-parse-even-better-errors: registry.npmmirror.com/json-parse-even-better-errors@2.3.1
      loader-runner: registry.npmmirror.com/loader-runner@4.3.0
      mime-types: registry.npmmirror.com/mime-types@2.1.35
      neo-async: registry.npmmirror.com/neo-async@2.6.2
      schema-utils: registry.npmmirror.com/schema-utils@3.3.0
      tapable: registry.npmmirror.com/tapable@2.2.2
      terser-webpack-plugin: registry.npmmirror.com/terser-webpack-plugin@5.3.14(@swc/core@1.10.7)(webpack@5.97.1)
      watchpack: registry.npmmirror.com/watchpack@2.4.4
      webpack-sources: registry.npmmirror.com/webpack-sources@3.3.3
    transitivePeerDependencies:
      - '@swc/core'
      - esbuild
      - uglify-js
    dev: true

  registry.npmmirror.com/webpack@5.99.9(@swc/core@1.10.7):
    resolution: {integrity: sha512-brOPwM3JnmOa+7kd3NsmOUOwbDAj8FT9xDsG3IW0MgbN9yZV7Oi/s/+MNQ/EcSMqw7qfoRyXPoeEWT8zLVdVGg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/webpack/-/webpack-5.99.9.tgz}
    id: registry.npmmirror.com/webpack/5.99.9
    name: webpack
    version: 5.99.9
    engines: {node: '>=10.13.0'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true
    dependencies:
      '@types/eslint-scope': registry.npmmirror.com/@types/eslint-scope@3.7.7
      '@types/estree': registry.npmmirror.com/@types/estree@1.0.8
      '@types/json-schema': registry.npmmirror.com/@types/json-schema@7.0.15
      '@webassemblyjs/ast': registry.npmmirror.com/@webassemblyjs/ast@1.14.1
      '@webassemblyjs/wasm-edit': registry.npmmirror.com/@webassemblyjs/wasm-edit@1.14.1
      '@webassemblyjs/wasm-parser': registry.npmmirror.com/@webassemblyjs/wasm-parser@1.14.1
      acorn: registry.npmmirror.com/acorn@8.15.0
      browserslist: registry.npmmirror.com/browserslist@4.25.1
      chrome-trace-event: registry.npmmirror.com/chrome-trace-event@1.0.4
      enhanced-resolve: registry.npmmirror.com/enhanced-resolve@5.18.2
      es-module-lexer: registry.npmmirror.com/es-module-lexer@1.7.0
      eslint-scope: registry.npmmirror.com/eslint-scope@5.1.1
      events: registry.npmmirror.com/events@3.3.0
      glob-to-regexp: registry.npmmirror.com/glob-to-regexp@0.4.1
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
      json-parse-even-better-errors: registry.npmmirror.com/json-parse-even-better-errors@2.3.1
      loader-runner: registry.npmmirror.com/loader-runner@4.3.0
      mime-types: registry.npmmirror.com/mime-types@2.1.35
      neo-async: registry.npmmirror.com/neo-async@2.6.2
      schema-utils: registry.npmmirror.com/schema-utils@4.3.2
      tapable: registry.npmmirror.com/tapable@2.2.2
      terser-webpack-plugin: registry.npmmirror.com/terser-webpack-plugin@5.3.14(@swc/core@1.10.7)(webpack@5.99.9)
      watchpack: registry.npmmirror.com/watchpack@2.4.4
      webpack-sources: registry.npmmirror.com/webpack-sources@3.3.3
    transitivePeerDependencies:
      - '@swc/core'
      - esbuild
      - uglify-js
    dev: true

  registry.npmmirror.com/which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/which/-/which-2.0.2.tgz}
    name: which
    version: 2.0.2
    engines: {node: '>= 8'}
    hasBin: true
    dependencies:
      isexe: registry.npmmirror.com/isexe@2.0.0

  registry.npmmirror.com/word-wrap@1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/word-wrap/-/word-wrap-1.2.5.tgz}
    name: word-wrap
    version: 1.2.5
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/wrap-ansi@6.2.0:
    resolution: {integrity: sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-6.2.0.tgz}
    name: wrap-ansi
    version: 6.2.0
    engines: {node: '>=8'}
    dependencies:
      ansi-styles: registry.npmmirror.com/ansi-styles@4.3.0
      string-width: registry.npmmirror.com/string-width@4.2.3
      strip-ansi: registry.npmmirror.com/strip-ansi@6.0.1
    dev: true

  registry.npmmirror.com/wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz}
    name: wrap-ansi
    version: 7.0.0
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: registry.npmmirror.com/ansi-styles@4.3.0
      string-width: registry.npmmirror.com/string-width@4.2.3
      strip-ansi: registry.npmmirror.com/strip-ansi@6.0.1

  registry.npmmirror.com/wrap-ansi@8.1.0:
    resolution: {integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-8.1.0.tgz}
    name: wrap-ansi
    version: 8.1.0
    engines: {node: '>=12'}
    dependencies:
      ansi-styles: registry.npmmirror.com/ansi-styles@6.2.1
      string-width: registry.npmmirror.com/string-width@5.1.2
      strip-ansi: registry.npmmirror.com/strip-ansi@7.1.0

  registry.npmmirror.com/wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/wrappy/-/wrappy-1.0.2.tgz}
    name: wrappy
    version: 1.0.2

  registry.npmmirror.com/write-file-atomic@4.0.2:
    resolution: {integrity: sha512-7KxauUdBmSdWnmpaGFg+ppNjKF8uNLry8LyzjauQDOVONfFLNKrKvQOxZ/VuTIcS/gge/YNahf5RIIQWTSarlg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-4.0.2.tgz}
    name: write-file-atomic
    version: 4.0.2
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}
    dependencies:
      imurmurhash: registry.npmmirror.com/imurmurhash@0.1.4
      signal-exit: registry.npmmirror.com/signal-exit@3.0.7
    dev: true

  registry.npmmirror.com/xtend@4.0.2:
    resolution: {integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/xtend/-/xtend-4.0.2.tgz}
    name: xtend
    version: 4.0.2
    engines: {node: '>=0.4'}

  registry.npmmirror.com/y18n@5.0.8:
    resolution: {integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/y18n/-/y18n-5.0.8.tgz}
    name: y18n
    version: 5.0.8
    engines: {node: '>=10'}

  registry.npmmirror.com/yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/yallist/-/yallist-3.1.1.tgz}
    name: yallist
    version: 3.1.1
    dev: true

  registry.npmmirror.com/yargs-parser@21.1.1:
    resolution: {integrity: sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/yargs-parser/-/yargs-parser-21.1.1.tgz}
    name: yargs-parser
    version: 21.1.1
    engines: {node: '>=12'}

  registry.npmmirror.com/yargs@17.7.2:
    resolution: {integrity: sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/yargs/-/yargs-17.7.2.tgz}
    name: yargs
    version: 17.7.2
    engines: {node: '>=12'}
    dependencies:
      cliui: registry.npmmirror.com/cliui@8.0.1
      escalade: registry.npmmirror.com/escalade@3.2.0
      get-caller-file: registry.npmmirror.com/get-caller-file@2.0.5
      require-directory: registry.npmmirror.com/require-directory@2.1.1
      string-width: registry.npmmirror.com/string-width@4.2.3
      y18n: registry.npmmirror.com/y18n@5.0.8
      yargs-parser: registry.npmmirror.com/yargs-parser@21.1.1

  registry.npmmirror.com/yauzl@3.2.0:
    resolution: {integrity: sha512-Ow9nuGZE+qp1u4JIPvg+uCiUr7xGQWdff7JQSk5VGYTAZMDe2q8lxJ10ygv10qmSj031Ty/6FNJpLO4o1Sgc+w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/yauzl/-/yauzl-3.2.0.tgz}
    name: yauzl
    version: 3.2.0
    engines: {node: '>=12'}
    dependencies:
      buffer-crc32: registry.npmmirror.com/buffer-crc32@0.2.13
      pend: registry.npmmirror.com/pend@1.2.0
    dev: true

  registry.npmmirror.com/ylru@2.0.0:
    resolution: {integrity: sha512-T6hTrKcr9lKeUG0MQ/tO72D3UGptWVohgzpHG8ljU1jeBt2RCjcWxvsTPD8ZzUq1t1FvwROAw1kxg2euvg/THg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ylru/-/ylru-2.0.0.tgz}
    name: ylru
    version: 2.0.0
    engines: {node: '>= 18.19.0'}
    dev: false

  registry.npmmirror.com/yn@3.1.1:
    resolution: {integrity: sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/yn/-/yn-3.1.1.tgz}
    name: yn
    version: 3.1.1
    engines: {node: '>=6'}

  registry.npmmirror.com/yocto-queue@0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/yocto-queue/-/yocto-queue-0.1.0.tgz}
    name: yocto-queue
    version: 0.1.0
    engines: {node: '>=10'}
    dev: true

  registry.npmmirror.com/yoctocolors-cjs@2.1.2:
    resolution: {integrity: sha512-cYVsTjKl8b+FrnidjibDWskAv7UKOfcwaVZdp/it9n1s9fU3IkgDbhdIRKCW4JDsAlECJY0ytoVPT3sK6kideA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/yoctocolors-cjs/-/yoctocolors-cjs-2.1.2.tgz}
    name: yoctocolors-cjs
    version: 2.1.2
    engines: {node: '>=18'}
    dev: true
