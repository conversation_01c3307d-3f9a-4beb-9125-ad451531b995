"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const typeorm_1 = require("typeorm");
const payment_config_entity_1 = require("../entities/payment-config.entity");
async function initPaymentConfig() {
    const dataSource = new typeorm_1.DataSource({
        type: 'mysql',
        host: '*************',
        port: 3306,
        username: 'df_yuezuhui_cn',
        password: 'Ej8Ej8Ej8',
        database: 'df_yuezuhui_cn',
        entities: [payment_config_entity_1.PaymentConfig],
        synchronize: false,
    });
    await dataSource.initialize();
    const paymentConfigRepository = dataSource.getRepository(payment_config_entity_1.PaymentConfig);
    const existingConfig = await paymentConfigRepository.findOne({
        where: { provider: 'alipay', isActive: true }
    });
    if (existingConfig) {
        console.log('Alipay配置已存在，跳过初始化');
        await dataSource.destroy();
        return;
    }
    const alipayConfig = new payment_config_entity_1.PaymentConfig();
    alipayConfig.provider = 'alipay';
    alipayConfig.appId = '2021000122671080';
    alipayConfig.privateKey = `-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB
xQOndFompydXhCVhMpM+XdD8zG7z4ckVDXI1Iy4Hr7k/9CTwhGKv4C/OBw==
-----END PRIVATE KEY-----`;
    alipayConfig.publicKey = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuVSU1LfVLPHCgcUDp3Ra
Jqcnl4QlYTKTPl3Q/Mxu8+HJFQlyNSMuB6+5P/Qk8IRir+AvzgcP
-----END PUBLIC KEY-----`;
    alipayConfig.gateway = 'https://openapi.alipaydev.com/gateway.do';
    alipayConfig.isSandbox = true;
    alipayConfig.defaultFeeRate = 0.006;
    alipayConfig.isActive = true;
    alipayConfig.status = payment_config_entity_1.PaymentConfigStatus.ACTIVE;
    await paymentConfigRepository.save(alipayConfig);
    console.log('Alipay沙箱配置初始化完成');
    console.log('配置ID:', alipayConfig.id);
    console.log('应用ID:', alipayConfig.appId);
    console.log('网关地址:', alipayConfig.gateway);
    await dataSource.destroy();
}
initPaymentConfig().catch(console.error);
//# sourceMappingURL=init-payment-config.js.map