"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const typeorm_1 = require("typeorm");
const bcrypt = require("bcryptjs");
const user_entity_1 = require("../entities/user.entity");
const merchant_entity_1 = require("../entities/merchant.entity");
async function createDefaultUsers() {
    const dataSource = new typeorm_1.DataSource({
        type: 'mysql',
        host: '*************',
        port: 3306,
        username: 'df_yuezuhui_cn',
        password: '8JN8Dnfi6s85cdzM',
        database: 'df_yuezuhui_cn',
        entities: [user_entity_1.User, merchant_entity_1.Merchant],
        synchronize: false,
    });
    await dataSource.initialize();
    const userRepository = dataSource.getRepository(user_entity_1.User);
    const merchantRepository = dataSource.getRepository(merchant_entity_1.Merchant);
    try {
        const existingAdmin = await userRepository.findOne({
            where: { username: 'admin' }
        });
        if (!existingAdmin) {
            const adminPassword = await bcrypt.hash('admin123', 10);
            const admin = userRepository.create({
                username: 'admin',
                password: adminPassword,
                email: '<EMAIL>',
                role: user_entity_1.UserRole.PLATFORM_ADMIN,
                status: user_entity_1.UserStatus.ACTIVE
            });
            await userRepository.save(admin);
            console.log('✅ 平台管理员账户创建成功');
            console.log('   用户名: admin');
            console.log('   密码: admin123');
        }
        else {
            console.log('ℹ️  平台管理员账户已存在');
        }
        let merchant1 = await merchantRepository.findOne({ where: { name: '测试商户1' } });
        if (!merchant1) {
            merchant1 = merchantRepository.create({
                name: '测试商户1',
                contactName: '张三',
                contactPhone: '13800138001',
                contactEmail: '<EMAIL>',
                balance: 1000.00,
                apiEnabled: true,
                status: merchant_entity_1.MerchantStatus.ACTIVE
            });
            await merchantRepository.save(merchant1);
            console.log('✅ 测试商户1创建成功');
        }
        let merchant2 = await merchantRepository.findOne({ where: { name: '测试商户2' } });
        if (!merchant2) {
            merchant2 = merchantRepository.create({
                name: '测试商户2',
                contactName: '李四',
                contactPhone: '13800138002',
                contactEmail: '<EMAIL>',
                balance: 500.00,
                apiEnabled: false,
                status: merchant_entity_1.MerchantStatus.ACTIVE
            });
            await merchantRepository.save(merchant2);
            console.log('✅ 测试商户2创建成功');
        }
        const existingMerchant1Admin = await userRepository.findOne({
            where: { username: 'merchant1' }
        });
        if (!existingMerchant1Admin) {
            const merchant1Password = await bcrypt.hash('merchant123', 10);
            const merchant1Admin = userRepository.create({
                username: 'merchant1',
                password: merchant1Password,
                email: '<EMAIL>',
                role: user_entity_1.UserRole.MERCHANT_ADMIN,
                merchantId: merchant1.id,
                status: user_entity_1.UserStatus.ACTIVE
            });
            await userRepository.save(merchant1Admin);
            console.log('✅ 商户1管理员账户创建成功');
            console.log('   用户名: merchant1');
            console.log('   密码: merchant123');
        }
        else {
            console.log('ℹ️  商户1管理员账户已存在');
        }
        const existingMerchant2Admin = await userRepository.findOne({
            where: { username: 'merchant2' }
        });
        if (!existingMerchant2Admin) {
            const merchant2Password = await bcrypt.hash('merchant123', 10);
            const merchant2Admin = userRepository.create({
                username: 'merchant2',
                password: merchant2Password,
                email: '<EMAIL>',
                role: user_entity_1.UserRole.MERCHANT_ADMIN,
                merchantId: merchant2.id,
                status: user_entity_1.UserStatus.ACTIVE
            });
            await userRepository.save(merchant2Admin);
            console.log('✅ 商户2管理员账户创建成功');
            console.log('   用户名: merchant2');
            console.log('   密码: merchant123');
        }
        else {
            console.log('ℹ️  商户2管理员账户已存在');
        }
        console.log('\n🎉 默认用户创建完成！');
        console.log('\n📋 登录信息汇总:');
        console.log('┌─────────────────────────────────────────┐');
        console.log('│ 平台管理员                              │');
        console.log('│ 用户名: admin                           │');
        console.log('│ 密码: admin123                         │');
        console.log('├─────────────────────────────────────────┤');
        console.log('│ 商户1管理员                             │');
        console.log('│ 用户名: merchant1                       │');
        console.log('│ 密码: merchant123                      │');
        console.log('├─────────────────────────────────────────┤');
        console.log('│ 商户2管理员                             │');
        console.log('│ 用户名: merchant2                       │');
        console.log('│ 密码: merchant123                      │');
        console.log('└─────────────────────────────────────────┘');
    }
    catch (error) {
        console.error('❌ 创建默认用户失败:', error);
    }
    finally {
        await dataSource.destroy();
    }
}
createDefaultUsers().catch(console.error);
//# sourceMappingURL=create-default-users.js.map