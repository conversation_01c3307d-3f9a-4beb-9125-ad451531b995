"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePayoutDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreatePayoutDto {
    amount;
    merchantId;
    recipientAccount;
    recipientName;
    accountType;
    description;
}
exports.CreatePayoutDto = CreatePayoutDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '代付金额', example: 50.00 }),
    (0, class_validator_1.IsNumber)({ maxDecimalPlaces: 2 }),
    (0, class_validator_1.Min)(0.01),
    __metadata("design:type", Number)
], CreatePayoutDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商户ID（平台管理员专用）', example: 1, required: false }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreatePayoutDto.prototype, "merchantId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '收款人账户', example: '<EMAIL>' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePayoutDto.prototype, "recipientAccount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '收款人姓名', example: '张三' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePayoutDto.prototype, "recipientName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '账户类型',
        example: 'ALIPAY_LOGON_ID',
        enum: ['ALIPAY_LOGON_ID', 'ALIPAY_USER_ID'],
        required: false
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['ALIPAY_LOGON_ID', 'ALIPAY_USER_ID']),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreatePayoutDto.prototype, "accountType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '代付描述', example: '提现转账', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreatePayoutDto.prototype, "description", void 0);
//# sourceMappingURL=create-payout.dto.js.map