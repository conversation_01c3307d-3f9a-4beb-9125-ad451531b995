"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateRechargeDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateRechargeDto {
    amount;
    merchantId;
    description;
    notifyUrl;
    returnUrl;
}
exports.CreateRechargeDto = CreateRechargeDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '充值金额', example: 100.00 }),
    (0, class_validator_1.IsNumber)({ maxDecimalPlaces: 2 }),
    (0, class_validator_1.Min)(0.01),
    __metadata("design:type", Number)
], CreateRechargeDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商户ID（平台管理员专用）', example: 1, required: false }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateRechargeDto.prototype, "merchantId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单描述', example: '账户充值', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateRechargeDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '异步通知URL', example: 'https://example.com/notify', required: false }),
    (0, class_validator_1.IsUrl)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateRechargeDto.prototype, "notifyUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '同步返回URL', example: 'https://example.com/return', required: false }),
    (0, class_validator_1.IsUrl)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateRechargeDto.prototype, "returnUrl", void 0);
//# sourceMappingURL=create-recharge.dto.js.map