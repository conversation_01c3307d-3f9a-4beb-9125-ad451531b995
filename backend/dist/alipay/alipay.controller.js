"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlipayController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const alipay_service_1 = require("./alipay.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
const user_entity_1 = require("../entities/user.entity");
const create_recharge_dto_1 = require("./dto/create-recharge.dto");
const create_payout_dto_1 = require("./dto/create-payout.dto");
let AlipayController = class AlipayController {
    alipayService;
    constructor(alipayService) {
        this.alipayService = alipayService;
    }
    async createRecharge(req, createRechargeDto) {
        const merchantId = req.user.role === user_entity_1.UserRole.PLATFORM_ADMIN
            ? createRechargeDto.merchantId
            : req.user.merchantId;
        if (!merchantId) {
            throw new Error('无法确定商户ID');
        }
        return this.alipayService.createRecharge(merchantId, createRechargeDto);
    }
    async createPayout(req, createPayoutDto) {
        const merchantId = req.user.role === user_entity_1.UserRole.PLATFORM_ADMIN
            ? createPayoutDto.merchantId
            : req.user.merchantId;
        if (!merchantId) {
            throw new Error('无法确定商户ID');
        }
        return this.alipayService.createPayout(merchantId, createPayoutDto);
    }
    async queryTransaction(outTradeNo) {
        return this.alipayService.queryTransaction(outTradeNo);
    }
    async handleNotify(notifyData) {
        const success = await this.alipayService.handleNotify(notifyData);
        return success ? 'success' : 'fail';
    }
};
exports.AlipayController = AlipayController;
__decorate([
    (0, common_1.Post)('recharge'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.PLATFORM_ADMIN, user_entity_1.UserRole.MERCHANT_ADMIN),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '创建充值订单' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '充值订单创建成功' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_recharge_dto_1.CreateRechargeDto]),
    __metadata("design:returntype", Promise)
], AlipayController.prototype, "createRecharge", null);
__decorate([
    (0, common_1.Post)('payout'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.PLATFORM_ADMIN, user_entity_1.UserRole.MERCHANT_ADMIN),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '创建代付订单' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '代付订单创建成功' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_payout_dto_1.CreatePayoutDto]),
    __metadata("design:returntype", Promise)
], AlipayController.prototype, "createPayout", null);
__decorate([
    (0, common_1.Get)('query/:outTradeNo'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '查询交易状态' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Param)('outTradeNo')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AlipayController.prototype, "queryTransaction", null);
__decorate([
    (0, common_1.Post)('notify'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '支付宝异步通知回调' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '处理成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AlipayController.prototype, "handleNotify", null);
exports.AlipayController = AlipayController = __decorate([
    (0, swagger_1.ApiTags)('支付宝支付'),
    (0, common_1.Controller)('alipay'),
    __metadata("design:paramtypes", [alipay_service_1.AlipayService])
], AlipayController);
//# sourceMappingURL=alipay.controller.js.map