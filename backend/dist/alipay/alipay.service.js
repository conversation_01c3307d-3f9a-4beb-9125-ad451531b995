"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AlipayService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlipayService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const config_1 = require("@nestjs/config");
const AlipaySdk = require('alipay-sdk');
const transaction_entity_1 = require("../entities/transaction.entity");
const merchant_entity_1 = require("../entities/merchant.entity");
const payment_config_entity_1 = require("../entities/payment-config.entity");
let AlipayService = AlipayService_1 = class AlipayService {
    transactionRepository;
    merchantRepository;
    paymentConfigRepository;
    configService;
    logger = new common_1.Logger(AlipayService_1.name);
    alipaySdk;
    constructor(transactionRepository, merchantRepository, paymentConfigRepository, configService) {
        this.transactionRepository = transactionRepository;
        this.merchantRepository = merchantRepository;
        this.paymentConfigRepository = paymentConfigRepository;
        this.configService = configService;
    }
    async initializeAlipaySDK() {
        const config = await this.getAlipayConfig();
        this.alipaySdk = new AlipaySdk({
            appId: config.appId,
            privateKey: config.privateKey,
            alipayPublicKey: config.alipayPublicKey,
            gateway: config.gateway || 'https://openapi.alipay.com/gateway.do',
            timeout: 5000,
            camelCase: true,
        });
    }
    async getAlipayConfig() {
        const config = await this.paymentConfigRepository.findOne({
            where: { provider: 'alipay', isActive: true },
        });
        if (!config) {
            return {
                appId: this.configService.get('ALIPAY_APP_ID'),
                privateKey: this.configService.get('ALIPAY_PRIVATE_KEY'),
                alipayPublicKey: this.configService.get('ALIPAY_PUBLIC_KEY'),
                gateway: this.configService.get('ALIPAY_GATEWAY'),
            };
        }
        return {
            appId: config.appId,
            privateKey: config.privateKey,
            alipayPublicKey: config.publicKey,
            gateway: config.gateway,
        };
    }
    async createRecharge(merchantId, createRechargeDto) {
        const merchant = await this.merchantRepository.findOne({
            where: { id: merchantId },
        });
        if (!merchant) {
            throw new common_1.NotFoundException('商户不存在');
        }
        if (!merchant.alipayAuthorized) {
            throw new common_1.BadRequestException('商户未授权支付宝支付');
        }
        const outTradeNo = this.generateOrderNo('R');
        const transaction = this.transactionRepository.create({
            merchantId,
            outTradeNo,
            amount: createRechargeDto.amount,
            type: transaction_entity_1.TransactionType.RECHARGE,
            status: transaction_entity_1.TransactionStatus.PENDING,
            description: createRechargeDto.description || '账户充值',
            notifyUrl: createRechargeDto.notifyUrl,
            returnUrl: createRechargeDto.returnUrl,
        });
        await this.transactionRepository.save(transaction);
        try {
            const result = await this.alipaySdk.exec('alipay.trade.precreate', {
                bizContent: {
                    outTradeNo,
                    totalAmount: createRechargeDto.amount,
                    subject: transaction.description,
                    notifyUrl: createRechargeDto.notifyUrl,
                    timeoutExpress: '30m',
                },
            });
            if (result.code === '10000') {
                transaction.thirdPartyTradeNo = result.outTradeNo;
                transaction.qrCode = result.qrCode;
                await this.transactionRepository.save(transaction);
                return {
                    transactionId: transaction.id,
                    outTradeNo,
                    qrCode: result.qrCode,
                    amount: createRechargeDto.amount,
                    expireTime: new Date(Date.now() + 30 * 60 * 1000),
                };
            }
            else {
                throw new common_1.BadRequestException(`支付宝API调用失败: ${result.msg}`);
            }
        }
        catch (error) {
            this.logger.error('创建充值订单失败', error);
            transaction.status = transaction_entity_1.TransactionStatus.FAILED;
            transaction.failureReason = error.message;
            await this.transactionRepository.save(transaction);
            throw new common_1.BadRequestException('创建充值订单失败');
        }
    }
    async createPayout(merchantId, createPayoutDto) {
        const merchant = await this.merchantRepository.findOne({
            where: { id: merchantId },
        });
        if (!merchant) {
            throw new common_1.NotFoundException('商户不存在');
        }
        if (!merchant.alipayAuthorized) {
            throw new common_1.BadRequestException('商户未授权支付宝支付');
        }
        if (parseFloat(merchant.balance.toString()) < createPayoutDto.amount) {
            throw new common_1.BadRequestException('商户余额不足');
        }
        const outTradeNo = this.generateOrderNo('P');
        const transaction = this.transactionRepository.create({
            merchantId,
            outTradeNo,
            amount: createPayoutDto.amount,
            type: transaction_entity_1.TransactionType.PAYOUT,
            status: transaction_entity_1.TransactionStatus.PENDING,
            description: createPayoutDto.description || '代付转账',
            recipientAccount: createPayoutDto.recipientAccount,
            recipientName: createPayoutDto.recipientName,
        });
        await this.transactionRepository.save(transaction);
        try {
            const result = await this.alipaySdk.exec('alipay.fund.trans.uni.transfer', {
                bizContent: {
                    outBizNo: outTradeNo,
                    transAmount: createPayoutDto.amount,
                    productCode: 'TRANS_ACCOUNT_NO_PWD',
                    bizScene: 'DIRECT_TRANSFER',
                    orderTitle: transaction.description,
                    payeeInfo: {
                        identity: createPayoutDto.recipientAccount,
                        identityType: createPayoutDto.accountType || 'ALIPAY_LOGON_ID',
                        name: createPayoutDto.recipientName,
                    },
                    remark: createPayoutDto.description,
                },
            });
            if (result.code === '10000') {
                transaction.thirdPartyTradeNo = result.orderId;
                transaction.status = transaction_entity_1.TransactionStatus.SUCCESS;
                await this.transactionRepository.save(transaction);
                merchant.balance = parseFloat((parseFloat(merchant.balance.toString()) - createPayoutDto.amount).toFixed(2));
                await this.merchantRepository.save(merchant);
                return {
                    transactionId: transaction.id,
                    outTradeNo,
                    thirdPartyTradeNo: result.orderId,
                    amount: createPayoutDto.amount,
                    status: 'SUCCESS',
                };
            }
            else {
                throw new common_1.BadRequestException(`支付宝代付失败: ${result.msg}`);
            }
        }
        catch (error) {
            this.logger.error('创建代付订单失败', error);
            transaction.status = transaction_entity_1.TransactionStatus.FAILED;
            transaction.failureReason = error.message;
            await this.transactionRepository.save(transaction);
            throw new common_1.BadRequestException('创建代付订单失败');
        }
    }
    async queryTransaction(outTradeNo) {
        const transaction = await this.transactionRepository.findOne({
            where: { outTradeNo },
            relations: ['merchant'],
        });
        if (!transaction) {
            throw new common_1.NotFoundException('交易不存在');
        }
        try {
            let result;
            if (transaction.type === transaction_entity_1.TransactionType.RECHARGE) {
                result = await this.alipaySdk.exec('alipay.trade.query', {
                    bizContent: {
                        outTradeNo,
                    },
                });
            }
            else {
                result = await this.alipaySdk.exec('alipay.fund.trans.common.query', {
                    bizContent: {
                        outBizNo: outTradeNo,
                    },
                });
            }
            if (result.code === '10000') {
                await this.updateTransactionStatus(transaction, result);
                return {
                    transactionId: transaction.id,
                    outTradeNo,
                    status: transaction.status,
                    amount: transaction.amount,
                    thirdPartyTradeNo: transaction.thirdPartyTradeNo,
                };
            }
            else {
                throw new common_1.BadRequestException(`查询交易状态失败: ${result.msg}`);
            }
        }
        catch (error) {
            this.logger.error('查询交易状态失败', error);
            throw new common_1.BadRequestException('查询交易状态失败');
        }
    }
    async handleNotify(notifyData) {
        try {
            const isValid = this.alipaySdk.checkNotifySign(notifyData);
            if (!isValid) {
                this.logger.warn('支付宝回调签名验证失败');
                return false;
            }
            const outTradeNo = notifyData.out_trade_no;
            const transaction = await this.transactionRepository.findOne({
                where: { outTradeNo },
                relations: ['merchant'],
            });
            if (!transaction) {
                this.logger.warn(`回调通知中的订单不存在: ${outTradeNo}`);
                return false;
            }
            await this.updateTransactionStatus(transaction, notifyData);
            return true;
        }
        catch (error) {
            this.logger.error('处理支付宝回调失败', error);
            return false;
        }
    }
    async updateTransactionStatus(transaction, alipayResult) {
        const oldStatus = transaction.status;
        if (transaction.type === transaction_entity_1.TransactionType.RECHARGE) {
            switch (alipayResult.trade_status || alipayResult.tradeStatus) {
                case 'TRADE_SUCCESS':
                case 'TRADE_FINISHED':
                    transaction.status = transaction_entity_1.TransactionStatus.SUCCESS;
                    transaction.thirdPartyTradeNo = alipayResult.trade_no || alipayResult.tradeNo;
                    if (oldStatus === transaction_entity_1.TransactionStatus.PENDING) {
                        const merchant = transaction.merchant;
                        merchant.balance = parseFloat((parseFloat(merchant.balance.toString()) + transaction.amount).toFixed(2));
                        await this.merchantRepository.save(merchant);
                    }
                    break;
                case 'TRADE_CLOSED':
                    transaction.status = transaction_entity_1.TransactionStatus.FAILED;
                    transaction.failureReason = '交易关闭';
                    break;
            }
        }
        else {
            switch (alipayResult.status) {
                case 'SUCCESS':
                    transaction.status = transaction_entity_1.TransactionStatus.SUCCESS;
                    break;
                case 'FAIL':
                    transaction.status = transaction_entity_1.TransactionStatus.FAILED;
                    transaction.failureReason = alipayResult.failReason || '代付失败';
                    break;
            }
        }
        await this.transactionRepository.save(transaction);
    }
    generateOrderNo(prefix) {
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        return `${prefix}${timestamp}${random}`;
    }
};
exports.AlipayService = AlipayService;
exports.AlipayService = AlipayService = AlipayService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(transaction_entity_1.Transaction)),
    __param(1, (0, typeorm_1.InjectRepository)(merchant_entity_1.Merchant)),
    __param(2, (0, typeorm_1.InjectRepository)(payment_config_entity_1.PaymentConfig)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        config_1.ConfigService])
], AlipayService);
//# sourceMappingURL=alipay.service.js.map