import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { Transaction, TransactionStatus } from '../entities/transaction.entity';
import { Merchant } from '../entities/merchant.entity';
import { PaymentConfig } from '../entities/payment-config.entity';
import { CreateRechargeDto } from './dto/create-recharge.dto';
import { CreatePayoutDto } from './dto/create-payout.dto';
export declare class AlipayService {
    private transactionRepository;
    private merchantRepository;
    private paymentConfigRepository;
    private configService;
    private readonly logger;
    private alipaySdk;
    constructor(transactionRepository: Repository<Transaction>, merchantRepository: Repository<Merchant>, paymentConfigRepository: Repository<PaymentConfig>, configService: ConfigService);
    private initializeAlipaySDK;
    private getAlipayConfig;
    createRecharge(merchantId: number, createRechargeDto: CreateRechargeDto): Promise<{
        transactionId: number;
        outTradeNo: string;
        qrCode: any;
        amount: number;
        expireTime: Date;
    }>;
    createPayout(merchantId: number, createPayoutDto: CreatePayoutDto): Promise<{
        transactionId: number;
        outTradeNo: string;
        thirdPartyTradeNo: any;
        amount: number;
        status: string;
    }>;
    queryTransaction(outTradeNo: string): Promise<{
        transactionId: number;
        outTradeNo: string;
        status: TransactionStatus;
        amount: number;
        thirdPartyTradeNo: string | undefined;
    }>;
    handleNotify(notifyData: any): Promise<boolean>;
    private updateTransactionStatus;
    private generateOrderNo;
}
