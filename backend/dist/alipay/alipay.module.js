"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlipayModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const alipay_service_1 = require("./alipay.service");
const alipay_controller_1 = require("./alipay.controller");
const transaction_entity_1 = require("../entities/transaction.entity");
const merchant_entity_1 = require("../entities/merchant.entity");
const payment_config_entity_1 = require("../entities/payment-config.entity");
let AlipayModule = class AlipayModule {
};
exports.AlipayModule = AlipayModule;
exports.AlipayModule = AlipayModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            typeorm_1.TypeOrmModule.forFeature([transaction_entity_1.Transaction, merchant_entity_1.Merchant, payment_config_entity_1.PaymentConfig]),
        ],
        controllers: [alipay_controller_1.AlipayController],
        providers: [alipay_service_1.AlipayService],
        exports: [alipay_service_1.AlipayService],
    })
], AlipayModule);
//# sourceMappingURL=alipay.module.js.map