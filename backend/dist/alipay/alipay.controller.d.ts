import { AlipayService } from './alipay.service';
import { CreateRechargeDto } from './dto/create-recharge.dto';
import { CreatePayoutDto } from './dto/create-payout.dto';
export declare class AlipayController {
    private readonly alipayService;
    constructor(alipayService: AlipayService);
    createRecharge(req: any, createRechargeDto: CreateRechargeDto): Promise<{
        transactionId: number;
        outTradeNo: string;
        qrCode: any;
        amount: number;
        expireTime: Date;
    }>;
    createPayout(req: any, createPayoutDto: CreatePayoutDto): Promise<{
        transactionId: number;
        outTradeNo: string;
        thirdPartyTradeNo: any;
        amount: number;
        status: string;
    }>;
    queryTransaction(outTradeNo: string): Promise<{
        transactionId: number;
        outTradeNo: string;
        status: import("../entities").TransactionStatus;
        amount: number;
        thirdPartyTradeNo: string | undefined;
    }>;
    handleNotify(notifyData: any): Promise<"success" | "fail">;
}
