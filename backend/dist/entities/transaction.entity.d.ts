import { Merchant } from './merchant.entity';
export declare enum TransactionType {
    RECHARGE = "recharge",
    PAYOUT = "payout"
}
export declare enum TransactionStatus {
    PENDING = "pending",
    PROCESSING = "processing",
    SUCCESS = "success",
    FAILED = "failed",
    EXPIRED = "expired"
}
export declare class Transaction {
    id: number;
    merchantId: number;
    type: TransactionType;
    amount: number;
    fee: number;
    status: TransactionStatus;
    alipayTradeNo?: string;
    thirdPartyTradeNo?: string;
    outTradeNo: string;
    targetAccount?: string;
    targetName?: string;
    recipientAccount?: string;
    recipientName?: string;
    qrCodeUrl?: string;
    qrCode?: string;
    description?: string;
    notifyUrl?: string;
    returnUrl?: string;
    failureReason?: string;
    expireTime: Date;
    remark?: string;
    createdAt: Date;
    updatedAt: Date;
    merchant: Merchant;
}
