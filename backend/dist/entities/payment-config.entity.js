"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentConfig = exports.PaymentConfigStatus = void 0;
const typeorm_1 = require("typeorm");
var PaymentConfigStatus;
(function (PaymentConfigStatus) {
    PaymentConfigStatus["ACTIVE"] = "active";
    PaymentConfigStatus["INACTIVE"] = "inactive";
})(PaymentConfigStatus || (exports.PaymentConfigStatus = PaymentConfigStatus = {}));
let PaymentConfig = class PaymentConfig {
    id;
    provider;
    appId;
    privateKey;
    publicKey;
    gateway;
    isSandbox;
    defaultFeeRate;
    isActive;
    status;
    createdAt;
    updatedAt;
};
exports.PaymentConfig = PaymentConfig;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], PaymentConfig.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, comment: '支付提供商' }),
    __metadata("design:type", String)
], PaymentConfig.prototype, "provider", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'app_id', length: 100, comment: '支付宝AppID' }),
    __metadata("design:type", String)
], PaymentConfig.prototype, "appId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'private_key', type: 'text', comment: '应用私钥' }),
    __metadata("design:type", String)
], PaymentConfig.prototype, "privateKey", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'public_key', type: 'text', comment: '支付宝公钥' }),
    __metadata("design:type", String)
], PaymentConfig.prototype, "publicKey", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 200, nullable: true, comment: '支付网关地址' }),
    __metadata("design:type", String)
], PaymentConfig.prototype, "gateway", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_sandbox', default: true, comment: '是否沙箱环境' }),
    __metadata("design:type", Boolean)
], PaymentConfig.prototype, "isSandbox", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'default_fee_rate',
        type: 'decimal',
        precision: 5,
        scale: 4,
        default: 0.0050,
        comment: '默认手续费比例（0.5%）',
    }),
    __metadata("design:type", Number)
], PaymentConfig.prototype, "defaultFeeRate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_active', default: true, comment: '是否激活' }),
    __metadata("design:type", Boolean)
], PaymentConfig.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: PaymentConfigStatus,
        default: PaymentConfigStatus.ACTIVE,
        comment: '配置状态',
    }),
    __metadata("design:type", String)
], PaymentConfig.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], PaymentConfig.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], PaymentConfig.prototype, "updatedAt", void 0);
exports.PaymentConfig = PaymentConfig = __decorate([
    (0, typeorm_1.Entity)('payment_configs')
], PaymentConfig);
//# sourceMappingURL=payment-config.entity.js.map