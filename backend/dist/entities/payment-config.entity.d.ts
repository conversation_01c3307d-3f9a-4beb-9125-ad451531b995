export declare enum PaymentConfigStatus {
    ACTIVE = "active",
    INACTIVE = "inactive"
}
export declare class PaymentConfig {
    id: number;
    provider: string;
    appId: string;
    privateKey: string;
    publicKey: string;
    gateway?: string;
    isSandbox: boolean;
    defaultFeeRate: number;
    isActive: boolean;
    status: PaymentConfigStatus;
    createdAt: Date;
    updatedAt: Date;
}
