"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Transaction = exports.TransactionStatus = exports.TransactionType = void 0;
const typeorm_1 = require("typeorm");
const merchant_entity_1 = require("./merchant.entity");
var TransactionType;
(function (TransactionType) {
    TransactionType["RECHARGE"] = "recharge";
    TransactionType["PAYOUT"] = "payout";
})(TransactionType || (exports.TransactionType = TransactionType = {}));
var TransactionStatus;
(function (TransactionStatus) {
    TransactionStatus["PENDING"] = "pending";
    TransactionStatus["PROCESSING"] = "processing";
    TransactionStatus["SUCCESS"] = "success";
    TransactionStatus["FAILED"] = "failed";
    TransactionStatus["EXPIRED"] = "expired";
})(TransactionStatus || (exports.TransactionStatus = TransactionStatus = {}));
let Transaction = class Transaction {
    id;
    merchantId;
    type;
    amount;
    fee;
    status;
    alipayTradeNo;
    thirdPartyTradeNo;
    outTradeNo;
    targetAccount;
    targetName;
    recipientAccount;
    recipientName;
    qrCodeUrl;
    qrCode;
    description;
    notifyUrl;
    returnUrl;
    failureReason;
    expireTime;
    remark;
    createdAt;
    updatedAt;
    merchant;
};
exports.Transaction = Transaction;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], Transaction.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'merchant_id', comment: '商户ID' }),
    __metadata("design:type", Number)
], Transaction.prototype, "merchantId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: TransactionType,
        comment: '交易类型：充值/代付',
    }),
    __metadata("design:type", String)
], Transaction.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'decimal',
        precision: 15,
        scale: 2,
        comment: '交易金额',
    }),
    __metadata("design:type", Number)
], Transaction.prototype, "amount", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'decimal',
        precision: 15,
        scale: 2,
        default: 0.00,
        comment: '手续费',
    }),
    __metadata("design:type", Number)
], Transaction.prototype, "fee", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: TransactionStatus,
        default: TransactionStatus.PENDING,
        comment: '状态',
    }),
    __metadata("design:type", String)
], Transaction.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'alipay_trade_no', length: 100, nullable: true, comment: '支付宝交易号' }),
    __metadata("design:type", String)
], Transaction.prototype, "alipayTradeNo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'third_party_trade_no', length: 100, nullable: true, comment: '第三方交易号' }),
    __metadata("design:type", String)
], Transaction.prototype, "thirdPartyTradeNo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'out_trade_no', length: 100, unique: true, comment: '商户订单号' }),
    __metadata("design:type", String)
], Transaction.prototype, "outTradeNo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'target_account', length: 100, nullable: true, comment: '目标账户（代付专用）' }),
    __metadata("design:type", String)
], Transaction.prototype, "targetAccount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'target_name', length: 50, nullable: true, comment: '目标账户姓名（代付专用）' }),
    __metadata("design:type", String)
], Transaction.prototype, "targetName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'recipient_account', length: 100, nullable: true, comment: '收款人账户（代付专用）' }),
    __metadata("design:type", String)
], Transaction.prototype, "recipientAccount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'recipient_name', length: 50, nullable: true, comment: '收款人姓名（代付专用）' }),
    __metadata("design:type", String)
], Transaction.prototype, "recipientName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'qr_code_url', length: 500, nullable: true, comment: '二维码URL（充值专用）' }),
    __metadata("design:type", String)
], Transaction.prototype, "qrCodeUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'qr_code', length: 500, nullable: true, comment: '二维码内容（充值专用）' }),
    __metadata("design:type", String)
], Transaction.prototype, "qrCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true, comment: '订单描述' }),
    __metadata("design:type", String)
], Transaction.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'notify_url', length: 500, nullable: true, comment: '异步通知URL' }),
    __metadata("design:type", String)
], Transaction.prototype, "notifyUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'return_url', length: 500, nullable: true, comment: '同步返回URL' }),
    __metadata("design:type", String)
], Transaction.prototype, "returnUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'failure_reason', type: 'text', nullable: true, comment: '失败原因' }),
    __metadata("design:type", String)
], Transaction.prototype, "failureReason", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'expire_time', nullable: true, comment: '过期时间' }),
    __metadata("design:type", Date)
], Transaction.prototype, "expireTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true, comment: '备注' }),
    __metadata("design:type", String)
], Transaction.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], Transaction.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], Transaction.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => merchant_entity_1.Merchant, (merchant) => merchant.transactions),
    (0, typeorm_1.JoinColumn)({ name: 'merchant_id' }),
    __metadata("design:type", merchant_entity_1.Merchant)
], Transaction.prototype, "merchant", void 0);
exports.Transaction = Transaction = __decorate([
    (0, typeorm_1.Entity)('transactions'),
    (0, typeorm_1.Index)(['merchantId']),
    (0, typeorm_1.Index)(['type']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['alipayTradeNo']),
    (0, typeorm_1.Index)(['createdAt'])
], Transaction);
//# sourceMappingURL=transaction.entity.js.map