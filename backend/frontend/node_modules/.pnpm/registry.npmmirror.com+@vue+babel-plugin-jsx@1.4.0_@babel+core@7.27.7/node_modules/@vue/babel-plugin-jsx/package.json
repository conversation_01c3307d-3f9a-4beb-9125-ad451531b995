{"name": "@vue/babel-plugin-jsx", "version": "1.4.0", "description": "Babel plugin for Vue 3 JSX", "author": "Amour1688 <<EMAIL>>", "homepage": "https://github.com/vuejs/babel-plugin-jsx/tree/dev/packages/babel-plugin-jsx#readme", "license": "MIT", "type": "commonjs", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./*": "./*"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/babel-plugin-jsx.git"}, "bugs": {"url": "https://github.com/vuejs/babel-plugin-jsx/issues"}, "files": ["dist"], "dependencies": {"@babel/helper-module-imports": "^7.25.9", "@babel/helper-plugin-utils": "^7.26.5", "@babel/plugin-syntax-jsx": "^7.25.9", "@babel/template": "^7.26.9", "@babel/traverse": "^7.26.9", "@babel/types": "^7.26.9", "@vue/shared": "^3.5.13", "@vue/babel-helper-vue-transform-on": "1.4.0", "@vue/babel-plugin-resolve-type": "1.4.0"}, "devDependencies": {"@babel/core": "^7.26.9", "@babel/preset-env": "^7.26.9", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "@vue/test-utils": "^2.4.6", "regenerator-runtime": "^0.14.1", "vue": "^3.5.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "peerDependenciesMeta": {"@babel/core": {"optional": true}}, "scripts": {"build": "tsup", "watch": "tsup --watch"}}