{"name": "utility", "version": "2.5.0", "description": "A collection of useful utilities.", "scripts": {"lint": "eslint src test --ext ts", "pretest": "npm run lint -- --fix && npm run prepublishOnly", "test": "egg-bin test", "test-local": "egg-bin test", "preci": "npm run lint && npm run prepublishOnly && attw --pack", "ci": "egg-bin cov", "prepublishOnly": "tshy && tshy-after"}, "dependencies": {"escape-html": "^1.0.3", "unescape": "^1.0.1", "ylru": "^2.0.0"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@eggjs/tsconfig": "^1.3.3", "@types/escape-html": "^1.0.4", "@types/mocha": "^10.0.6", "@types/node": "22", "beautify-benchmark": "^0.2.4", "benchmark": "^2.1.4", "egg-bin": "^6.5.2", "eslint": "^8.54.0", "eslint-config-egg": "^13.0.0", "moment": "^2.22.2", "object-assign": "^4.1.1", "optimized": "^1.2.0", "time-require": "^0.1.2", "tsd": "^0.28.1", "tshy": "^3.0.2", "tshy-after": "^1.0.0", "typescript": "^5.2.2"}, "homepage": "https://github.com/node-modules/utility", "repository": {"type": "git", "url": "git://github.com/node-modules/utility.git"}, "keywords": ["utility", "util", "utils", "sha256", "sha1", "hash", "hex"], "engines": {"node": ">= 16.0.0"}, "author": "fengmk2 <<EMAIL>> (https://github.com/fengmk2)", "license": "MIT", "type": "module", "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "files": ["dist", "src"], "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js"}