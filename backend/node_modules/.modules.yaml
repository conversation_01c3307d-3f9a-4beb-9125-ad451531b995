hoistPattern:
  - '*'
hoistedDependencies:
  registry.npmmirror.com/@ampproject/remapping/2.3.0:
    '@ampproject/remapping': private
  registry.npmmirror.com/@angular-devkit/core/19.1.2(chokidar@4.0.3):
    '@angular-devkit/core': private
  registry.npmmirror.com/@angular-devkit/schematics-cli/19.1.2(@types/node@22.10.7)(chokidar@4.0.3):
    '@angular-devkit/schematics-cli': private
  registry.npmmirror.com/@angular-devkit/schematics/19.1.2(chokidar@4.0.3):
    '@angular-devkit/schematics': private
  registry.npmmirror.com/@babel/code-frame/7.27.1:
    '@babel/code-frame': private
  registry.npmmirror.com/@babel/compat-data/7.27.7:
    '@babel/compat-data': private
  registry.npmmirror.com/@babel/core/7.27.7:
    '@babel/core': private
  registry.npmmirror.com/@babel/generator/7.27.5:
    '@babel/generator': private
  registry.npmmirror.com/@babel/helper-compilation-targets/7.27.2:
    '@babel/helper-compilation-targets': private
  registry.npmmirror.com/@babel/helper-module-imports/7.27.1:
    '@babel/helper-module-imports': private
  registry.npmmirror.com/@babel/helper-module-transforms/7.27.3(@babel/core@7.27.7):
    '@babel/helper-module-transforms': private
  registry.npmmirror.com/@babel/helper-plugin-utils/7.27.1:
    '@babel/helper-plugin-utils': private
  registry.npmmirror.com/@babel/helper-string-parser/7.27.1:
    '@babel/helper-string-parser': private
  registry.npmmirror.com/@babel/helper-validator-identifier/7.27.1:
    '@babel/helper-validator-identifier': private
  registry.npmmirror.com/@babel/helper-validator-option/7.27.1:
    '@babel/helper-validator-option': private
  registry.npmmirror.com/@babel/helpers/7.27.6:
    '@babel/helpers': private
  registry.npmmirror.com/@babel/parser/7.27.7:
    '@babel/parser': private
  registry.npmmirror.com/@babel/plugin-syntax-async-generators/7.8.4(@babel/core@7.27.7):
    '@babel/plugin-syntax-async-generators': private
  registry.npmmirror.com/@babel/plugin-syntax-bigint/7.8.3(@babel/core@7.27.7):
    '@babel/plugin-syntax-bigint': private
  registry.npmmirror.com/@babel/plugin-syntax-class-properties/7.12.13(@babel/core@7.27.7):
    '@babel/plugin-syntax-class-properties': private
  registry.npmmirror.com/@babel/plugin-syntax-class-static-block/7.14.5(@babel/core@7.27.7):
    '@babel/plugin-syntax-class-static-block': private
  registry.npmmirror.com/@babel/plugin-syntax-import-attributes/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-syntax-import-attributes': private
  registry.npmmirror.com/@babel/plugin-syntax-import-meta/7.10.4(@babel/core@7.27.7):
    '@babel/plugin-syntax-import-meta': private
  registry.npmmirror.com/@babel/plugin-syntax-json-strings/7.8.3(@babel/core@7.27.7):
    '@babel/plugin-syntax-json-strings': private
  registry.npmmirror.com/@babel/plugin-syntax-jsx/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-syntax-jsx': private
  registry.npmmirror.com/@babel/plugin-syntax-logical-assignment-operators/7.10.4(@babel/core@7.27.7):
    '@babel/plugin-syntax-logical-assignment-operators': private
  registry.npmmirror.com/@babel/plugin-syntax-nullish-coalescing-operator/7.8.3(@babel/core@7.27.7):
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  registry.npmmirror.com/@babel/plugin-syntax-numeric-separator/7.10.4(@babel/core@7.27.7):
    '@babel/plugin-syntax-numeric-separator': private
  registry.npmmirror.com/@babel/plugin-syntax-object-rest-spread/7.8.3(@babel/core@7.27.7):
    '@babel/plugin-syntax-object-rest-spread': private
  registry.npmmirror.com/@babel/plugin-syntax-optional-catch-binding/7.8.3(@babel/core@7.27.7):
    '@babel/plugin-syntax-optional-catch-binding': private
  registry.npmmirror.com/@babel/plugin-syntax-optional-chaining/7.8.3(@babel/core@7.27.7):
    '@babel/plugin-syntax-optional-chaining': private
  registry.npmmirror.com/@babel/plugin-syntax-private-property-in-object/7.14.5(@babel/core@7.27.7):
    '@babel/plugin-syntax-private-property-in-object': private
  registry.npmmirror.com/@babel/plugin-syntax-top-level-await/7.14.5(@babel/core@7.27.7):
    '@babel/plugin-syntax-top-level-await': private
  registry.npmmirror.com/@babel/plugin-syntax-typescript/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-syntax-typescript': private
  registry.npmmirror.com/@babel/template/7.27.2:
    '@babel/template': private
  registry.npmmirror.com/@babel/traverse/7.27.7:
    '@babel/traverse': private
  registry.npmmirror.com/@babel/types/7.27.7:
    '@babel/types': private
  registry.npmmirror.com/@bcoe/v8-coverage/0.2.3:
    '@bcoe/v8-coverage': private
  registry.npmmirror.com/@colors/colors/1.5.0:
    '@colors/colors': private
  registry.npmmirror.com/@cspotcode/source-map-support/0.8.1:
    '@cspotcode/source-map-support': private
  registry.npmmirror.com/@eslint-community/eslint-utils/4.7.0(eslint@9.18.0):
    '@eslint-community/eslint-utils': public
  registry.npmmirror.com/@eslint-community/regexpp/4.12.1:
    '@eslint-community/regexpp': public
  registry.npmmirror.com/@eslint/config-array/0.19.2:
    '@eslint/config-array': public
  registry.npmmirror.com/@eslint/core/0.10.0:
    '@eslint/core': public
  registry.npmmirror.com/@eslint/object-schema/2.1.6:
    '@eslint/object-schema': public
  registry.npmmirror.com/@eslint/plugin-kit/0.2.8:
    '@eslint/plugin-kit': public
  registry.npmmirror.com/@fidm/asn1/1.0.4:
    '@fidm/asn1': private
  registry.npmmirror.com/@fidm/x509/1.2.1:
    '@fidm/x509': private
  registry.npmmirror.com/@humanfs/core/0.19.1:
    '@humanfs/core': private
  registry.npmmirror.com/@humanfs/node/0.16.6:
    '@humanfs/node': private
  registry.npmmirror.com/@humanwhocodes/module-importer/1.0.1:
    '@humanwhocodes/module-importer': private
  registry.npmmirror.com/@humanwhocodes/retry/0.4.3:
    '@humanwhocodes/retry': private
  registry.npmmirror.com/@inquirer/checkbox/4.1.8(@types/node@22.10.7):
    '@inquirer/checkbox': private
  registry.npmmirror.com/@inquirer/confirm/5.1.12(@types/node@22.10.7):
    '@inquirer/confirm': private
  registry.npmmirror.com/@inquirer/core/10.1.13(@types/node@22.10.7):
    '@inquirer/core': private
  registry.npmmirror.com/@inquirer/editor/4.2.13(@types/node@22.10.7):
    '@inquirer/editor': private
  registry.npmmirror.com/@inquirer/expand/4.0.15(@types/node@22.10.7):
    '@inquirer/expand': private
  registry.npmmirror.com/@inquirer/figures/1.0.12:
    '@inquirer/figures': private
  registry.npmmirror.com/@inquirer/input/4.1.12(@types/node@22.10.7):
    '@inquirer/input': private
  registry.npmmirror.com/@inquirer/number/3.0.15(@types/node@22.10.7):
    '@inquirer/number': private
  registry.npmmirror.com/@inquirer/password/4.0.15(@types/node@22.10.7):
    '@inquirer/password': private
  registry.npmmirror.com/@inquirer/prompts/7.2.3(@types/node@22.10.7):
    '@inquirer/prompts': private
  registry.npmmirror.com/@inquirer/rawlist/4.1.3(@types/node@22.10.7):
    '@inquirer/rawlist': private
  registry.npmmirror.com/@inquirer/search/3.0.15(@types/node@22.10.7):
    '@inquirer/search': private
  registry.npmmirror.com/@inquirer/select/4.2.3(@types/node@22.10.7):
    '@inquirer/select': private
  registry.npmmirror.com/@inquirer/type/3.0.7(@types/node@22.10.7):
    '@inquirer/type': private
  registry.npmmirror.com/@isaacs/balanced-match/4.0.1:
    '@isaacs/balanced-match': private
  registry.npmmirror.com/@isaacs/brace-expansion/5.0.0:
    '@isaacs/brace-expansion': private
  registry.npmmirror.com/@isaacs/cliui/8.0.2:
    '@isaacs/cliui': private
  registry.npmmirror.com/@istanbuljs/load-nyc-config/1.1.0:
    '@istanbuljs/load-nyc-config': private
  registry.npmmirror.com/@istanbuljs/schema/0.1.3:
    '@istanbuljs/schema': private
  registry.npmmirror.com/@jest/console/29.7.0:
    '@jest/console': private
  registry.npmmirror.com/@jest/core/29.7.0(ts-node@10.9.2):
    '@jest/core': private
  registry.npmmirror.com/@jest/environment/29.7.0:
    '@jest/environment': private
  registry.npmmirror.com/@jest/expect-utils/29.7.0:
    '@jest/expect-utils': private
  registry.npmmirror.com/@jest/expect/29.7.0:
    '@jest/expect': private
  registry.npmmirror.com/@jest/fake-timers/29.7.0:
    '@jest/fake-timers': private
  registry.npmmirror.com/@jest/globals/29.7.0:
    '@jest/globals': private
  registry.npmmirror.com/@jest/reporters/29.7.0:
    '@jest/reporters': private
  registry.npmmirror.com/@jest/schemas/29.6.3:
    '@jest/schemas': private
  registry.npmmirror.com/@jest/source-map/29.6.3:
    '@jest/source-map': private
  registry.npmmirror.com/@jest/test-result/29.7.0:
    '@jest/test-result': private
  registry.npmmirror.com/@jest/test-sequencer/29.7.0:
    '@jest/test-sequencer': private
  registry.npmmirror.com/@jest/transform/29.7.0:
    '@jest/transform': private
  registry.npmmirror.com/@jest/types/29.6.3:
    '@jest/types': private
  registry.npmmirror.com/@jridgewell/gen-mapping/0.3.8:
    '@jridgewell/gen-mapping': private
  registry.npmmirror.com/@jridgewell/resolve-uri/3.1.2:
    '@jridgewell/resolve-uri': private
  registry.npmmirror.com/@jridgewell/set-array/1.2.1:
    '@jridgewell/set-array': private
  registry.npmmirror.com/@jridgewell/source-map/0.3.6:
    '@jridgewell/source-map': private
  registry.npmmirror.com/@jridgewell/sourcemap-codec/1.5.0:
    '@jridgewell/sourcemap-codec': private
  registry.npmmirror.com/@jridgewell/trace-mapping/0.3.9:
    '@jridgewell/trace-mapping': private
  registry.npmmirror.com/@lukeed/csprng/1.1.0:
    '@lukeed/csprng': private
  registry.npmmirror.com/@microsoft/tsdoc/0.15.1:
    '@microsoft/tsdoc': private
  registry.npmmirror.com/@napi-rs/nice-android-arm-eabi/1.0.1:
    '@napi-rs/nice-android-arm-eabi': private
  registry.npmmirror.com/@napi-rs/nice-android-arm64/1.0.1:
    '@napi-rs/nice-android-arm64': private
  registry.npmmirror.com/@napi-rs/nice-darwin-arm64/1.0.1:
    '@napi-rs/nice-darwin-arm64': private
  registry.npmmirror.com/@napi-rs/nice-darwin-x64/1.0.1:
    '@napi-rs/nice-darwin-x64': private
  registry.npmmirror.com/@napi-rs/nice-freebsd-x64/1.0.1:
    '@napi-rs/nice-freebsd-x64': private
  registry.npmmirror.com/@napi-rs/nice-linux-arm-gnueabihf/1.0.1:
    '@napi-rs/nice-linux-arm-gnueabihf': private
  registry.npmmirror.com/@napi-rs/nice-linux-arm64-gnu/1.0.1:
    '@napi-rs/nice-linux-arm64-gnu': private
  registry.npmmirror.com/@napi-rs/nice-linux-arm64-musl/1.0.1:
    '@napi-rs/nice-linux-arm64-musl': private
  registry.npmmirror.com/@napi-rs/nice-linux-ppc64-gnu/1.0.1:
    '@napi-rs/nice-linux-ppc64-gnu': private
  registry.npmmirror.com/@napi-rs/nice-linux-riscv64-gnu/1.0.1:
    '@napi-rs/nice-linux-riscv64-gnu': private
  registry.npmmirror.com/@napi-rs/nice-linux-s390x-gnu/1.0.1:
    '@napi-rs/nice-linux-s390x-gnu': private
  registry.npmmirror.com/@napi-rs/nice-linux-x64-gnu/1.0.1:
    '@napi-rs/nice-linux-x64-gnu': private
  registry.npmmirror.com/@napi-rs/nice-linux-x64-musl/1.0.1:
    '@napi-rs/nice-linux-x64-musl': private
  registry.npmmirror.com/@napi-rs/nice-win32-arm64-msvc/1.0.1:
    '@napi-rs/nice-win32-arm64-msvc': private
  registry.npmmirror.com/@napi-rs/nice-win32-ia32-msvc/1.0.1:
    '@napi-rs/nice-win32-ia32-msvc': private
  registry.npmmirror.com/@napi-rs/nice-win32-x64-msvc/1.0.1:
    '@napi-rs/nice-win32-x64-msvc': private
  registry.npmmirror.com/@napi-rs/nice/1.0.1:
    '@napi-rs/nice': private
  registry.npmmirror.com/@nestjs/mapped-types/2.1.0(@nestjs/common@11.0.1)(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2):
    '@nestjs/mapped-types': private
  registry.npmmirror.com/@noble/hashes/1.8.0:
    '@noble/hashes': private
  registry.npmmirror.com/@nodelib/fs.scandir/2.1.5:
    '@nodelib/fs.scandir': private
  registry.npmmirror.com/@nodelib/fs.stat/2.0.5:
    '@nodelib/fs.stat': private
  registry.npmmirror.com/@nodelib/fs.walk/1.2.8:
    '@nodelib/fs.walk': private
  registry.npmmirror.com/@nuxt/opencollective/0.4.1:
    '@nuxt/opencollective': private
  registry.npmmirror.com/@paralleldrive/cuid2/2.2.2:
    '@paralleldrive/cuid2': private
  registry.npmmirror.com/@pkgjs/parseargs/0.11.0:
    '@pkgjs/parseargs': private
  registry.npmmirror.com/@pkgr/core/0.1.2:
    '@pkgr/core': private
  registry.npmmirror.com/@scarf/scarf/1.4.0:
    '@scarf/scarf': private
  registry.npmmirror.com/@sec-ant/readable-stream/0.4.1:
    '@sec-ant/readable-stream': private
  registry.npmmirror.com/@sinclair/typebox/0.27.8:
    '@sinclair/typebox': private
  registry.npmmirror.com/@sindresorhus/is/5.6.0:
    '@sindresorhus/is': private
  registry.npmmirror.com/@sinonjs/commons/3.0.1:
    '@sinonjs/commons': private
  registry.npmmirror.com/@sinonjs/fake-timers/10.3.0:
    '@sinonjs/fake-timers': private
  registry.npmmirror.com/@sqltools/formatter/1.2.5:
    '@sqltools/formatter': private
  registry.npmmirror.com/@swc/core-darwin-arm64/1.10.7:
    '@swc/core-darwin-arm64': private
  registry.npmmirror.com/@swc/core-darwin-x64/1.10.7:
    '@swc/core-darwin-x64': private
  registry.npmmirror.com/@swc/core-linux-arm-gnueabihf/1.10.7:
    '@swc/core-linux-arm-gnueabihf': private
  registry.npmmirror.com/@swc/core-linux-arm64-gnu/1.10.7:
    '@swc/core-linux-arm64-gnu': private
  registry.npmmirror.com/@swc/core-linux-arm64-musl/1.10.7:
    '@swc/core-linux-arm64-musl': private
  registry.npmmirror.com/@swc/core-linux-x64-gnu/1.10.7:
    '@swc/core-linux-x64-gnu': private
  registry.npmmirror.com/@swc/core-linux-x64-musl/1.10.7:
    '@swc/core-linux-x64-musl': private
  registry.npmmirror.com/@swc/core-win32-arm64-msvc/1.10.7:
    '@swc/core-win32-arm64-msvc': private
  registry.npmmirror.com/@swc/core-win32-ia32-msvc/1.10.7:
    '@swc/core-win32-ia32-msvc': private
  registry.npmmirror.com/@swc/core-win32-x64-msvc/1.10.7:
    '@swc/core-win32-x64-msvc': private
  registry.npmmirror.com/@swc/counter/0.1.3:
    '@swc/counter': private
  registry.npmmirror.com/@swc/types/0.1.23:
    '@swc/types': private
  registry.npmmirror.com/@szmarczak/http-timer/5.0.1:
    '@szmarczak/http-timer': private
  registry.npmmirror.com/@tokenizer/token/0.3.0:
    '@tokenizer/token': private
  registry.npmmirror.com/@tsconfig/node10/1.0.11:
    '@tsconfig/node10': private
  registry.npmmirror.com/@tsconfig/node12/1.0.11:
    '@tsconfig/node12': private
  registry.npmmirror.com/@tsconfig/node14/1.0.3:
    '@tsconfig/node14': private
  registry.npmmirror.com/@tsconfig/node16/1.0.4:
    '@tsconfig/node16': private
  registry.npmmirror.com/@types/babel__core/7.20.5:
    '@types/babel__core': private
  registry.npmmirror.com/@types/babel__generator/7.27.0:
    '@types/babel__generator': private
  registry.npmmirror.com/@types/babel__template/7.4.4:
    '@types/babel__template': private
  registry.npmmirror.com/@types/babel__traverse/7.20.7:
    '@types/babel__traverse': private
  registry.npmmirror.com/@types/body-parser/1.19.6:
    '@types/body-parser': private
  registry.npmmirror.com/@types/connect/3.4.38:
    '@types/connect': private
  registry.npmmirror.com/@types/cookiejar/2.1.5:
    '@types/cookiejar': private
  registry.npmmirror.com/@types/eslint-scope/3.7.7:
    '@types/eslint-scope': public
  registry.npmmirror.com/@types/eslint/9.6.1:
    '@types/eslint': public
  registry.npmmirror.com/@types/estree/1.0.8:
    '@types/estree': private
  registry.npmmirror.com/@types/express-serve-static-core/5.0.6:
    '@types/express-serve-static-core': private
  registry.npmmirror.com/@types/graceful-fs/4.1.9:
    '@types/graceful-fs': private
  registry.npmmirror.com/@types/http-cache-semantics/4.0.4:
    '@types/http-cache-semantics': private
  registry.npmmirror.com/@types/http-errors/2.0.5:
    '@types/http-errors': private
  registry.npmmirror.com/@types/istanbul-lib-coverage/2.0.6:
    '@types/istanbul-lib-coverage': private
  registry.npmmirror.com/@types/istanbul-lib-report/3.0.3:
    '@types/istanbul-lib-report': private
  registry.npmmirror.com/@types/istanbul-reports/3.0.4:
    '@types/istanbul-reports': private
  registry.npmmirror.com/@types/json-schema/7.0.15:
    '@types/json-schema': private
  registry.npmmirror.com/@types/jsonwebtoken/9.0.7:
    '@types/jsonwebtoken': private
  registry.npmmirror.com/@types/methods/1.1.4:
    '@types/methods': private
  registry.npmmirror.com/@types/mime/1.3.5:
    '@types/mime': private
  registry.npmmirror.com/@types/passport-strategy/0.2.38:
    '@types/passport-strategy': private
  registry.npmmirror.com/@types/passport/1.0.17:
    '@types/passport': private
  registry.npmmirror.com/@types/qs/6.14.0:
    '@types/qs': private
  registry.npmmirror.com/@types/range-parser/1.2.7:
    '@types/range-parser': private
  registry.npmmirror.com/@types/send/0.17.5:
    '@types/send': private
  registry.npmmirror.com/@types/serve-static/1.15.8:
    '@types/serve-static': private
  registry.npmmirror.com/@types/stack-utils/2.0.3:
    '@types/stack-utils': private
  registry.npmmirror.com/@types/superagent/8.1.9:
    '@types/superagent': private
  registry.npmmirror.com/@types/validator/13.15.2:
    '@types/validator': private
  registry.npmmirror.com/@types/yargs-parser/21.0.3:
    '@types/yargs-parser': private
  registry.npmmirror.com/@types/yargs/17.0.33:
    '@types/yargs': private
  registry.npmmirror.com/@typescript-eslint/eslint-plugin/8.20.0(@typescript-eslint/parser@8.20.0)(eslint@9.18.0)(typescript@5.7.3):
    '@typescript-eslint/eslint-plugin': public
  registry.npmmirror.com/@typescript-eslint/parser/8.20.0(eslint@9.18.0)(typescript@5.7.3):
    '@typescript-eslint/parser': public
  registry.npmmirror.com/@typescript-eslint/scope-manager/8.20.0:
    '@typescript-eslint/scope-manager': public
  registry.npmmirror.com/@typescript-eslint/type-utils/8.20.0(eslint@9.18.0)(typescript@5.7.3):
    '@typescript-eslint/type-utils': public
  registry.npmmirror.com/@typescript-eslint/types/8.20.0:
    '@typescript-eslint/types': public
  registry.npmmirror.com/@typescript-eslint/typescript-estree/8.20.0(typescript@5.7.3):
    '@typescript-eslint/typescript-estree': public
  registry.npmmirror.com/@typescript-eslint/utils/8.20.0(eslint@9.18.0)(typescript@5.7.3):
    '@typescript-eslint/utils': public
  registry.npmmirror.com/@typescript-eslint/visitor-keys/8.20.0:
    '@typescript-eslint/visitor-keys': public
  registry.npmmirror.com/@webassemblyjs/ast/1.14.1:
    '@webassemblyjs/ast': private
  registry.npmmirror.com/@webassemblyjs/floating-point-hex-parser/1.13.2:
    '@webassemblyjs/floating-point-hex-parser': private
  registry.npmmirror.com/@webassemblyjs/helper-api-error/1.13.2:
    '@webassemblyjs/helper-api-error': private
  registry.npmmirror.com/@webassemblyjs/helper-buffer/1.14.1:
    '@webassemblyjs/helper-buffer': private
  registry.npmmirror.com/@webassemblyjs/helper-numbers/1.13.2:
    '@webassemblyjs/helper-numbers': private
  registry.npmmirror.com/@webassemblyjs/helper-wasm-bytecode/1.13.2:
    '@webassemblyjs/helper-wasm-bytecode': private
  registry.npmmirror.com/@webassemblyjs/helper-wasm-section/1.14.1:
    '@webassemblyjs/helper-wasm-section': private
  registry.npmmirror.com/@webassemblyjs/ieee754/1.13.2:
    '@webassemblyjs/ieee754': private
  registry.npmmirror.com/@webassemblyjs/leb128/1.13.2:
    '@webassemblyjs/leb128': private
  registry.npmmirror.com/@webassemblyjs/utf8/1.13.2:
    '@webassemblyjs/utf8': private
  registry.npmmirror.com/@webassemblyjs/wasm-edit/1.14.1:
    '@webassemblyjs/wasm-edit': private
  registry.npmmirror.com/@webassemblyjs/wasm-gen/1.14.1:
    '@webassemblyjs/wasm-gen': private
  registry.npmmirror.com/@webassemblyjs/wasm-opt/1.14.1:
    '@webassemblyjs/wasm-opt': private
  registry.npmmirror.com/@webassemblyjs/wasm-parser/1.14.1:
    '@webassemblyjs/wasm-parser': private
  registry.npmmirror.com/@webassemblyjs/wast-printer/1.14.1:
    '@webassemblyjs/wast-printer': private
  registry.npmmirror.com/@xhmikosr/archive-type/7.0.0:
    '@xhmikosr/archive-type': private
  registry.npmmirror.com/@xhmikosr/bin-check/7.0.3:
    '@xhmikosr/bin-check': private
  registry.npmmirror.com/@xhmikosr/bin-wrapper/13.0.5:
    '@xhmikosr/bin-wrapper': private
  registry.npmmirror.com/@xhmikosr/decompress-tar/8.0.1:
    '@xhmikosr/decompress-tar': private
  registry.npmmirror.com/@xhmikosr/decompress-tarbz2/8.0.2:
    '@xhmikosr/decompress-tarbz2': private
  registry.npmmirror.com/@xhmikosr/decompress-targz/8.0.1:
    '@xhmikosr/decompress-targz': private
  registry.npmmirror.com/@xhmikosr/decompress-unzip/7.0.0:
    '@xhmikosr/decompress-unzip': private
  registry.npmmirror.com/@xhmikosr/decompress/10.0.1:
    '@xhmikosr/decompress': private
  registry.npmmirror.com/@xhmikosr/downloader/15.0.1:
    '@xhmikosr/downloader': private
  registry.npmmirror.com/@xhmikosr/os-filter-obj/3.0.0:
    '@xhmikosr/os-filter-obj': private
  registry.npmmirror.com/@xtuc/ieee754/1.2.0:
    '@xtuc/ieee754': private
  registry.npmmirror.com/@xtuc/long/4.2.2:
    '@xtuc/long': private
  registry.npmmirror.com/accepts/2.0.0:
    accepts: private
  registry.npmmirror.com/acorn-jsx/5.3.2(acorn@8.15.0):
    acorn-jsx: private
  registry.npmmirror.com/acorn-walk/8.3.4:
    acorn-walk: private
  registry.npmmirror.com/acorn/8.15.0:
    acorn: private
  registry.npmmirror.com/ajv-formats/3.0.1(ajv@8.17.1):
    ajv-formats: private
  registry.npmmirror.com/ajv-keywords/3.5.2(ajv@6.12.6):
    ajv-keywords: private
  registry.npmmirror.com/ajv/6.12.6:
    ajv: private
  registry.npmmirror.com/ansi-colors/4.1.3:
    ansi-colors: private
  registry.npmmirror.com/ansi-escapes/4.3.2:
    ansi-escapes: private
  registry.npmmirror.com/ansi-regex/5.0.1:
    ansi-regex: private
  registry.npmmirror.com/ansi-styles/4.3.0:
    ansi-styles: private
  registry.npmmirror.com/ansis/3.9.0:
    ansis: private
  registry.npmmirror.com/anymatch/3.1.3:
    anymatch: private
  registry.npmmirror.com/app-root-path/3.1.0:
    app-root-path: private
  registry.npmmirror.com/append-field/1.0.0:
    append-field: private
  registry.npmmirror.com/arch/3.0.0:
    arch: private
  registry.npmmirror.com/arg/4.1.3:
    arg: private
  registry.npmmirror.com/argparse/2.0.1:
    argparse: private
  registry.npmmirror.com/array-timsort/1.0.3:
    array-timsort: private
  registry.npmmirror.com/asap/2.0.6:
    asap: private
  registry.npmmirror.com/async/3.2.6:
    async: private
  registry.npmmirror.com/asynckit/0.4.0:
    asynckit: private
  registry.npmmirror.com/aws-ssl-profiles/1.1.2:
    aws-ssl-profiles: private
  registry.npmmirror.com/b4a/1.6.7:
    b4a: private
  registry.npmmirror.com/babel-jest/29.7.0(@babel/core@7.27.7):
    babel-jest: private
  registry.npmmirror.com/babel-plugin-istanbul/6.1.1:
    babel-plugin-istanbul: private
  registry.npmmirror.com/babel-plugin-jest-hoist/29.6.3:
    babel-plugin-jest-hoist: private
  registry.npmmirror.com/babel-preset-current-node-syntax/1.1.0(@babel/core@7.27.7):
    babel-preset-current-node-syntax: private
  registry.npmmirror.com/babel-preset-jest/29.6.3(@babel/core@7.27.7):
    babel-preset-jest: private
  registry.npmmirror.com/balanced-match/1.0.2:
    balanced-match: private
  registry.npmmirror.com/bare-events/2.5.4:
    bare-events: private
  registry.npmmirror.com/base64-js/1.5.1:
    base64-js: private
  registry.npmmirror.com/bignumber.js/9.3.0:
    bignumber.js: private
  registry.npmmirror.com/bin-version-check/5.1.0:
    bin-version-check: private
  registry.npmmirror.com/bin-version/6.0.0:
    bin-version: private
  registry.npmmirror.com/binary-extensions/2.3.0:
    binary-extensions: private
  registry.npmmirror.com/bl/4.1.0:
    bl: private
  registry.npmmirror.com/body-parser/1.20.3:
    body-parser: private
  registry.npmmirror.com/brace-expansion/1.1.12:
    brace-expansion: private
  registry.npmmirror.com/braces/3.0.3:
    braces: private
  registry.npmmirror.com/browserslist/4.25.1:
    browserslist: private
  registry.npmmirror.com/bs-logger/0.2.6:
    bs-logger: private
  registry.npmmirror.com/bser/2.1.1:
    bser: private
  registry.npmmirror.com/buffer-crc32/0.2.13:
    buffer-crc32: private
  registry.npmmirror.com/buffer-equal-constant-time/1.0.1:
    buffer-equal-constant-time: private
  registry.npmmirror.com/buffer-from/1.1.2:
    buffer-from: private
  registry.npmmirror.com/buffer/6.0.3:
    buffer: private
  registry.npmmirror.com/busboy/1.6.0:
    busboy: private
  registry.npmmirror.com/bytes/3.1.2:
    bytes: private
  registry.npmmirror.com/cacheable-lookup/7.0.0:
    cacheable-lookup: private
  registry.npmmirror.com/cacheable-request/10.2.14:
    cacheable-request: private
  registry.npmmirror.com/call-bind-apply-helpers/1.0.2:
    call-bind-apply-helpers: private
  registry.npmmirror.com/call-bound/1.0.4:
    call-bound: private
  registry.npmmirror.com/callsites/3.1.0:
    callsites: private
  registry.npmmirror.com/camelcase-keys/7.0.2:
    camelcase-keys: private
  registry.npmmirror.com/camelcase/6.3.0:
    camelcase: private
  registry.npmmirror.com/caniuse-lite/1.0.30001726:
    caniuse-lite: private
  registry.npmmirror.com/chalk/4.1.2:
    chalk: private
  registry.npmmirror.com/char-regex/1.0.2:
    char-regex: private
  registry.npmmirror.com/chardet/0.7.0:
    chardet: private
  registry.npmmirror.com/chokidar/4.0.3:
    chokidar: private
  registry.npmmirror.com/chrome-trace-event/1.0.4:
    chrome-trace-event: private
  registry.npmmirror.com/ci-info/3.9.0:
    ci-info: private
  registry.npmmirror.com/cjs-module-lexer/1.4.3:
    cjs-module-lexer: private
  registry.npmmirror.com/cli-cursor/3.1.0:
    cli-cursor: private
  registry.npmmirror.com/cli-spinners/2.9.2:
    cli-spinners: private
  registry.npmmirror.com/cli-table3/0.6.5:
    cli-table3: private
  registry.npmmirror.com/cli-width/4.1.0:
    cli-width: private
  registry.npmmirror.com/cliui/8.0.1:
    cliui: private
  registry.npmmirror.com/clone/1.0.4:
    clone: private
  registry.npmmirror.com/co/4.6.0:
    co: private
  registry.npmmirror.com/collect-v8-coverage/1.0.2:
    collect-v8-coverage: private
  registry.npmmirror.com/color-convert/2.0.1:
    color-convert: private
  registry.npmmirror.com/color-name/1.1.4:
    color-name: private
  registry.npmmirror.com/combined-stream/1.0.8:
    combined-stream: private
  registry.npmmirror.com/commander/4.1.1:
    commander: private
  registry.npmmirror.com/comment-json/4.2.5:
    comment-json: private
  registry.npmmirror.com/component-emitter/1.3.1:
    component-emitter: private
  registry.npmmirror.com/concat-map/0.0.1:
    concat-map: private
  registry.npmmirror.com/concat-stream/1.6.2:
    concat-stream: private
  registry.npmmirror.com/consola/3.4.2:
    consola: private
  registry.npmmirror.com/content-disposition/1.0.0:
    content-disposition: private
  registry.npmmirror.com/content-type/1.0.5:
    content-type: private
  registry.npmmirror.com/convert-source-map/2.0.0:
    convert-source-map: private
  registry.npmmirror.com/cookie-signature/1.2.2:
    cookie-signature: private
  registry.npmmirror.com/cookie/0.7.1:
    cookie: private
  registry.npmmirror.com/cookiejar/2.1.4:
    cookiejar: private
  registry.npmmirror.com/core-util-is/1.0.3:
    core-util-is: private
  registry.npmmirror.com/cors/2.8.5:
    cors: private
  registry.npmmirror.com/cosmiconfig/8.3.6(typescript@5.7.3):
    cosmiconfig: private
  registry.npmmirror.com/create-jest/29.7.0(@types/node@22.10.7)(ts-node@10.9.2):
    create-jest: private
  registry.npmmirror.com/create-require/1.1.1:
    create-require: private
  registry.npmmirror.com/cross-spawn/7.0.6:
    cross-spawn: private
  registry.npmmirror.com/crypto-js/4.2.0:
    crypto-js: private
  registry.npmmirror.com/dayjs/1.11.13:
    dayjs: private
  registry.npmmirror.com/debug/4.4.1:
    debug: private
  registry.npmmirror.com/decompress-response/6.0.0:
    decompress-response: private
  registry.npmmirror.com/dedent/1.6.0:
    dedent: private
  registry.npmmirror.com/deep-is/0.1.4:
    deep-is: private
  registry.npmmirror.com/deepmerge/4.3.1:
    deepmerge: private
  registry.npmmirror.com/defaults/3.0.0:
    defaults: private
  registry.npmmirror.com/defer-to-connect/2.0.1:
    defer-to-connect: private
  registry.npmmirror.com/delayed-stream/1.0.0:
    delayed-stream: private
  registry.npmmirror.com/denque/2.1.0:
    denque: private
  registry.npmmirror.com/depd/2.0.0:
    depd: private
  registry.npmmirror.com/destroy/1.2.0:
    destroy: private
  registry.npmmirror.com/detect-newline/3.1.0:
    detect-newline: private
  registry.npmmirror.com/dezalgo/1.0.4:
    dezalgo: private
  registry.npmmirror.com/diff-sequences/29.6.3:
    diff-sequences: private
  registry.npmmirror.com/diff/4.0.2:
    diff: private
  registry.npmmirror.com/dot-case/3.0.4:
    dot-case: private
  registry.npmmirror.com/dotenv-expand/12.0.1:
    dotenv-expand: private
  registry.npmmirror.com/dotenv/16.4.7:
    dotenv: private
  registry.npmmirror.com/dunder-proto/1.0.1:
    dunder-proto: private
  registry.npmmirror.com/eastasianwidth/0.2.0:
    eastasianwidth: private
  registry.npmmirror.com/ecdsa-sig-formatter/1.0.11:
    ecdsa-sig-formatter: private
  registry.npmmirror.com/ee-first/1.1.1:
    ee-first: private
  registry.npmmirror.com/ejs/3.1.10:
    ejs: private
  registry.npmmirror.com/electron-to-chromium/1.5.177:
    electron-to-chromium: private
  registry.npmmirror.com/emittery/0.13.1:
    emittery: private
  registry.npmmirror.com/emoji-regex/8.0.0:
    emoji-regex: private
  registry.npmmirror.com/encodeurl/2.0.0:
    encodeurl: private
  registry.npmmirror.com/enhanced-resolve/5.18.2:
    enhanced-resolve: private
  registry.npmmirror.com/error-ex/1.3.2:
    error-ex: private
  registry.npmmirror.com/es-define-property/1.0.1:
    es-define-property: private
  registry.npmmirror.com/es-errors/1.3.0:
    es-errors: private
  registry.npmmirror.com/es-module-lexer/1.7.0:
    es-module-lexer: private
  registry.npmmirror.com/es-object-atoms/1.1.1:
    es-object-atoms: private
  registry.npmmirror.com/es-set-tostringtag/2.1.0:
    es-set-tostringtag: private
  registry.npmmirror.com/escalade/3.2.0:
    escalade: private
  registry.npmmirror.com/escape-html/1.0.3:
    escape-html: private
  registry.npmmirror.com/escape-string-regexp/4.0.0:
    escape-string-regexp: private
  registry.npmmirror.com/eslint-scope/8.4.0:
    eslint-scope: public
  registry.npmmirror.com/eslint-visitor-keys/4.2.1:
    eslint-visitor-keys: public
  registry.npmmirror.com/espree/10.4.0:
    espree: private
  registry.npmmirror.com/esprima/4.0.1:
    esprima: private
  registry.npmmirror.com/esquery/1.6.0:
    esquery: private
  registry.npmmirror.com/esrecurse/4.3.0:
    esrecurse: private
  registry.npmmirror.com/estraverse/5.3.0:
    estraverse: private
  registry.npmmirror.com/esutils/2.0.3:
    esutils: private
  registry.npmmirror.com/etag/1.8.1:
    etag: private
  registry.npmmirror.com/events/3.3.0:
    events: private
  registry.npmmirror.com/execa/5.1.1:
    execa: private
  registry.npmmirror.com/exit/0.1.2:
    exit: private
  registry.npmmirror.com/expect/29.7.0:
    expect: private
  registry.npmmirror.com/express/5.0.1:
    express: private
  registry.npmmirror.com/ext-list/2.2.2:
    ext-list: private
  registry.npmmirror.com/ext-name/5.0.0:
    ext-name: private
  registry.npmmirror.com/extend-shallow/2.0.1:
    extend-shallow: private
  registry.npmmirror.com/external-editor/3.1.0:
    external-editor: private
  registry.npmmirror.com/fast-deep-equal/3.1.3:
    fast-deep-equal: private
  registry.npmmirror.com/fast-diff/1.3.0:
    fast-diff: private
  registry.npmmirror.com/fast-fifo/1.3.2:
    fast-fifo: private
  registry.npmmirror.com/fast-glob/3.3.3:
    fast-glob: private
  registry.npmmirror.com/fast-json-stable-stringify/2.1.0:
    fast-json-stable-stringify: private
  registry.npmmirror.com/fast-levenshtein/2.0.6:
    fast-levenshtein: private
  registry.npmmirror.com/fast-safe-stringify/2.1.1:
    fast-safe-stringify: private
  registry.npmmirror.com/fast-uri/3.0.6:
    fast-uri: private
  registry.npmmirror.com/fastq/1.19.1:
    fastq: private
  registry.npmmirror.com/fb-watchman/2.0.2:
    fb-watchman: private
  registry.npmmirror.com/file-entry-cache/8.0.0:
    file-entry-cache: private
  registry.npmmirror.com/file-type/19.6.0:
    file-type: private
  registry.npmmirror.com/filelist/1.0.4:
    filelist: private
  registry.npmmirror.com/filename-reserved-regex/3.0.0:
    filename-reserved-regex: private
  registry.npmmirror.com/filenamify/6.0.0:
    filenamify: private
  registry.npmmirror.com/fill-range/7.1.1:
    fill-range: private
  registry.npmmirror.com/finalhandler/2.1.0:
    finalhandler: private
  registry.npmmirror.com/find-up/5.0.0:
    find-up: private
  registry.npmmirror.com/find-versions/5.1.0:
    find-versions: private
  registry.npmmirror.com/flat-cache/4.0.1:
    flat-cache: private
  registry.npmmirror.com/flatted/3.3.3:
    flatted: private
  registry.npmmirror.com/foreground-child/3.3.1:
    foreground-child: private
  registry.npmmirror.com/fork-ts-checker-webpack-plugin/9.0.2(typescript@5.7.3)(webpack@5.97.1):
    fork-ts-checker-webpack-plugin: private
  registry.npmmirror.com/form-data-encoder/2.1.4:
    form-data-encoder: private
  registry.npmmirror.com/form-data/4.0.3:
    form-data: private
  registry.npmmirror.com/formidable/3.5.4:
    formidable: private
  registry.npmmirror.com/formstream/1.5.1:
    formstream: private
  registry.npmmirror.com/forwarded/0.2.0:
    forwarded: private
  registry.npmmirror.com/fresh/2.0.0:
    fresh: private
  registry.npmmirror.com/fs-extra/10.1.0:
    fs-extra: private
  registry.npmmirror.com/fs-monkey/1.0.6:
    fs-monkey: private
  registry.npmmirror.com/fs.realpath/1.0.0:
    fs.realpath: private
  registry.npmmirror.com/fsevents/2.3.3:
    fsevents: private
  registry.npmmirror.com/function-bind/1.1.2:
    function-bind: private
  registry.npmmirror.com/generate-function/2.3.1:
    generate-function: private
  registry.npmmirror.com/gensync/1.0.0-beta.2:
    gensync: private
  registry.npmmirror.com/get-caller-file/2.0.5:
    get-caller-file: private
  registry.npmmirror.com/get-intrinsic/1.3.0:
    get-intrinsic: private
  registry.npmmirror.com/get-package-type/0.1.0:
    get-package-type: private
  registry.npmmirror.com/get-proto/1.0.1:
    get-proto: private
  registry.npmmirror.com/get-stream/6.0.1:
    get-stream: private
  registry.npmmirror.com/glob-parent/6.0.2:
    glob-parent: private
  registry.npmmirror.com/glob-to-regexp/0.4.1:
    glob-to-regexp: private
  registry.npmmirror.com/glob/11.0.1:
    glob: private
  registry.npmmirror.com/gopd/1.2.0:
    gopd: private
  registry.npmmirror.com/got/13.0.0:
    got: private
  registry.npmmirror.com/graceful-fs/4.2.11:
    graceful-fs: private
  registry.npmmirror.com/graphemer/1.4.0:
    graphemer: private
  registry.npmmirror.com/has-flag/4.0.0:
    has-flag: private
  registry.npmmirror.com/has-own-prop/2.0.0:
    has-own-prop: private
  registry.npmmirror.com/has-symbols/1.1.0:
    has-symbols: private
  registry.npmmirror.com/has-tostringtag/1.0.2:
    has-tostringtag: private
  registry.npmmirror.com/hasown/2.0.2:
    hasown: private
  registry.npmmirror.com/html-escaper/2.0.2:
    html-escaper: private
  registry.npmmirror.com/http-cache-semantics/4.2.0:
    http-cache-semantics: private
  registry.npmmirror.com/http-errors/2.0.0:
    http-errors: private
  registry.npmmirror.com/http2-wrapper/2.2.1:
    http2-wrapper: private
  registry.npmmirror.com/human-signals/2.1.0:
    human-signals: private
  registry.npmmirror.com/iconv-lite/0.6.3:
    iconv-lite: private
  registry.npmmirror.com/ieee754/1.2.1:
    ieee754: private
  registry.npmmirror.com/ignore/5.3.2:
    ignore: private
  registry.npmmirror.com/import-fresh/3.3.1:
    import-fresh: private
  registry.npmmirror.com/import-local/3.2.0:
    import-local: private
  registry.npmmirror.com/imurmurhash/0.1.4:
    imurmurhash: private
  registry.npmmirror.com/inflight/1.0.6:
    inflight: private
  registry.npmmirror.com/inherits/2.0.4:
    inherits: private
  registry.npmmirror.com/inspect-with-kind/1.0.5:
    inspect-with-kind: private
  registry.npmmirror.com/ipaddr.js/1.9.1:
    ipaddr.js: private
  registry.npmmirror.com/is-arrayish/0.2.1:
    is-arrayish: private
  registry.npmmirror.com/is-binary-path/2.1.0:
    is-binary-path: private
  registry.npmmirror.com/is-core-module/2.16.1:
    is-core-module: private
  registry.npmmirror.com/is-extendable/0.1.1:
    is-extendable: private
  registry.npmmirror.com/is-extglob/2.1.1:
    is-extglob: private
  registry.npmmirror.com/is-fullwidth-code-point/3.0.0:
    is-fullwidth-code-point: private
  registry.npmmirror.com/is-generator-fn/2.1.0:
    is-generator-fn: private
  registry.npmmirror.com/is-glob/4.0.3:
    is-glob: private
  registry.npmmirror.com/is-interactive/1.0.0:
    is-interactive: private
  registry.npmmirror.com/is-number/7.0.0:
    is-number: private
  registry.npmmirror.com/is-plain-obj/1.1.0:
    is-plain-obj: private
  registry.npmmirror.com/is-promise/4.0.0:
    is-promise: private
  registry.npmmirror.com/is-property/1.0.2:
    is-property: private
  registry.npmmirror.com/is-stream/2.0.1:
    is-stream: private
  registry.npmmirror.com/is-unicode-supported/0.1.0:
    is-unicode-supported: private
  registry.npmmirror.com/isarray/1.0.0:
    isarray: private
  registry.npmmirror.com/isexe/2.0.0:
    isexe: private
  registry.npmmirror.com/istanbul-lib-coverage/3.2.2:
    istanbul-lib-coverage: private
  registry.npmmirror.com/istanbul-lib-instrument/6.0.3:
    istanbul-lib-instrument: private
  registry.npmmirror.com/istanbul-lib-report/3.0.1:
    istanbul-lib-report: private
  registry.npmmirror.com/istanbul-lib-source-maps/4.0.1:
    istanbul-lib-source-maps: private
  registry.npmmirror.com/istanbul-reports/3.1.7:
    istanbul-reports: private
  registry.npmmirror.com/iterare/1.2.1:
    iterare: private
  registry.npmmirror.com/jackspeak/3.4.3:
    jackspeak: private
  registry.npmmirror.com/jake/10.9.2:
    jake: private
  registry.npmmirror.com/jest-changed-files/29.7.0:
    jest-changed-files: private
  registry.npmmirror.com/jest-circus/29.7.0:
    jest-circus: private
  registry.npmmirror.com/jest-cli/29.7.0(@types/node@22.10.7)(ts-node@10.9.2):
    jest-cli: private
  registry.npmmirror.com/jest-config/29.7.0(@types/node@22.10.7)(ts-node@10.9.2):
    jest-config: private
  registry.npmmirror.com/jest-diff/29.7.0:
    jest-diff: private
  registry.npmmirror.com/jest-docblock/29.7.0:
    jest-docblock: private
  registry.npmmirror.com/jest-each/29.7.0:
    jest-each: private
  registry.npmmirror.com/jest-environment-node/29.7.0:
    jest-environment-node: private
  registry.npmmirror.com/jest-get-type/29.6.3:
    jest-get-type: private
  registry.npmmirror.com/jest-haste-map/29.7.0:
    jest-haste-map: private
  registry.npmmirror.com/jest-leak-detector/29.7.0:
    jest-leak-detector: private
  registry.npmmirror.com/jest-matcher-utils/29.7.0:
    jest-matcher-utils: private
  registry.npmmirror.com/jest-message-util/29.7.0:
    jest-message-util: private
  registry.npmmirror.com/jest-mock/29.7.0:
    jest-mock: private
  registry.npmmirror.com/jest-pnp-resolver/1.2.3(jest-resolve@29.7.0):
    jest-pnp-resolver: private
  registry.npmmirror.com/jest-regex-util/29.6.3:
    jest-regex-util: private
  registry.npmmirror.com/jest-resolve-dependencies/29.7.0:
    jest-resolve-dependencies: private
  registry.npmmirror.com/jest-resolve/29.7.0:
    jest-resolve: private
  registry.npmmirror.com/jest-runner/29.7.0:
    jest-runner: private
  registry.npmmirror.com/jest-runtime/29.7.0:
    jest-runtime: private
  registry.npmmirror.com/jest-snapshot/29.7.0:
    jest-snapshot: private
  registry.npmmirror.com/jest-util/29.7.0:
    jest-util: private
  registry.npmmirror.com/jest-validate/29.7.0:
    jest-validate: private
  registry.npmmirror.com/jest-watcher/29.7.0:
    jest-watcher: private
  registry.npmmirror.com/jest-worker/29.7.0:
    jest-worker: private
  registry.npmmirror.com/js-tokens/4.0.0:
    js-tokens: private
  registry.npmmirror.com/js-yaml/4.1.0:
    js-yaml: private
  registry.npmmirror.com/jsesc/3.1.0:
    jsesc: private
  registry.npmmirror.com/json-buffer/3.0.1:
    json-buffer: private
  registry.npmmirror.com/json-parse-even-better-errors/2.3.1:
    json-parse-even-better-errors: private
  registry.npmmirror.com/json-schema-traverse/0.4.1:
    json-schema-traverse: private
  registry.npmmirror.com/json-stable-stringify-without-jsonify/1.0.1:
    json-stable-stringify-without-jsonify: private
  registry.npmmirror.com/json5/2.2.3:
    json5: private
  registry.npmmirror.com/jsonc-parser/3.3.1:
    jsonc-parser: private
  registry.npmmirror.com/jsonfile/6.1.0:
    jsonfile: private
  registry.npmmirror.com/jsonwebtoken/9.0.2:
    jsonwebtoken: private
  registry.npmmirror.com/jwa/1.4.2:
    jwa: private
  registry.npmmirror.com/jws/3.2.2:
    jws: private
  registry.npmmirror.com/keyv/4.5.4:
    keyv: private
  registry.npmmirror.com/kind-of/6.0.3:
    kind-of: private
  registry.npmmirror.com/kleur/3.0.3:
    kleur: private
  registry.npmmirror.com/leven/3.1.0:
    leven: private
  registry.npmmirror.com/levn/0.4.1:
    levn: private
  registry.npmmirror.com/libphonenumber-js/1.12.9:
    libphonenumber-js: private
  registry.npmmirror.com/lines-and-columns/1.2.4:
    lines-and-columns: private
  registry.npmmirror.com/loader-runner/4.3.0:
    loader-runner: private
  registry.npmmirror.com/locate-path/6.0.0:
    locate-path: private
  registry.npmmirror.com/lodash.includes/4.3.0:
    lodash.includes: private
  registry.npmmirror.com/lodash.isboolean/3.0.3:
    lodash.isboolean: private
  registry.npmmirror.com/lodash.isinteger/4.0.4:
    lodash.isinteger: private
  registry.npmmirror.com/lodash.isnumber/3.0.3:
    lodash.isnumber: private
  registry.npmmirror.com/lodash.isplainobject/4.0.6:
    lodash.isplainobject: private
  registry.npmmirror.com/lodash.isstring/4.0.1:
    lodash.isstring: private
  registry.npmmirror.com/lodash.memoize/4.1.2:
    lodash.memoize: private
  registry.npmmirror.com/lodash.merge/4.6.2:
    lodash.merge: private
  registry.npmmirror.com/lodash.once/4.1.1:
    lodash.once: private
  registry.npmmirror.com/lodash/4.17.21:
    lodash: private
  registry.npmmirror.com/log-symbols/4.1.0:
    log-symbols: private
  registry.npmmirror.com/long/5.3.2:
    long: private
  registry.npmmirror.com/lower-case/2.0.2:
    lower-case: private
  registry.npmmirror.com/lowercase-keys/3.0.0:
    lowercase-keys: private
  registry.npmmirror.com/lru-cache/7.18.3:
    lru-cache: private
  registry.npmmirror.com/lru.min/1.1.2:
    lru.min: private
  registry.npmmirror.com/magic-string/0.30.12:
    magic-string: private
  registry.npmmirror.com/make-dir/4.0.0:
    make-dir: private
  registry.npmmirror.com/make-error/1.3.6:
    make-error: private
  registry.npmmirror.com/makeerror/1.0.12:
    makeerror: private
  registry.npmmirror.com/map-obj/4.3.0:
    map-obj: private
  registry.npmmirror.com/math-intrinsics/1.1.0:
    math-intrinsics: private
  registry.npmmirror.com/media-typer/0.3.0:
    media-typer: private
  registry.npmmirror.com/memfs/3.5.3:
    memfs: private
  registry.npmmirror.com/merge-descriptors/2.0.0:
    merge-descriptors: private
  registry.npmmirror.com/merge-stream/2.0.0:
    merge-stream: private
  registry.npmmirror.com/merge2/1.4.1:
    merge2: private
  registry.npmmirror.com/methods/1.1.2:
    methods: private
  registry.npmmirror.com/micromatch/4.0.8:
    micromatch: private
  registry.npmmirror.com/mime-db/1.52.0:
    mime-db: private
  registry.npmmirror.com/mime-types/3.0.1:
    mime-types: private
  registry.npmmirror.com/mime/2.6.0:
    mime: private
  registry.npmmirror.com/mimic-fn/2.1.0:
    mimic-fn: private
  registry.npmmirror.com/mimic-response/4.0.0:
    mimic-response: private
  registry.npmmirror.com/minimatch/3.1.2:
    minimatch: private
  registry.npmmirror.com/minimist/1.2.8:
    minimist: private
  registry.npmmirror.com/minipass/7.1.2:
    minipass: private
  registry.npmmirror.com/mkdirp/0.5.6:
    mkdirp: private
  registry.npmmirror.com/ms/2.1.3:
    ms: private
  registry.npmmirror.com/multer/1.4.5-lts.1:
    multer: private
  registry.npmmirror.com/mute-stream/2.0.0:
    mute-stream: private
  registry.npmmirror.com/named-placeholders/1.1.3:
    named-placeholders: private
  registry.npmmirror.com/natural-compare/1.4.0:
    natural-compare: private
  registry.npmmirror.com/negotiator/1.0.0:
    negotiator: private
  registry.npmmirror.com/neo-async/2.6.2:
    neo-async: private
  registry.npmmirror.com/no-case/3.0.4:
    no-case: private
  registry.npmmirror.com/node-abort-controller/3.1.1:
    node-abort-controller: private
  registry.npmmirror.com/node-emoji/1.11.0:
    node-emoji: private
  registry.npmmirror.com/node-hex/1.0.1:
    node-hex: private
  registry.npmmirror.com/node-int64/0.4.0:
    node-int64: private
  registry.npmmirror.com/node-releases/2.0.19:
    node-releases: private
  registry.npmmirror.com/normalize-path/3.0.0:
    normalize-path: private
  registry.npmmirror.com/normalize-url/8.0.2:
    normalize-url: private
  registry.npmmirror.com/npm-run-path/4.0.1:
    npm-run-path: private
  registry.npmmirror.com/object-assign/4.1.1:
    object-assign: private
  registry.npmmirror.com/object-inspect/1.13.4:
    object-inspect: private
  registry.npmmirror.com/on-finished/2.4.1:
    on-finished: private
  registry.npmmirror.com/once/1.4.0:
    once: private
  registry.npmmirror.com/onetime/5.1.2:
    onetime: private
  registry.npmmirror.com/optionator/0.9.4:
    optionator: private
  registry.npmmirror.com/ora/5.4.1:
    ora: private
  registry.npmmirror.com/os-tmpdir/1.0.2:
    os-tmpdir: private
  registry.npmmirror.com/p-cancelable/3.0.0:
    p-cancelable: private
  registry.npmmirror.com/p-limit/3.1.0:
    p-limit: private
  registry.npmmirror.com/p-locate/5.0.0:
    p-locate: private
  registry.npmmirror.com/p-try/2.2.0:
    p-try: private
  registry.npmmirror.com/package-json-from-dist/1.0.1:
    package-json-from-dist: private
  registry.npmmirror.com/parent-module/1.0.1:
    parent-module: private
  registry.npmmirror.com/parse-json/5.2.0:
    parse-json: private
  registry.npmmirror.com/parseurl/1.3.3:
    parseurl: private
  registry.npmmirror.com/passport-strategy/1.0.0:
    passport-strategy: private
  registry.npmmirror.com/path-exists/4.0.0:
    path-exists: private
  registry.npmmirror.com/path-is-absolute/1.0.1:
    path-is-absolute: private
  registry.npmmirror.com/path-key/3.1.1:
    path-key: private
  registry.npmmirror.com/path-parse/1.0.7:
    path-parse: private
  registry.npmmirror.com/path-scurry/1.11.1:
    path-scurry: private
  registry.npmmirror.com/path-to-regexp/8.2.0:
    path-to-regexp: private
  registry.npmmirror.com/path-type/4.0.0:
    path-type: private
  registry.npmmirror.com/pause-stream/0.0.11:
    pause-stream: private
  registry.npmmirror.com/pause/0.0.1:
    pause: private
  registry.npmmirror.com/peek-readable/5.4.2:
    peek-readable: private
  registry.npmmirror.com/pend/1.2.0:
    pend: private
  registry.npmmirror.com/picocolors/1.1.1:
    picocolors: private
  registry.npmmirror.com/picomatch/4.0.2:
    picomatch: private
  registry.npmmirror.com/pirates/4.0.7:
    pirates: private
  registry.npmmirror.com/piscina/4.9.2:
    piscina: private
  registry.npmmirror.com/pkg-dir/4.2.0:
    pkg-dir: private
  registry.npmmirror.com/pluralize/8.0.0:
    pluralize: private
  registry.npmmirror.com/prelude-ls/1.2.1:
    prelude-ls: private
  registry.npmmirror.com/prettier-linter-helpers/1.0.0:
    prettier-linter-helpers: public
  registry.npmmirror.com/pretty-format/29.7.0:
    pretty-format: private
  registry.npmmirror.com/process-nextick-args/2.0.1:
    process-nextick-args: private
  registry.npmmirror.com/prompts/2.4.2:
    prompts: private
  registry.npmmirror.com/proxy-addr/2.0.7:
    proxy-addr: private
  registry.npmmirror.com/punycode/2.3.1:
    punycode: private
  registry.npmmirror.com/pure-rand/6.1.0:
    pure-rand: private
  registry.npmmirror.com/qs/6.13.0:
    qs: private
  registry.npmmirror.com/queue-microtask/1.2.3:
    queue-microtask: private
  registry.npmmirror.com/quick-lru/5.1.1:
    quick-lru: private
  registry.npmmirror.com/randombytes/2.1.0:
    randombytes: private
  registry.npmmirror.com/range-parser/1.2.1:
    range-parser: private
  registry.npmmirror.com/raw-body/2.5.2:
    raw-body: private
  registry.npmmirror.com/react-is/18.3.1:
    react-is: private
  registry.npmmirror.com/readable-stream/3.6.2:
    readable-stream: private
  registry.npmmirror.com/readdirp/4.1.2:
    readdirp: private
  registry.npmmirror.com/repeat-string/1.6.1:
    repeat-string: private
  registry.npmmirror.com/require-directory/2.1.1:
    require-directory: private
  registry.npmmirror.com/require-from-string/2.0.2:
    require-from-string: private
  registry.npmmirror.com/resolve-alpn/1.2.1:
    resolve-alpn: private
  registry.npmmirror.com/resolve-cwd/3.0.0:
    resolve-cwd: private
  registry.npmmirror.com/resolve-from/4.0.0:
    resolve-from: private
  registry.npmmirror.com/resolve.exports/2.0.3:
    resolve.exports: private
  registry.npmmirror.com/resolve/1.22.10:
    resolve: private
  registry.npmmirror.com/responselike/3.0.0:
    responselike: private
  registry.npmmirror.com/restore-cursor/3.1.0:
    restore-cursor: private
  registry.npmmirror.com/reusify/1.1.0:
    reusify: private
  registry.npmmirror.com/router/2.2.0:
    router: private
  registry.npmmirror.com/run-parallel/1.2.0:
    run-parallel: private
  registry.npmmirror.com/safe-buffer/5.2.1:
    safe-buffer: private
  registry.npmmirror.com/safer-buffer/2.1.2:
    safer-buffer: private
  registry.npmmirror.com/schema-utils/3.3.0:
    schema-utils: private
  registry.npmmirror.com/seek-bzip/2.0.0:
    seek-bzip: private
  registry.npmmirror.com/semver-regex/4.0.5:
    semver-regex: private
  registry.npmmirror.com/semver-truncate/3.0.0:
    semver-truncate: private
  registry.npmmirror.com/semver/7.7.2:
    semver: private
  registry.npmmirror.com/send/1.2.0:
    send: private
  registry.npmmirror.com/seq-queue/0.0.5:
    seq-queue: private
  registry.npmmirror.com/serialize-javascript/6.0.2:
    serialize-javascript: private
  registry.npmmirror.com/serve-static/2.2.0:
    serve-static: private
  registry.npmmirror.com/setprototypeof/1.2.0:
    setprototypeof: private
  registry.npmmirror.com/sha.js/2.4.11:
    sha.js: private
  registry.npmmirror.com/shebang-command/2.0.0:
    shebang-command: private
  registry.npmmirror.com/shebang-regex/3.0.0:
    shebang-regex: private
  registry.npmmirror.com/side-channel-list/1.0.0:
    side-channel-list: private
  registry.npmmirror.com/side-channel-map/1.0.1:
    side-channel-map: private
  registry.npmmirror.com/side-channel-weakmap/1.0.2:
    side-channel-weakmap: private
  registry.npmmirror.com/side-channel/1.1.0:
    side-channel: private
  registry.npmmirror.com/signal-exit/4.1.0:
    signal-exit: private
  registry.npmmirror.com/sisteransi/1.0.5:
    sisteransi: private
  registry.npmmirror.com/slash/3.0.0:
    slash: private
  registry.npmmirror.com/snake-case/3.0.4:
    snake-case: private
  registry.npmmirror.com/snakecase-keys/8.0.1:
    snakecase-keys: private
  registry.npmmirror.com/sort-keys-length/1.0.1:
    sort-keys-length: private
  registry.npmmirror.com/sort-keys/1.1.2:
    sort-keys: private
  registry.npmmirror.com/source-map/0.7.4:
    source-map: private
  registry.npmmirror.com/sprintf-js/1.0.3:
    sprintf-js: private
  registry.npmmirror.com/sql-highlight/6.1.0:
    sql-highlight: private
  registry.npmmirror.com/sqlstring/2.3.3:
    sqlstring: private
  registry.npmmirror.com/sse-decoder/1.0.0:
    sse-decoder: private
  registry.npmmirror.com/stack-utils/2.0.6:
    stack-utils: private
  registry.npmmirror.com/statuses/2.0.1:
    statuses: private
  registry.npmmirror.com/streamsearch/1.1.0:
    streamsearch: private
  registry.npmmirror.com/streamx/2.22.1:
    streamx: private
  registry.npmmirror.com/string-length/4.0.2:
    string-length: private
  registry.npmmirror.com/string-width/4.2.3:
    string-width: private
    string-width-cjs: private
  registry.npmmirror.com/string_decoder/1.1.1:
    string_decoder: private
  registry.npmmirror.com/strip-ansi/6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  registry.npmmirror.com/strip-bom/3.0.0:
    strip-bom: private
  registry.npmmirror.com/strip-dirs/3.0.0:
    strip-dirs: private
  registry.npmmirror.com/strip-final-newline/2.0.0:
    strip-final-newline: private
  registry.npmmirror.com/strip-json-comments/3.1.1:
    strip-json-comments: private
  registry.npmmirror.com/strtok3/9.1.1:
    strtok3: private
  registry.npmmirror.com/superagent/9.0.2:
    superagent: private
  registry.npmmirror.com/supports-color/7.2.0:
    supports-color: private
  registry.npmmirror.com/supports-preserve-symlinks-flag/1.0.0:
    supports-preserve-symlinks-flag: private
  registry.npmmirror.com/swagger-ui-dist/5.21.0:
    swagger-ui-dist: private
  registry.npmmirror.com/symbol-observable/4.0.0:
    symbol-observable: private
  registry.npmmirror.com/synckit/0.9.3:
    synckit: private
  registry.npmmirror.com/tapable/2.2.2:
    tapable: private
  registry.npmmirror.com/tar-stream/3.1.7:
    tar-stream: private
  registry.npmmirror.com/terser-webpack-plugin/5.3.14(@swc/core@1.10.7)(webpack@5.97.1):
    terser-webpack-plugin: private
  registry.npmmirror.com/terser/5.43.1:
    terser: private
  registry.npmmirror.com/test-exclude/6.0.0:
    test-exclude: private
  registry.npmmirror.com/text-decoder/1.2.3:
    text-decoder: private
  registry.npmmirror.com/through/2.3.8:
    through: private
  registry.npmmirror.com/tmp/0.0.33:
    tmp: private
  registry.npmmirror.com/tmpl/1.0.5:
    tmpl: private
  registry.npmmirror.com/to-regex-range/5.0.1:
    to-regex-range: private
  registry.npmmirror.com/toidentifier/1.0.1:
    toidentifier: private
  registry.npmmirror.com/token-types/6.0.3:
    token-types: private
  registry.npmmirror.com/tree-kill/1.2.2:
    tree-kill: private
  registry.npmmirror.com/ts-api-utils/2.1.0(typescript@5.7.3):
    ts-api-utils: private
  registry.npmmirror.com/tsconfig-paths-webpack-plugin/4.2.0:
    tsconfig-paths-webpack-plugin: private
  registry.npmmirror.com/tslib/2.8.1:
    tslib: private
  registry.npmmirror.com/tweetnacl/1.0.3:
    tweetnacl: private
  registry.npmmirror.com/type-check/0.4.0:
    type-check: private
  registry.npmmirror.com/type-detect/4.0.8:
    type-detect: private
  registry.npmmirror.com/type-fest/1.4.0:
    type-fest: private
  registry.npmmirror.com/type-is/1.6.18:
    type-is: private
  registry.npmmirror.com/typedarray/0.0.6:
    typedarray: private
  registry.npmmirror.com/uid/2.0.2:
    uid: private
  registry.npmmirror.com/uint8array-extras/1.4.0:
    uint8array-extras: private
  registry.npmmirror.com/unbzip2-stream/1.4.3:
    unbzip2-stream: private
  registry.npmmirror.com/undici-types/6.20.0:
    undici-types: private
  registry.npmmirror.com/undici/7.11.0:
    undici: private
  registry.npmmirror.com/unescape/1.0.1:
    unescape: private
  registry.npmmirror.com/universalify/2.0.1:
    universalify: private
  registry.npmmirror.com/unpipe/1.0.0:
    unpipe: private
  registry.npmmirror.com/update-browserslist-db/1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  registry.npmmirror.com/uri-js/4.4.1:
    uri-js: private
  registry.npmmirror.com/urllib/4.6.12:
    urllib: private
  registry.npmmirror.com/util-deprecate/1.0.2:
    util-deprecate: private
  registry.npmmirror.com/utility/2.5.0:
    utility: private
  registry.npmmirror.com/utils-merge/1.0.1:
    utils-merge: private
  registry.npmmirror.com/uuid/11.1.0:
    uuid: private
  registry.npmmirror.com/v8-compile-cache-lib/3.0.1:
    v8-compile-cache-lib: private
  registry.npmmirror.com/v8-to-istanbul/9.3.0:
    v8-to-istanbul: private
  registry.npmmirror.com/validator/13.15.15:
    validator: private
  registry.npmmirror.com/vary/1.1.2:
    vary: private
  registry.npmmirror.com/walker/1.0.8:
    walker: private
  registry.npmmirror.com/watchpack/2.4.4:
    watchpack: private
  registry.npmmirror.com/wcwidth/1.0.1:
    wcwidth: private
  registry.npmmirror.com/webpack-node-externals/3.0.0:
    webpack-node-externals: private
  registry.npmmirror.com/webpack-sources/3.3.3:
    webpack-sources: private
  registry.npmmirror.com/webpack/5.97.1(@swc/core@1.10.7):
    webpack: private
  registry.npmmirror.com/which/2.0.2:
    which: private
  registry.npmmirror.com/word-wrap/1.2.5:
    word-wrap: private
  registry.npmmirror.com/wrap-ansi/7.0.0:
    wrap-ansi: private
    wrap-ansi-cjs: private
  registry.npmmirror.com/wrappy/1.0.2:
    wrappy: private
  registry.npmmirror.com/write-file-atomic/4.0.2:
    write-file-atomic: private
  registry.npmmirror.com/xtend/4.0.2:
    xtend: private
  registry.npmmirror.com/y18n/5.0.8:
    y18n: private
  registry.npmmirror.com/yallist/3.1.1:
    yallist: private
  registry.npmmirror.com/yargs-parser/21.1.1:
    yargs-parser: private
  registry.npmmirror.com/yargs/17.7.2:
    yargs: private
  registry.npmmirror.com/yauzl/3.2.0:
    yauzl: private
  registry.npmmirror.com/ylru/2.0.0:
    ylru: private
  registry.npmmirror.com/yn/3.1.1:
    yn: private
  registry.npmmirror.com/yocto-queue/0.1.0:
    yocto-queue: private
  registry.npmmirror.com/yoctocolors-cjs/2.1.2:
    yoctocolors-cjs: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@8.6.12
pendingBuilds: []
prunedAt: Sun, 29 Jun 2025 16:26:06 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: http://registry.npm.taobao.org/
skipped:
  - registry.npmmirror.com/@napi-rs/nice-android-arm-eabi/1.0.1
  - registry.npmmirror.com/@napi-rs/nice-android-arm64/1.0.1
  - registry.npmmirror.com/@napi-rs/nice-darwin-x64/1.0.1
  - registry.npmmirror.com/@napi-rs/nice-freebsd-x64/1.0.1
  - registry.npmmirror.com/@napi-rs/nice-linux-arm-gnueabihf/1.0.1
  - registry.npmmirror.com/@napi-rs/nice-linux-arm64-gnu/1.0.1
  - registry.npmmirror.com/@napi-rs/nice-linux-arm64-musl/1.0.1
  - registry.npmmirror.com/@napi-rs/nice-linux-ppc64-gnu/1.0.1
  - registry.npmmirror.com/@napi-rs/nice-linux-riscv64-gnu/1.0.1
  - registry.npmmirror.com/@napi-rs/nice-linux-s390x-gnu/1.0.1
  - registry.npmmirror.com/@napi-rs/nice-linux-x64-gnu/1.0.1
  - registry.npmmirror.com/@napi-rs/nice-linux-x64-musl/1.0.1
  - registry.npmmirror.com/@napi-rs/nice-win32-arm64-msvc/1.0.1
  - registry.npmmirror.com/@napi-rs/nice-win32-ia32-msvc/1.0.1
  - registry.npmmirror.com/@napi-rs/nice-win32-x64-msvc/1.0.1
  - registry.npmmirror.com/@swc/core-darwin-x64/1.10.7
  - registry.npmmirror.com/@swc/core-linux-arm-gnueabihf/1.10.7
  - registry.npmmirror.com/@swc/core-linux-arm64-gnu/1.10.7
  - registry.npmmirror.com/@swc/core-linux-arm64-musl/1.10.7
  - registry.npmmirror.com/@swc/core-linux-x64-gnu/1.10.7
  - registry.npmmirror.com/@swc/core-linux-x64-musl/1.10.7
  - registry.npmmirror.com/@swc/core-win32-arm64-msvc/1.10.7
  - registry.npmmirror.com/@swc/core-win32-ia32-msvc/1.10.7
  - registry.npmmirror.com/@swc/core-win32-x64-msvc/1.10.7
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
