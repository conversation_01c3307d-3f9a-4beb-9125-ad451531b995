import { DataSource } from 'typeorm';
import { PaymentConfig, PaymentConfigStatus } from '../entities/payment-config.entity';

async function initPaymentConfig() {
  const dataSource = new DataSource({
    type: 'mysql',
    host: '*************',
    port: 3306,
    username: 'df_y<PERSON><PERSON><PERSON>_cn',
    password: 'Ej8Ej8Ej8',
    database: 'df_yue<PERSON>hui_cn',
    entities: [PaymentConfig],
    synchronize: false,
  });

  await dataSource.initialize();

  const paymentConfigRepository = dataSource.getRepository(PaymentConfig);

  // 检查是否已存在Alipay配置
  const existingConfig = await paymentConfigRepository.findOne({
    where: { provider: 'alipay', isActive: true }
  });

  if (existingConfig) {
    console.log('Alipay配置已存在，跳过初始化');
    await dataSource.destroy();
    return;
  }

  // 创建初始Alipay配置（沙箱环境）
  const alipayConfig = new PaymentConfig();
  alipayConfig.provider = 'alipay';
  alipayConfig.appId = '2021000122671080'; // 沙箱应用ID
  alipayConfig.privateKey = `-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB
xQOndFompydXhCVhMpM+XdD8zG7z4ckVDXI1Iy4Hr7k/9CTwhGKv4C/OBw==
-----END PRIVATE KEY-----`; // 示例私钥，实际使用时需要替换
  alipayConfig.publicKey = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuVSU1LfVLPHCgcUDp3Ra
Jqcnl4QlYTKTPl3Q/Mxu8+HJFQlyNSMuB6+5P/Qk8IRir+AvzgcP
-----END PUBLIC KEY-----`; // 示例公钥，实际使用时需要替换
  alipayConfig.gateway = 'https://openapi.alipaydev.com/gateway.do'; // 沙箱网关
  alipayConfig.isSandbox = true;
  alipayConfig.defaultFeeRate = 0.006; // 0.6%手续费
  alipayConfig.isActive = true;
  alipayConfig.status = PaymentConfigStatus.ACTIVE;

  await paymentConfigRepository.save(alipayConfig);

  console.log('Alipay沙箱配置初始化完成');
  console.log('配置ID:', alipayConfig.id);
  console.log('应用ID:', alipayConfig.appId);
  console.log('网关地址:', alipayConfig.gateway);

  await dataSource.destroy();
}

initPaymentConfig().catch(console.error);
