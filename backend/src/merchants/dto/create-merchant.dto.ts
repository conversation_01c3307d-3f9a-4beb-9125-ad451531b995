import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsEmail, IsOptional, IsDecimal, IsBoolean } from 'class-validator';

export class CreateMerchantDto {
  @ApiProperty({ description: '商户名称', example: '测试商户' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: '联系人姓名', example: '张三', required: false })
  @IsString()
  @IsOptional()
  contactName?: string;

  @ApiProperty({ description: '联系电话', example: '13800138000', required: false })
  @IsString()
  @IsOptional()
  contactPhone?: string;

  @ApiProperty({ description: '联系邮箱', example: '<EMAIL>', required: false })
  @IsEmail()
  @IsOptional()
  contactEmail?: string;

  @ApiProperty({ description: 'API权限开关', example: false, required: false })
  @IsBoolean()
  @IsOptional()
  apiEnabled?: boolean;

  @ApiProperty({ description: '自定义手续费比例', example: '0.0050', required: false })
  @IsDecimal()
  @IsOptional()
  feeRate?: string;

  @ApiProperty({ description: '支付宝支付授权状态', example: false, required: false })
  @IsBoolean()
  @IsOptional()
  alipayAuthorized?: boolean;
}
