import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
const AlipaySdk = require('alipay-sdk');
import { Transaction, TransactionType, TransactionStatus } from '../entities/transaction.entity';
import { Merchant } from '../entities/merchant.entity';
import { PaymentConfig } from '../entities/payment-config.entity';
import { CreateRechargeDto } from './dto/create-recharge.dto';
import { CreatePayoutDto } from './dto/create-payout.dto';

@Injectable()
export class AlipayService {
  private readonly logger = new Logger(AlipayService.name);
  private alipaySdk: any;

  constructor(
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
    @InjectRepository(Merchant)
    private merchantRepository: Repository<Merchant>,
    @InjectRepository(PaymentConfig)
    private paymentConfigRepository: Repository<PaymentConfig>,
    private configService: ConfigService,
  ) {
    // TODO: 修复Alipay SDK导入问题后再启用
    // this.initializeAlipaySDK();
  }

  private async initializeAlipaySDK() {
    // 从配置中获取支付宝配置
    const config = await this.getAlipayConfig();
    
    this.alipaySdk = new AlipaySdk({
      appId: config.appId,
      privateKey: config.privateKey,
      alipayPublicKey: config.alipayPublicKey,
      gateway: config.gateway || 'https://openapi.alipay.com/gateway.do',
      timeout: 5000,
      camelCase: true,
    });
  }

  private async getAlipayConfig() {
    // 从数据库获取支付宝配置
    const config = await this.paymentConfigRepository.findOne({
      where: { provider: 'alipay', isActive: true },
    });

    if (!config) {
      // 如果数据库中没有配置，使用环境变量
      return {
        appId: this.configService.get<string>('ALIPAY_APP_ID'),
        privateKey: this.configService.get<string>('ALIPAY_PRIVATE_KEY'),
        alipayPublicKey: this.configService.get<string>('ALIPAY_PUBLIC_KEY'),
        gateway: this.configService.get<string>('ALIPAY_GATEWAY'),
      };
    }

    return {
      appId: config.appId,
      privateKey: config.privateKey,
      alipayPublicKey: config.publicKey,
      gateway: config.gateway,
    };
  }

  /**
   * 创建充值订单（扫码支付）
   */
  async createRecharge(merchantId: number, createRechargeDto: CreateRechargeDto) {
    const merchant = await this.merchantRepository.findOne({
      where: { id: merchantId },
    });

    if (!merchant) {
      throw new NotFoundException('商户不存在');
    }

    if (!merchant.alipayAuthorized) {
      throw new BadRequestException('商户未授权支付宝支付');
    }

    // 生成订单号
    const outTradeNo = this.generateOrderNo('R');

    // 创建交易记录
    const transaction = this.transactionRepository.create({
      merchantId,
      outTradeNo,
      amount: createRechargeDto.amount,
      type: TransactionType.RECHARGE,
      status: TransactionStatus.PENDING,
      description: createRechargeDto.description || '账户充值',
      notifyUrl: createRechargeDto.notifyUrl,
      returnUrl: createRechargeDto.returnUrl,
    });

    await this.transactionRepository.save(transaction);

    try {
      // 调用支付宝API创建二维码
      const result = await this.alipaySdk.exec('alipay.trade.precreate', {
        bizContent: {
          outTradeNo,
          totalAmount: createRechargeDto.amount,
          subject: transaction.description,
          notifyUrl: createRechargeDto.notifyUrl,
          timeoutExpress: '30m', // 30分钟超时
        },
      });

      if (result.code === '10000') {
        // 更新交易记录
        transaction.thirdPartyTradeNo = result.outTradeNo;
        transaction.qrCode = result.qrCode;
        await this.transactionRepository.save(transaction);

        return {
          transactionId: transaction.id,
          outTradeNo,
          qrCode: result.qrCode,
          amount: createRechargeDto.amount,
          expireTime: new Date(Date.now() + 30 * 60 * 1000), // 30分钟后过期
        };
      } else {
        throw new BadRequestException(`支付宝API调用失败: ${result.msg}`);
      }
    } catch (error) {
      this.logger.error('创建充值订单失败', error);
      // 更新交易状态为失败
      transaction.status = TransactionStatus.FAILED;
      transaction.failureReason = error.message;
      await this.transactionRepository.save(transaction);
      throw new BadRequestException('创建充值订单失败');
    }
  }

  /**
   * 创建代付订单
   */
  async createPayout(merchantId: number, createPayoutDto: CreatePayoutDto) {
    const merchant = await this.merchantRepository.findOne({
      where: { id: merchantId },
    });

    if (!merchant) {
      throw new NotFoundException('商户不存在');
    }

    if (!merchant.alipayAuthorized) {
      throw new BadRequestException('商户未授权支付宝支付');
    }

    // 检查余额
    if (parseFloat(merchant.balance.toString()) < createPayoutDto.amount) {
      throw new BadRequestException('商户余额不足');
    }

    // 生成订单号
    const outTradeNo = this.generateOrderNo('P');

    // 创建交易记录
    const transaction = this.transactionRepository.create({
      merchantId,
      outTradeNo,
      amount: createPayoutDto.amount,
      type: TransactionType.PAYOUT,
      status: TransactionStatus.PENDING,
      description: createPayoutDto.description || '代付转账',
      recipientAccount: createPayoutDto.recipientAccount,
      recipientName: createPayoutDto.recipientName,
    });

    await this.transactionRepository.save(transaction);

    try {
      // 调用支付宝代付API
      const result = await this.alipaySdk.exec('alipay.fund.trans.uni.transfer', {
        bizContent: {
          outBizNo: outTradeNo,
          transAmount: createPayoutDto.amount,
          productCode: 'TRANS_ACCOUNT_NO_PWD',
          bizScene: 'DIRECT_TRANSFER',
          orderTitle: transaction.description,
          payeeInfo: {
            identity: createPayoutDto.recipientAccount,
            identityType: createPayoutDto.accountType || 'ALIPAY_LOGON_ID',
            name: createPayoutDto.recipientName,
          },
          remark: createPayoutDto.description,
        },
      });

      if (result.code === '10000') {
        // 更新交易记录
        transaction.thirdPartyTradeNo = result.orderId;
        transaction.status = TransactionStatus.SUCCESS;
        await this.transactionRepository.save(transaction);

        // 扣减商户余额
        merchant.balance = parseFloat((parseFloat(merchant.balance.toString()) - createPayoutDto.amount).toFixed(2));
        await this.merchantRepository.save(merchant);

        return {
          transactionId: transaction.id,
          outTradeNo,
          thirdPartyTradeNo: result.orderId,
          amount: createPayoutDto.amount,
          status: 'SUCCESS',
        };
      } else {
        throw new BadRequestException(`支付宝代付失败: ${result.msg}`);
      }
    } catch (error) {
      this.logger.error('创建代付订单失败', error);
      // 更新交易状态为失败
      transaction.status = TransactionStatus.FAILED;
      transaction.failureReason = error.message;
      await this.transactionRepository.save(transaction);
      throw new BadRequestException('创建代付订单失败');
    }
  }

  /**
   * 查询交易状态
   */
  async queryTransaction(outTradeNo: string) {
    const transaction = await this.transactionRepository.findOne({
      where: { outTradeNo },
      relations: ['merchant'],
    });

    if (!transaction) {
      throw new NotFoundException('交易不存在');
    }

    try {
      let result;
      if (transaction.type === TransactionType.RECHARGE) {
        // 查询充值订单状态
        result = await this.alipaySdk.exec('alipay.trade.query', {
          bizContent: {
            outTradeNo,
          },
        });
      } else {
        // 查询代付订单状态
        result = await this.alipaySdk.exec('alipay.fund.trans.common.query', {
          bizContent: {
            outBizNo: outTradeNo,
          },
        });
      }

      if (result.code === '10000') {
        // 更新本地交易状态
        await this.updateTransactionStatus(transaction, result);
        
        return {
          transactionId: transaction.id,
          outTradeNo,
          status: transaction.status,
          amount: transaction.amount,
          thirdPartyTradeNo: transaction.thirdPartyTradeNo,
        };
      } else {
        throw new BadRequestException(`查询交易状态失败: ${result.msg}`);
      }
    } catch (error) {
      this.logger.error('查询交易状态失败', error);
      throw new BadRequestException('查询交易状态失败');
    }
  }

  /**
   * 处理支付宝回调通知
   */
  async handleNotify(notifyData: any) {
    try {
      // 验证签名
      const isValid = this.alipaySdk.checkNotifySign(notifyData);
      if (!isValid) {
        this.logger.warn('支付宝回调签名验证失败');
        return false;
      }

      const outTradeNo = notifyData.out_trade_no;
      const transaction = await this.transactionRepository.findOne({
        where: { outTradeNo },
        relations: ['merchant'],
      });

      if (!transaction) {
        this.logger.warn(`回调通知中的订单不存在: ${outTradeNo}`);
        return false;
      }

      // 更新交易状态
      await this.updateTransactionStatus(transaction, notifyData);
      
      return true;
    } catch (error) {
      this.logger.error('处理支付宝回调失败', error);
      return false;
    }
  }

  private async updateTransactionStatus(transaction: Transaction, alipayResult: any) {
    const oldStatus = transaction.status;
    
    if (transaction.type === TransactionType.RECHARGE) {
      // 充值订单状态更新
      switch (alipayResult.trade_status || alipayResult.tradeStatus) {
        case 'TRADE_SUCCESS':
        case 'TRADE_FINISHED':
          transaction.status = TransactionStatus.SUCCESS;
          transaction.thirdPartyTradeNo = alipayResult.trade_no || alipayResult.tradeNo;
          
          // 如果状态从待支付变为成功，增加商户余额
          if (oldStatus === TransactionStatus.PENDING) {
            const merchant = transaction.merchant;
            merchant.balance = parseFloat((parseFloat(merchant.balance.toString()) + transaction.amount).toFixed(2));
            await this.merchantRepository.save(merchant);
          }
          break;
        case 'TRADE_CLOSED':
          transaction.status = TransactionStatus.FAILED;
          transaction.failureReason = '交易关闭';
          break;
      }
    } else {
      // 代付订单状态更新
      switch (alipayResult.status) {
        case 'SUCCESS':
          transaction.status = TransactionStatus.SUCCESS;
          break;
        case 'FAIL':
          transaction.status = TransactionStatus.FAILED;
          transaction.failureReason = alipayResult.failReason || '代付失败';
          break;
      }
    }

    await this.transactionRepository.save(transaction);
  }

  private generateOrderNo(prefix: string): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `${prefix}${timestamp}${random}`;
  }
}
