import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString, IsOptional, Min, IsInt, IsIn } from 'class-validator';

export class CreatePayoutDto {
  @ApiProperty({ description: '代付金额', example: 50.00 })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.01)
  amount: number;

  @ApiProperty({ description: '商户ID（平台管理员专用）', example: 1, required: false })
  @IsInt()
  @IsOptional()
  merchantId?: number;

  @ApiProperty({ description: '收款人账户', example: '<EMAIL>' })
  @IsString()
  recipientAccount: string;

  @ApiProperty({ description: '收款人姓名', example: '张三' })
  @IsString()
  recipientName: string;

  @ApiProperty({ 
    description: '账户类型', 
    example: 'ALIPAY_LOGON_ID',
    enum: ['ALIPAY_LOGON_ID', 'ALIPAY_USER_ID'],
    required: false 
  })
  @IsString()
  @IsIn(['ALIPAY_LOGON_ID', 'ALIPAY_USER_ID'])
  @IsOptional()
  accountType?: string;

  @ApiProperty({ description: '代付描述', example: '提现转账', required: false })
  @IsString()
  @IsOptional()
  description?: string;
}
