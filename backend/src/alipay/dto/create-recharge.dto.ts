import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString, IsOptional, IsUrl, Min, IsInt } from 'class-validator';

export class CreateRechargeDto {
  @ApiProperty({ description: '充值金额', example: 100.00 })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.01)
  amount: number;

  @ApiProperty({ description: '商户ID（平台管理员专用）', example: 1, required: false })
  @IsInt()
  @IsOptional()
  merchantId?: number;

  @ApiProperty({ description: '订单描述', example: '账户充值', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: '异步通知URL', example: 'https://example.com/notify', required: false })
  @IsUrl()
  @IsOptional()
  notifyUrl?: string;

  @ApiProperty({ description: '同步返回URL', example: 'https://example.com/return', required: false })
  @IsUrl()
  @IsOptional()
  returnUrl?: string;
}
