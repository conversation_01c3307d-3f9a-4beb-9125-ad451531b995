import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AlipayService } from './alipay.service';
import { AlipayController } from './alipay.controller';
import { Transaction } from '../entities/transaction.entity';
import { Merchant } from '../entities/merchant.entity';
import { PaymentConfig } from '../entities/payment-config.entity';

@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([Transaction, Merchant, PaymentConfig]),
  ],
  controllers: [AlipayController],
  providers: [AlipayService],
  exports: [AlipayService],
})
export class AlipayModule {}
