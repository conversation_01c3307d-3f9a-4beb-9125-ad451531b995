import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AlipayService } from './alipay.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../entities/user.entity';
import { CreateRechargeDto } from './dto/create-recharge.dto';
import { CreatePayoutDto } from './dto/create-payout.dto';

@ApiTags('支付宝支付')
@Controller('alipay')
export class AlipayController {
  constructor(private readonly alipayService: AlipayService) {}

  @Post('recharge')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.PLATFORM_ADMIN, UserRole.MERCHANT_ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建充值订单' })
  @ApiResponse({ status: 201, description: '充值订单创建成功' })
  async createRecharge(@Request() req, @Body() createRechargeDto: CreateRechargeDto) {
    const merchantId = req.user.role === UserRole.PLATFORM_ADMIN 
      ? createRechargeDto.merchantId 
      : req.user.merchantId;
    
    if (!merchantId) {
      throw new Error('无法确定商户ID');
    }

    return this.alipayService.createRecharge(merchantId, createRechargeDto);
  }

  @Post('payout')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.PLATFORM_ADMIN, UserRole.MERCHANT_ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建代付订单' })
  @ApiResponse({ status: 201, description: '代付订单创建成功' })
  async createPayout(@Request() req, @Body() createPayoutDto: CreatePayoutDto) {
    const merchantId = req.user.role === UserRole.PLATFORM_ADMIN 
      ? createPayoutDto.merchantId 
      : req.user.merchantId;
    
    if (!merchantId) {
      throw new Error('无法确定商户ID');
    }

    return this.alipayService.createPayout(merchantId, createPayoutDto);
  }

  @Get('query/:outTradeNo')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '查询交易状态' })
  @ApiResponse({ status: 200, description: '查询成功' })
  async queryTransaction(@Param('outTradeNo') outTradeNo: string) {
    return this.alipayService.queryTransaction(outTradeNo);
  }

  @Post('notify')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '支付宝异步通知回调' })
  @ApiResponse({ status: 200, description: '处理成功' })
  async handleNotify(@Body() notifyData: any) {
    const success = await this.alipayService.handleNotify(notifyData);
    return success ? 'success' : 'fail';
  }
}
