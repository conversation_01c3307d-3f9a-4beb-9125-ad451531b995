import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Merchant } from './merchant.entity';

export enum TransactionType {
  RECHARGE = 'recharge',
  PAYOUT = 'payout',
}

export enum TransactionStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  SUCCESS = 'success',
  FAILED = 'failed',
  EXPIRED = 'expired',
}

@Entity('transactions')
@Index(['merchantId'])
@Index(['type'])
@Index(['status'])
@Index(['alipayTradeNo'])
@Index(['createdAt'])
export class Transaction {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'merchant_id', comment: '商户ID' })
  merchantId: number;

  @Column({
    type: 'enum',
    enum: TransactionType,
    comment: '交易类型：充值/代付',
  })
  type: TransactionType;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    comment: '交易金额',
  })
  amount: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0.00,
    comment: '手续费',
  })
  fee: number;

  @Column({
    type: 'enum',
    enum: TransactionStatus,
    default: TransactionStatus.PENDING,
    comment: '状态',
  })
  status: TransactionStatus;

  @Column({ name: 'alipay_trade_no', length: 100, nullable: true, comment: '支付宝交易号' })
  alipayTradeNo?: string;

  @Column({ name: 'third_party_trade_no', length: 100, nullable: true, comment: '第三方交易号' })
  thirdPartyTradeNo?: string;

  @Column({ name: 'out_trade_no', length: 100, unique: true, comment: '商户订单号' })
  outTradeNo: string;

  @Column({ name: 'target_account', length: 100, nullable: true, comment: '目标账户（代付专用）' })
  targetAccount?: string;

  @Column({ name: 'target_name', length: 50, nullable: true, comment: '目标账户姓名（代付专用）' })
  targetName?: string;

  @Column({ name: 'recipient_account', length: 100, nullable: true, comment: '收款人账户（代付专用）' })
  recipientAccount?: string;

  @Column({ name: 'recipient_name', length: 50, nullable: true, comment: '收款人姓名（代付专用）' })
  recipientName?: string;

  @Column({ name: 'qr_code_url', length: 500, nullable: true, comment: '二维码URL（充值专用）' })
  qrCodeUrl?: string;

  @Column({ name: 'qr_code', length: 500, nullable: true, comment: '二维码内容（充值专用）' })
  qrCode?: string;

  @Column({ type: 'text', nullable: true, comment: '订单描述' })
  description?: string;

  @Column({ name: 'notify_url', length: 500, nullable: true, comment: '异步通知URL' })
  notifyUrl?: string;

  @Column({ name: 'return_url', length: 500, nullable: true, comment: '同步返回URL' })
  returnUrl?: string;

  @Column({ name: 'failure_reason', type: 'text', nullable: true, comment: '失败原因' })
  failureReason?: string;

  @Column({ name: 'expire_time', nullable: true, comment: '过期时间' })
  expireTime: Date;

  @Column({ type: 'text', nullable: true, comment: '备注' })
  remark?: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联商户
  @ManyToOne(() => Merchant, (merchant) => merchant.transactions)
  @JoinColumn({ name: 'merchant_id' })
  merchant: Merchant;
}
