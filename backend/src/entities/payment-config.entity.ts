import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum PaymentConfigStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

@Entity('payment_configs')
export class PaymentConfig {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 50, comment: '支付提供商' })
  provider: string;

  @Column({ name: 'app_id', length: 100, comment: '支付宝AppID' })
  appId: string;

  @Column({ name: 'private_key', type: 'text', comment: '应用私钥' })
  privateKey: string;

  @Column({ name: 'public_key', type: 'text', comment: '支付宝公钥' })
  publicKey: string;

  @Column({ length: 200, nullable: true, comment: '支付网关地址' })
  gateway?: string;

  @Column({ name: 'is_sandbox', default: true, comment: '是否沙箱环境' })
  isSandbox: boolean;

  @Column({
    name: 'default_fee_rate',
    type: 'decimal',
    precision: 5,
    scale: 4,
    default: 0.0050,
    comment: '默认手续费比例（0.5%）',
  })
  defaultFeeRate: number;

  @Column({ name: 'is_active', default: true, comment: '是否激活' })
  isActive: boolean;

  @Column({
    type: 'enum',
    enum: PaymentConfigStatus,
    default: PaymentConfigStatus.ACTIVE,
    comment: '配置状态',
  })
  status: PaymentConfigStatus;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
