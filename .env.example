# 数据库配置
MYSQL_ROOT_PASSWORD=root123456
MYSQL_DATABASE=alipay_platform
MYSQL_USER=alipay
MYSQL_PASSWORD=alipay123456

# JWT密钥
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# 支付宝配置
ALIPAY_APP_ID=your_alipay_app_id
ALIPAY_PRIVATE_KEY=your_alipay_private_key
ALIPAY_PUBLIC_KEY=your_alipay_public_key
ALIPAY_GATEWAY_URL=https://openapi.alipay.com/gateway.do

# 开发环境支付宝配置（沙箱）
ALIPAY_SANDBOX_APP_ID=your_sandbox_app_id
ALIPAY_SANDBOX_PRIVATE_KEY=your_sandbox_private_key
ALIPAY_SANDBOX_PUBLIC_KEY=your_sandbox_public_key
ALIPAY_SANDBOX_GATEWAY_URL=https://openapi.alipaydev.com/gateway.do

# 应用配置
APP_NAME=支付宝多商户平台
APP_URL=http://localhost
APP_PORT=3000

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 文件上传配置
UPLOAD_PATH=uploads
MAX_FILE_SIZE=10485760

# 邮件配置（可选）
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your_email_password
MAIL_FROM=<EMAIL>

# 短信配置（可选）
SMS_ACCESS_KEY=your_sms_access_key
SMS_SECRET_KEY=your_sms_secret_key
SMS_SIGN_NAME=your_sms_sign_name

# 监控配置（可选）
SENTRY_DSN=your_sentry_dsn
PROMETHEUS_PORT=9090
