version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: alipay_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-root123456}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-alipay_platform}
      MYSQL_USER: ${MYSQL_USER:-alipay}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-alipay123456}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - alipay_network
    command: --default-authentication-plugin=mysql_native_password

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: alipay_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - alipay_network
    command: redis-server --appendonly yes

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: alipay_backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DATABASE_URL: mysql://${MYSQL_USER:-alipay}:${MYSQL_PASSWORD:-alipay123456}@mysql:3306/${MYSQL_DATABASE:-alipay_platform}
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
      ALIPAY_APP_ID: ${ALIPAY_APP_ID}
      ALIPAY_PRIVATE_KEY: ${ALIPAY_PRIVATE_KEY}
      ALIPAY_PUBLIC_KEY: ${ALIPAY_PUBLIC_KEY}
      ALIPAY_GATEWAY_URL: ${ALIPAY_GATEWAY_URL:-https://openapi.alipay.com/gateway.do}
    ports:
      - "3000:3000"
    depends_on:
      - mysql
      - redis
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/uploads:/app/uploads
    networks:
      - alipay_network

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: alipay_frontend
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - alipay_network

  # Nginx负载均衡器（可选）
  nginx:
    image: nginx:alpine
    container_name: alipay_nginx
    restart: unless-stopped
    ports:
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - alipay_network
    profiles:
      - production

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  alipay_network:
    driver: bridge
