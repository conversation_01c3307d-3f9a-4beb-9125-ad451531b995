🛠️ 项目描述：多商户支付宝代付与充值平台系统
📌 项目目标
搭建一个支持多商户的支付宝扫码充值和代付管理后台系统，平台管理员管理商户和全局配置，商户管理员管理自己账户，支持API调用，余额和手续费管理，交易记录查询及导出。

🧱 技术要求
前端：Vue 3 + Vite + Composition API + Element Plus

后端：Node.js + NestJS（支持 Swagger 自动生成接口文档）

数据库：MySQL

包管理：pnpm

部署：生产环境用 Docker（含 Dockerfile 和 docker-compose.yml），开发环境可不使用 Docker

不使用 webhook，状态通过轮询接口查询

无日志审计、安全模块、统计模块、调用限额、功能开关

🗂️ 项目目录结构
根目录下创建 frontend 和 backend 两个目录

frontend 下初始化 Vue3 + Vite 项目

backend 下初始化 NestJS 项目

依赖统一用 pnpm 管理

🎯 核心功能及角色权限
统一登录及权限控制
登录界面与接口统一，用户通过同一入口登录系统

根据登录用户权限（平台管理员或商户管理员）动态渲染对应菜单与按钮

不同权限用户仅能访问和操作自身权限范围内的功能

平台管理员后台菜单及功能
首页

总览全平台余额、充值总额、代付总额

余额不足提醒等运营提示

商户管理

新建/启用/停用商户账号

配置商户 API 权限（开/关）

设置平台默认手续费比例

覆盖单个商户手续费比例

查看商户授权状态

支付设置

配置支付宝 AppID、公钥、私钥

沙箱与正式环境切换

交易管理

查看全平台充值和代付流水

按商户筛选流水

导出流水数据（Excel/CSV）

API 密钥管理

管理平台公钥私钥

查看各商户 API Key 状态

商户管理员后台菜单及功能
首页

查看本商户余额及充值/代付总额

余额不足提醒

充值管理

发起支付宝扫码充值，支持自定义金额

查看充值订单状态（处理中/成功/失败/过期）

充值二维码自动生成与管理

代付管理

发起代付，填写目标支付宝账号

查看代付交易状态（处理中/成功/失败/过期）

显示手续费单独从平台余额扣除提示

流水查询

查看本商户充值和代付流水记录

按时间、状态等筛选

导出流水数据（Excel/CSV）

API 管理（如有权限）

生成、管理 API Key 用于接口调用

📝 交易与手续费逻辑说明
商户账户使用平台虚拟余额

充值成功增加余额

代付金额全额支付给目标支付宝账户

手续费由平台虚拟余额单独扣除，不影响代付金额

手续费比例支持平台统一设置，也支持单商户自定义

余额不足时，提醒充值（包括转账金额+手续费总额）

充值二维码有效期 15 分钟，超时自动过期

所有金额保留两位小数

🔧 数据库连接信息
IP: *************

用户名: df_yuezuhui_cn

密码: 8JN8Dnfi6s85cdzM

请基于以上需求进行项目初始化、权限设计、路由控制、接口开发、前端界面和 Docker 配置。
有任何疑问请随时沟通。



本项目需要调用支付宝开放平台的扫码支付与代付转账接口，请参考以下文档完成参数准备、签名校验和回调处理等集成流程：

- 扫码支付接口（alipay.trade.precreate）
- [代付转账接口](https://opendocs.alipay.com/open-v3/05w7hy)（alipay.fund.trans.uni.transfer）
- 查询支付状态接口（alipay.trade.query）
- 签名机制 使用 RSA2 签名
- 所有接口需传入 app_id、sign、biz_content 等参数，并使用 UTF-8 编码，签名类型为 RSA2。
