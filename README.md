# 🛠️ 项目描述：多商户支付宝代付与充值平台系统

## 📌 项目目标

构建一个后台管理系统，支持平台管理员和多个商户用户使用支付宝进行扫码充值和代付操作，支持余额管理、手续费配置、交易记录查询与导出、API 调用等功能。平台将部署在一台服务器上，通过 Docker 部署，使用 pnpm 管理依赖。

------

## 🧱 技术要求

- 前端：Vue 3 + Vite + Composition API + Element Plus
- 后端：Node.js + NestJS（使用 Swagger 自动生成接口文档）
- 数据库：MySQL（连接信息稍后提供）
- ORM：建议 Prisma（也可 TypeORM）
- 包管理器：pnpm
- 部署：使用 Docker（生成 Dockerfile 和 docker-compose.yml）
- 不使用 webhook，使用轮询方式获取状态
- 不需要日志审计、安全模块、统计模块、调用限额、功能开关

------

## 🧾 系统功能

### 1. 用户权限角色

#### 平台管理员

- 创建/管理多个商户账户
- 为商户开启或关闭 API 使用权限
- 设置统一手续费比例，或为商户设置独立比例
- 管理平台支付宝配置（AppId、公钥、私钥）
- 查看和导出整个平台或单个商户的交易流水

#### 商户管理员（每个商户账号）

- 查看本商户的账户余额和交易流水
- 发起支付宝扫码充值（自定义金额，生成二维码）
- 发起代付操作（指定目标支付宝账号，计算并扣除手续费）
- 如果被授权，可生成并使用 API 密钥调用接口
- 可查看本商户所有充值/转账记录，状态包括：处理中、成功、失败、过期

------

### 2. 交易与余额逻辑

- 所有商户账户都有一个**平台内虚拟余额**
- 充值为支付宝扫码支付，成功后余额增加
- 代付时扣除手续费后，将剩余金额转至指定支付宝账户
- 所有金额保留两位小数
- 手续费按商户独立配置 > 平台默认比例 执行
- 当余额不足时，提示充值

------

### 3. API 功能（开放给被授权商户）

> 商户可在后台生成 API 密钥，用于访问本商户的接口

API 接口包括：

| 功能         | 说明                                  |
| ------------ | ------------------------------------- |
| 创建充值订单 | 返回支付宝二维码链接                  |
| 发起代付转账 | 指定目标支付宝账号                    |
| 查询交易状态 | 通过 biz_id 查询状态（用于轮询）      |
| 查询账户余额 | 查看当前商户余额                      |
| 查询流水记录 | 查询本商户的所有充值/代付记录（分页） |



- 接口需带 API Key + 签名 + 时间戳（建议用 Header 传递）
- 每个订单状态轮询频率建议不超过 1 次/5 秒，最多轮询 1 分钟

------

## 📂 后台菜单结构

### 首页

- 概览数据（余额、交易总额）
- 提示信息（余额不足提醒）

### 商户管理

- 商户创建、启用、停用
- 设置手续费比例
- 设置是否允许使用 API
- 为商户生成密钥

### 支付设置

- 配置 AppID、公钥、私钥
- 支持沙箱 / 正式环境切换

### 交易管理

- 发起扫码充值（自动生成二维码）
- 发起代付转账（填写目标支付宝）
- 展示交易状态（成功 / 失败 / 处理中 / 过期）
- 支持按条件筛选与导出记录

### 数据中心

- 全平台或按商户导出流水数据（Excel/CSV）

------

## 🧩 数据模型（示意）

Augment 可根据下列结构自动生成 schema：

### 商户（Merchant）

```
ts


复制编辑
{
  id,
  name,
  balance,
  api_enabled,
  default_fee_rate,
  custom_fee_rate,
  created_at
}
```

### API 密钥（ApiKey）

```
ts


复制编辑
{
  id,
  merchant_id,
  api_key,
  api_secret,
  enabled,
  created_at
}
```

### 交易记录（Transaction）

```
ts


复制编辑
{
  id,
  merchant_id,
  type: 'recharge' | 'transfer',
  amount,
  fee,
  status: 'PROCESSING' | 'SUCCESS' | 'FAILED' | 'EXPIRED',
  biz_id,
  created_at
}
```

### 收款人白名单（可选 Payee）

```
ts


复制编辑
{
  id,
  merchant_id,
  name,
  alipay_account
}
```

------

## 🛠️ 其他要求

- 所有金额字段必须为保留两位小数的浮点数或整数（建议用字符串储存金额）
- 交易二维码有效期为 15 分钟，超时设为 `EXPIRED`
- 不使用 Webhook，所有状态靠轮询查询
- 所有操作日志可简单 console 输出，不需要日志审计系统
- 所有接口支持跨域（CORS）
- 开发环境无需 Docker，生产环境部署使用 Docker + Compose

------

## ✅ 输入配置说明（稍后提供）

- MySQL 数据库连接配置
- 支付宝开放平台密钥对（AppId、公钥、私钥）

------

如果你需要生成代码，请基于以上说明进行代码初始化、数据库结构生成、路由与权限框架设计、接口创建、API 文档生成、Docker 配置文件创建。前端部分应包含完整的商户与平台管理后台界面。