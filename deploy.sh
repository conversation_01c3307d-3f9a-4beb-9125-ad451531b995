#!/bin/bash

# 支付宝多商户平台部署脚本
# 使用方法: ./deploy.sh [环境] [操作]
# 环境: dev|staging|prod
# 操作: build|start|stop|restart|logs|clean

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 检查环境文件
check_env_file() {
    local env_file=".env"
    
    if [ ! -f "$env_file" ]; then
        log_warning "环境文件 $env_file 不存在，从示例文件创建..."
        cp .env.example "$env_file"
        log_warning "请编辑 $env_file 文件，配置正确的环境变量"
        exit 1
    fi
    
    log_success "环境文件检查通过"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    # 构建后端镜像
    log_info "构建后端镜像..."
    docker-compose build backend
    
    # 构建前端镜像
    log_info "构建前端镜像..."
    docker-compose build frontend
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动数据库和缓存
    log_info "启动数据库和缓存服务..."
    docker-compose up -d mysql redis
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 30
    
    # 运行数据库迁移
    log_info "运行数据库迁移..."
    docker-compose run --rm backend npm run migrate
    
    # 启动应用服务
    log_info "启动应用服务..."
    docker-compose up -d backend frontend
    
    log_success "服务启动完成"
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    docker-compose down
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    stop_services
    start_services
    log_success "服务重启完成"
}

# 查看日志
show_logs() {
    local service=${1:-""}
    
    if [ -n "$service" ]; then
        log_info "查看 $service 服务日志..."
        docker-compose logs -f "$service"
    else
        log_info "查看所有服务日志..."
        docker-compose logs -f
    fi
}

# 清理资源
clean_resources() {
    log_warning "这将删除所有容器、镜像和数据卷，确定要继续吗？(y/N)"
    read -r response
    
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "清理Docker资源..."
        docker-compose down -v --rmi all
        docker system prune -f
        log_success "清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查服务状态
    log_info "检查服务状态..."
    docker-compose ps
    
    # 检查后端API
    log_info "检查后端API..."
    if curl -f http://localhost:3000/health > /dev/null 2>&1; then
        log_success "后端API正常"
    else
        log_error "后端API异常"
    fi
    
    # 检查前端
    log_info "检查前端..."
    if curl -f http://localhost > /dev/null 2>&1; then
        log_success "前端服务正常"
    else
        log_error "前端服务异常"
    fi
}

# 备份数据
backup_data() {
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    
    log_info "创建数据备份到 $backup_dir..."
    mkdir -p "$backup_dir"
    
    # 备份数据库
    log_info "备份数据库..."
    docker-compose exec mysql mysqldump -u root -p"${MYSQL_ROOT_PASSWORD}" "${MYSQL_DATABASE}" > "$backup_dir/database.sql"
    
    # 备份上传文件
    log_info "备份上传文件..."
    docker cp alipay_backend:/app/uploads "$backup_dir/"
    
    log_success "数据备份完成: $backup_dir"
}

# 显示帮助信息
show_help() {
    echo "支付宝多商户平台部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [操作]"
    echo ""
    echo "操作:"
    echo "  build     构建Docker镜像"
    echo "  start     启动所有服务"
    echo "  stop      停止所有服务"
    echo "  restart   重启所有服务"
    echo "  logs      查看服务日志"
    echo "  health    执行健康检查"
    echo "  backup    备份数据"
    echo "  clean     清理所有资源"
    echo "  help      显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 build"
    echo "  $0 start"
    echo "  $0 logs backend"
}

# 主函数
main() {
    local action=${1:-"help"}
    
    case $action in
        "build")
            check_dependencies
            check_env_file
            build_images
            ;;
        "start")
            check_dependencies
            check_env_file
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            check_dependencies
            check_env_file
            restart_services
            ;;
        "logs")
            show_logs "$2"
            ;;
        "health")
            health_check
            ;;
        "backup")
            backup_data
            ;;
        "clean")
            clean_resources
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
