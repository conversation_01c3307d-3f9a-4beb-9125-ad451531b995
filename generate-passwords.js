const bcrypt = require('bcryptjs');

async function generatePasswords() {
  const passwords = [
    { username: 'admin', password: 'admin123' },
    { username: 'merchant1', password: 'merchant123' },
    { username: 'merchant2', password: 'merchant123' }
  ];

  console.log('生成的密码哈希值：\n');
  
  for (const user of passwords) {
    const hash = await bcrypt.hash(user.password, 10);
    console.log(`用户名: ${user.username}`);
    console.log(`明文密码: ${user.password}`);
    console.log(`哈希值: ${hash}`);
    console.log('---');
  }
}

generatePasswords().catch(console.error);
