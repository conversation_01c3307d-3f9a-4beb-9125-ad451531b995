const crypto = require('crypto');

function generateBcryptHash(password, saltRounds = 10) {
  // 简化版bcrypt实现，使用Node.js内置crypto
  const salt = crypto.randomBytes(16).toString('hex');
  const hash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
  return `$2b$${saltRounds}$${salt}${hash}`;
}

function generatePasswords() {
  const passwords = [
    { username: 'admin', password: 'admin123' },
    { username: 'merchant1', password: 'merchant123' },
    { username: 'merchant2', password: 'merchant123' }
  ];

  console.log('生成的密码哈希值：\n');
  console.log('直接复制SQL语句执行：\n');

  for (const user of passwords) {
    const hash = generateBcryptHash(user.password);
    console.log(`-- ${user.username} (密码: ${user.password})`);
    console.log(`UPDATE users SET password = '${hash}' WHERE username = '${user.username}';`);
    console.log('');
  }

  console.log('\n或者手动复制哈希值：\n');
  for (const user of passwords) {
    const hash = generateBcryptHash(user.password);
    console.log(`用户名: ${user.username}`);
    console.log(`明文密码: ${user.password}`);
    console.log(`哈希值: ${hash}`);
    console.log('---');
  }
}

generatePasswords();
