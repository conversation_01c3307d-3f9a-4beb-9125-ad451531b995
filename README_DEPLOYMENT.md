# 支付宝多商户平台部署指南

## 系统要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少 4GB RAM
- 至少 20GB 磁盘空间

## 快速部署

### 1. 克隆项目

```bash
git clone <repository-url>
cd alipay-platform
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件
vim .env
```

**重要配置项：**

- `MYSQL_ROOT_PASSWORD`: MySQL root密码
- `JWT_SECRET`: JWT签名密钥（生产环境必须修改）
- `ALIPAY_APP_ID`: 支付宝应用ID
- `ALIPAY_PRIVATE_KEY`: 支付宝应用私钥
- `ALIPAY_PUBLIC_KEY`: 支付宝公钥

### 3. 部署服务

```bash
# 构建镜像
./deploy.sh build

# 启动服务
./deploy.sh start

# 查看服务状态
./deploy.sh health
```

### 4. 访问应用

- 前端地址: http://localhost
- 后端API: http://localhost:3000
- 数据库: localhost:3306

## 详细部署步骤

### 环境准备

1. **安装Docker**
   ```bash
   # Ubuntu/Debian
   curl -fsSL https://get.docker.com -o get-docker.sh
   sudo sh get-docker.sh
   
   # CentOS/RHEL
   sudo yum install -y docker-ce docker-ce-cli containerd.io
   ```

2. **安装Docker Compose**
   ```bash
   sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose
   ```

### 配置说明

#### 数据库配置
```env
MYSQL_ROOT_PASSWORD=your_secure_password
MYSQL_DATABASE=alipay_platform
MYSQL_USER=alipay
MYSQL_PASSWORD=your_mysql_password
```

#### 支付宝配置
```env
# 生产环境
ALIPAY_APP_ID=your_app_id
ALIPAY_PRIVATE_KEY=your_private_key
ALIPAY_PUBLIC_KEY=alipay_public_key
ALIPAY_GATEWAY_URL=https://openapi.alipay.com/gateway.do

# 沙箱环境
ALIPAY_SANDBOX_APP_ID=your_sandbox_app_id
ALIPAY_SANDBOX_PRIVATE_KEY=your_sandbox_private_key
ALIPAY_SANDBOX_PUBLIC_KEY=alipay_sandbox_public_key
ALIPAY_SANDBOX_GATEWAY_URL=https://openapi.alipaydev.com/gateway.do
```

### 服务管理

#### 启动服务
```bash
# 完整启动
./deploy.sh start

# 单独启动某个服务
docker-compose up -d mysql
docker-compose up -d backend
docker-compose up -d frontend
```

#### 停止服务
```bash
# 停止所有服务
./deploy.sh stop

# 停止单个服务
docker-compose stop backend
```

#### 重启服务
```bash
# 重启所有服务
./deploy.sh restart

# 重启单个服务
docker-compose restart backend
```

#### 查看日志
```bash
# 查看所有服务日志
./deploy.sh logs

# 查看特定服务日志
./deploy.sh logs backend
./deploy.sh logs frontend
```

### 数据管理

#### 数据备份
```bash
# 自动备份
./deploy.sh backup

# 手动备份数据库
docker-compose exec mysql mysqldump -u root -p alipay_platform > backup.sql
```

#### 数据恢复
```bash
# 恢复数据库
docker-compose exec -i mysql mysql -u root -p alipay_platform < backup.sql
```

### 监控和维护

#### 健康检查
```bash
# 执行健康检查
./deploy.sh health

# 查看容器状态
docker-compose ps

# 查看资源使用情况
docker stats
```

#### 日志管理
```bash
# 查看实时日志
docker-compose logs -f backend

# 查看最近100行日志
docker-compose logs --tail=100 backend

# 清理日志
docker system prune -f
```

### 性能优化

#### 数据库优化
```sql
-- 创建索引
CREATE INDEX idx_transactions_merchant_id ON transactions(merchant_id);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_created_at ON transactions(created_at);
```

#### Nginx优化
```nginx
# 启用gzip压缩
gzip on;
gzip_types text/plain text/css application/json application/javascript;

# 设置缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 安全配置

#### SSL证书配置
```bash
# 使用Let's Encrypt
certbot --nginx -d your-domain.com

# 手动配置SSL
# 将证书文件放在 nginx/ssl/ 目录下
# 修改 nginx/nginx.conf 配置
```

#### 防火墙配置
```bash
# 开放必要端口
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 22/tcp

# 限制数据库端口访问
sudo ufw deny 3306/tcp
```

### 故障排除

#### 常见问题

1. **容器启动失败**
   ```bash
   # 查看详细错误信息
   docker-compose logs backend
   
   # 检查端口占用
   netstat -tulpn | grep :3000
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker-compose exec mysql mysql -u root -p -e "SHOW DATABASES;"
   
   # 重置数据库密码
   docker-compose exec mysql mysql -u root -p -e "ALTER USER 'root'@'%' IDENTIFIED BY 'new_password';"
   ```

3. **支付宝接口调用失败**
   - 检查应用ID和密钥配置
   - 验证网络连接
   - 查看支付宝开发者控制台

#### 性能问题

1. **响应慢**
   ```bash
   # 查看资源使用情况
   docker stats
   
   # 增加内存限制
   # 在docker-compose.yml中添加：
   # mem_limit: 2g
   ```

2. **数据库性能**
   ```sql
   -- 查看慢查询
   SHOW VARIABLES LIKE 'slow_query_log';
   
   -- 优化查询
   EXPLAIN SELECT * FROM transactions WHERE merchant_id = 1;
   ```

### 升级指南

#### 应用升级
```bash
# 1. 备份数据
./deploy.sh backup

# 2. 拉取最新代码
git pull origin main

# 3. 重新构建镜像
./deploy.sh build

# 4. 重启服务
./deploy.sh restart
```

#### 数据库迁移
```bash
# 运行数据库迁移
docker-compose run --rm backend npm run migrate

# 回滚迁移（如果需要）
docker-compose run --rm backend npm run migrate:rollback
```

### 生产环境建议

1. **使用外部数据库**：建议使用云数据库服务
2. **配置负载均衡**：使用Nginx或云负载均衡器
3. **启用监控**：集成Prometheus、Grafana等监控工具
4. **定期备份**：设置自动备份策略
5. **安全加固**：定期更新依赖，配置防火墙规则

### 联系支持

如果遇到部署问题，请：

1. 查看日志文件
2. 检查配置文件
3. 参考故障排除指南
4. 联系技术支持团队
